const { getTeamManager } = require('../../database/teamManager');

module.exports = (client) => {
    client.handleTeamSystem = async () => {
        try {
            console.log('🏅 Initializing Team System...');
            
            // Initialize the team manager (this will create the database if it doesn't exist)
            const teamManager = getTeamManager();
            
            // Test database connection
            const teams = teamManager.getAllTeams();
            console.log(`✅ Team System initialized successfully! Found ${teams.length} existing teams.`);
            
            // Set up graceful shutdown
            process.on('SIGINT', () => {
                console.log('🏅 Shutting down Team System...');
                teamManager.close();
                process.exit(0);
            });
            
            process.on('SIGTERM', () => {
                console.log('🏅 Shutting down Team System...');
                teamManager.close();
                process.exit(0);
            });
            
            // Store team manager reference on client for easy access
            client.teamManager = teamManager;
            
            return true;
            
        } catch (error) {
            console.error('❌ Failed to initialize Team System:', error);
            return false;
        }
    };
};
