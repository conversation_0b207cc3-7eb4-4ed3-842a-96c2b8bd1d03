const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const { getTeamManager } = require('../../database/teamManager');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('top-teams')
        .setDescription('Show the top teams by total points'),

    async execute(interaction) {
        try {
            const teamManager = getTeamManager();
            const result = await teamManager.getTopTeams(25); // Get top 25 teams

            if (!result.success || result.teams.length === 0) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FFA500')
                    .setTitle('🏅 Top Teams')
                    .setDescription('No teams found yet!')
                    .addFields({
                        name: '💡 How to Create a Team',
                        value: 'Use the team request system to request team creation from staff!',
                        inline: false
                    })
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed] });
            }

            const teams = result.teams;

            // Pagination setup
            const teamsPerPage = 10;
            const totalPages = Math.ceil(teams.length / teamsPerPage);
            let currentPage = 0;

            const generateEmbed = (page) => {
                const start = page * teamsPerPage;
                const end = start + teamsPerPage;
                const pageTeams = teams.slice(start, end);

                const teamManager = getTeamManager();
                const teamList = pageTeams.map((team, index) => {
                    const globalRank = start + index + 1;
                    const medal = globalRank === 1 ? '🥇' : globalRank === 2 ? '🥈' : globalRank === 3 ? '🥉' : `**${globalRank}.**`;
                    const memberCount = teamManager.getMemberCount(team.id);
                    const avgPoints = memberCount > 0 ? Math.round(team.total_points / memberCount) : 0;
                    
                    return `${medal} **${team.name}**\n` +
                           `   🏆 ${team.total_points} points | ⭐ Level ${team.level} | 👥 ${memberCount} members | 📊 ${avgPoints} avg`;
                }).join('\n\n');

                const embed = new EmbedBuilder()
                    .setColor('#FFD700')
                    .setTitle('🏅 Top Teams Leaderboard')
                    .setDescription(teamList)
                    .setFooter({ text: `Page ${page + 1} of ${totalPages} • ${teams.length} total teams` })
                    .setTimestamp();

                // Add special recognition for top 3 teams
                if (page === 0 && teams.length >= 3) {
                    const topTeam = teams[0];
                    const secondTeam = teams[1];
                    const thirdTeam = teams[2];

                    embed.addFields({
                        name: '🎉 Championship Podium',
                        value: `🥇 **${topTeam.name}** - ${topTeam.total_points} points (Level ${topTeam.level})\n` +
                               `🥈 **${secondTeam.name}** - ${secondTeam.total_points} points (Level ${secondTeam.level})\n` +
                               `🥉 **${thirdTeam.name}** - ${thirdTeam.total_points} points (Level ${thirdTeam.level})`,
                        inline: false
                    });
                }

                return embed;
            };

            const generateButtons = (page) => {
                const row = new ActionRowBuilder();

                if (totalPages > 1) {
                    row.addComponents(
                        new ButtonBuilder()
                            .setCustomId('top_teams_prev')
                            .setLabel('◀️ Previous')
                            .setStyle(ButtonStyle.Secondary)
                            .setDisabled(page === 0),
                        new ButtonBuilder()
                            .setCustomId('top_teams_next')
                            .setLabel('Next ▶️')
                            .setStyle(ButtonStyle.Secondary)
                            .setDisabled(page === totalPages - 1)
                    );
                }

                return row.components.length > 0 ? [row] : [];
            };

            const embed = generateEmbed(currentPage);
            const components = generateButtons(currentPage);

            const response = await interaction.reply({
                embeds: [embed],
                components: components
            });

            if (totalPages > 1) {
                const collector = response.createMessageComponentCollector({
                    time: 300000 // 5 minutes
                });

                collector.on('collect', async (buttonInteraction) => {
                    if (buttonInteraction.user.id !== interaction.user.id) {
                        await buttonInteraction.reply({
                            content: 'Only the command user can navigate pages.',
                            ephemeral: true
                        });
                        return;
                    }

                    if (buttonInteraction.customId === 'top_teams_prev') {
                        currentPage = Math.max(0, currentPage - 1);
                    } else if (buttonInteraction.customId === 'top_teams_next') {
                        currentPage = Math.min(totalPages - 1, currentPage + 1);
                    }

                    const newEmbed = generateEmbed(currentPage);
                    const newComponents = generateButtons(currentPage);

                    await buttonInteraction.update({
                        embeds: [newEmbed],
                        components: newComponents
                    });
                });

                collector.on('end', async () => {
                    try {
                        await response.edit({ components: [] });
                    } catch (error) {
                        // Message might have been deleted
                        console.log('Could not remove buttons from top-teams command');
                    }
                });
            }

        } catch (error) {
            console.error('Error in top-teams command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Command Error')
                .setDescription('An error occurred while retrieving the team leaderboard. Please try again later.')
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
