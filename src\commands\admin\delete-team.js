const { <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, PermissionFlagsBits } = require('discord.js');
const { getTeamManager } = require('../../database/teamManager');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('delete-team')
        .setDescription('Delete a team and its role (Admin only)')
        .addStringOption(option =>
            option.setName('team_name')
                .setDescription('The name of the team to delete')
                .setRequired(true)
                .setAutocomplete(true))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    async autocomplete(interaction) {
        try {
            const focusedValue = interaction.options.getFocused();
            const teamManager = getTeamManager();
            const teams = teamManager.getAllTeams();
            
            const filtered = teams.filter(team => 
                team.name.toLowerCase().includes(focusedValue.toLowerCase())
            ).slice(0, 25); // Discord limits to 25 choices

            await interaction.respond(
                filtered.map(team => ({
                    name: `${team.name} (${team.total_points} points, ${teamManager.getMemberCount(team.id)} members)`,
                    value: team.name
                }))
            );
        } catch (error) {
            console.error('Error in delete-team autocomplete:', error);
            await interaction.respond([]);
        }
    },

    async execute(interaction) {
        try {
            // Check if user has administrator permissions
            if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ Access Denied')
                    .setDescription('You need Administrator permissions to use this command.')
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            const teamName = interaction.options.getString('team_name');
            const teamManager = getTeamManager();

            // Get team info before deletion
            const teamInfo = await teamManager.getTeamInfo(teamName);
            if (!teamInfo.success) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ Team Not Found')
                    .setDescription(`Team "${teamName}" was not found.`)
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            const team = teamInfo.team;

            // Delete the team
            const result = await teamManager.deleteTeam(teamName, interaction.guild);

            if (!result.success) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ Team Deletion Failed')
                    .setDescription(result.message)
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Success response
            const successEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('🗑️ Team Deleted Successfully')
                .setDescription(`Team **"${teamName}"** has been permanently deleted!`)
                .addFields(
                    { name: '👥 Former Members', value: `${team.memberCount}`, inline: true },
                    { name: '🏆 Total Points', value: `${team.total_points}`, inline: true },
                    { name: '⭐ Team Level', value: `${team.level}`, inline: true },
                    { name: '⚠️ Note', value: 'All former members are now without a team and will not earn points until they join a new team.', inline: false }
                )
                .setFooter({ text: `Deleted by ${interaction.user.tag}` })
                .setTimestamp();

            await interaction.reply({ embeds: [successEmbed] });

            // Log the team deletion
            console.log(`🗑️ Team "${teamName}" deleted by ${interaction.user.tag} (${interaction.user.id})`);

        } catch (error) {
            console.error('Error in delete-team command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Command Error')
                .setDescription('An error occurred while deleting the team. Please try again later.')
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
