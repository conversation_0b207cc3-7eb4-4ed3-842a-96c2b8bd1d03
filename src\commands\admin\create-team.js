const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const { getTeamManager } = require('../../database/teamManager');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('انشاء-فريق')
        .setDescription('إنشاء فريق جديد (للإدارة فقط)')
        .addStringOption(option =>
            option.setName('اسم_الفريق')
                .setDescription('اسم الفريق المراد إنشاؤه')
                .setRequired(true)
                .setMaxLength(50))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    async execute(interaction) {
        try {
            // Check if user has administrator permissions
            if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ تم رفض الوصول')
                    .setDescription('تحتاج إلى صلاحيات الإدارة لاستخدام هذا الأمر.')
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            const teamName = interaction.options.getString('اسم_الفريق');
            const teamManager = getTeamManager();

            // Validate team name
            if (teamName.length < 2) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ اسم فريق غير صالح')
                    .setDescription('يجب أن يكون اسم الفريق مكوناً من حرفين على الأقل.')
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Create the team
            const result = await teamManager.createTeam(teamName, interaction.user.id, interaction.guild);

            if (!result.success) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ فشل في إنشاء الفريق')
                    .setDescription(result.message)
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Success response
            const successEmbed = new EmbedBuilder()
                .setColor('#00FF00')
                .setTitle('✅ تم إنشاء الفريق بنجاح')
                .setDescription(`تم إنشاء فريق **"${teamName}"** بنجاح!`)
                .addFields(
                    { name: '🆔 معرف الفريق', value: result.team.id, inline: true },
                    { name: '📅 تاريخ الإنشاء', value: `<t:${result.team.created_at}:F>`, inline: true },
                    { name: '👥 الأعضاء', value: '0', inline: true },
                    { name: '🏆 إجمالي النقاط', value: '0', inline: true },
                    { name: '⭐ المستوى', value: '0', inline: true }
                )
                .setFooter({ text: `تم الإنشاء بواسطة ${interaction.user.tag}` })
                .setTimestamp();

            // Add role information if role was created
            if (result.roleId) {
                const role = interaction.guild.roles.cache.get(result.roleId);
                if (role) {
                    successEmbed.addFields({
                        name: '🎭 رتبة الفريق',
                        value: `${role} (${role.id})`,
                        inline: false
                    });
                }
            }

            await interaction.reply({ embeds: [successEmbed] });

            // Log the team creation
            console.log(`✅ Team "${teamName}" created by ${interaction.user.tag} (${interaction.user.id})`);

        } catch (error) {
            console.error('Error in create-team command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Command Error')
                .setDescription('An error occurred while creating the team. Please try again later.')
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
