const { <PERSON><PERSON><PERSON><PERSON>mandB<PERSON>er, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const { getTeamManager } = require('../../database/teamManager');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('create-team')
        .setDescription('Create a new team (Admin only)')
        .addStringOption(option =>
            option.setName('team_name')
                .setDescription('The name of the team to create')
                .setRequired(true)
                .setMaxLength(50))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    async execute(interaction) {
        try {
            // Check if user has administrator permissions
            if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ Access Denied')
                    .setDescription('You need Administrator permissions to use this command.')
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            const teamName = interaction.options.getString('team_name');
            const teamManager = getTeamManager();

            // Validate team name
            if (teamName.length < 2) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ Invalid Team Name')
                    .setDescription('Team name must be at least 2 characters long.')
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Create the team
            const result = await teamManager.createTeam(teamName, interaction.user.id, interaction.guild);

            if (!result.success) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ Team Creation Failed')
                    .setDescription(result.message)
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Success response
            const successEmbed = new EmbedBuilder()
                .setColor('#00FF00')
                .setTitle('✅ Team Created Successfully')
                .setDescription(`Team **"${teamName}"** has been created!`)
                .addFields(
                    { name: '🆔 Team ID', value: result.team.id, inline: true },
                    { name: '📅 Created', value: `<t:${result.team.created_at}:F>`, inline: true },
                    { name: '👥 Members', value: '0', inline: true },
                    { name: '🏆 Total Points', value: '0', inline: true },
                    { name: '⭐ Level', value: '0', inline: true }
                )
                .setFooter({ text: `Created by ${interaction.user.tag}` })
                .setTimestamp();

            // Add role information if role was created
            if (result.roleId) {
                const role = interaction.guild.roles.cache.get(result.roleId);
                if (role) {
                    successEmbed.addFields({
                        name: '🎭 Team Role',
                        value: `${role} (${role.id})`,
                        inline: false
                    });
                }
            }

            await interaction.reply({ embeds: [successEmbed] });

            // Log the team creation
            console.log(`✅ Team "${teamName}" created by ${interaction.user.tag} (${interaction.user.id})`);

        } catch (error) {
            console.error('Error in create-team command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Command Error')
                .setDescription('An error occurred while creating the team. Please try again later.')
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
