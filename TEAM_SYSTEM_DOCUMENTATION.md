# 🏅 Team System Documentation

## Overview
The Team System is a comprehensive Discord bot feature that allows servers to create competitive teams where members earn points through various activities. Teams compete on leaderboards and members can level up based on their contributions.

## 🎯 Key Features

### ✅ **Team Management (Admin Only)**
- Create teams with unique names
- Add/remove members from teams
- View detailed team and member information
- All team actions handled through staff via ticket system

### 🎮 **Point System**
- **Text Chat Activity**: 1 point per valuable message (anti-spam protected)
- **Voice Activity**: 1 point per 5 minutes in voice channels
- **Daily Rewards**: 10-50 random points once per 24 hours
- **Daily Chat Limit**: Maximum 100 points from chat per day

### 📊 **Leveling System**
- **Member Level**: Points ÷ 100 (e.g., 500 points = Level 5)
- **Team Level**: Total Team Points ÷ 500 (e.g., 2500 points = Level 5)
- Automatic level calculation with database triggers

### 🎟️ **Ticket-Based Requests**
- Users request team actions through buttons
- Creates dedicated ticket channels for staff
- Three request types: Join Team, Leave Team, Create Team

### 🏆 **Leaderboards**
- Top members by points (with pagination)
- Top teams by total points (with pagination)
- Real-time rankings and statistics

## 📋 Database Structure

### Members Table
```sql
- id (TEXT PRIMARY KEY) - Discord user ID
- team_id (TEXT) - Team ID they belong to
- points (INTEGER) - Total points earned
- level (INTEGER) - Current level (auto-calculated)
- last_daily (INTEGER) - Last daily claim timestamp
- voice_time (INTEGER) - Total voice time in seconds
- created_at (INTEGER) - Account creation timestamp
- updated_at (INTEGER) - Last update timestamp
```

### Teams Table
```sql
- id (TEXT PRIMARY KEY) - Unique team ID
- name (TEXT UNIQUE) - Team name
- created_at (INTEGER) - Team creation timestamp
- total_points (INTEGER) - Sum of all member points
- level (INTEGER) - Team level (auto-calculated)
```

## 🔧 Setup Instructions

### 1. Install Dependencies
The system requires `better-sqlite3` which should already be installed:
```bash
npm install better-sqlite3
```

### 2. Initialize Team System
The team system initializes automatically when the bot starts. The database file will be created at `src/data/teams.db`.

### 3. Setup Team Request Channel
Use the admin command to setup the team request system:
```
/setup-team-requests channel:#your-channel
```

This creates a message with buttons for users to request team actions.

### 4. Configure Staff Roles
Edit `src/events/interactions/teamRequestHandler.js` and update the staff role names:
```javascript
const TEAM_REQUEST_CONFIG = {
    categoryName: 'Team Requests',
    staffRoleNames: ['Admin', 'Moderator', 'Staff'], // Update these
    logChannelName: 'team-logs' // Optional logging channel
};
```

## 📝 Admin Commands

### `/create-team <team_name>`
- **Permission**: Administrator
- **Description**: Create a new team
- **Example**: `/create-team team_name:Warriors`

### `/add-to-team <member> <team_name>`
- **Permission**: Administrator  
- **Description**: Add a member to a team
- **Example**: `/add-to-team member:@user team_name:Warriors`
- **Features**: Autocomplete for team names

### `/remove-from-team <member>`
- **Permission**: Administrator
- **Description**: Remove a member from their current team
- **Example**: `/remove-from-team member:@user`

### `/team-info <subcommand>`
- **Permission**: Administrator
- **Subcommands**: 
  - `team <team_name>` - Get team information
  - `member <user>` - Get member information
- **Examples**: 
  - `/team-info team team_name:Warriors`
  - `/team-info member user:@user`

### `/team-members <team_name>`
- **Permission**: Administrator
- **Description**: Show all members in a team (paginated)
- **Example**: `/team-members team_name:Warriors`

### `/setup-team-requests <channel>`
- **Permission**: Administrator
- **Description**: Setup team request system in a channel
- **Example**: `/setup-team-requests channel:#team-requests`

## 👥 User Commands

### `/daily`
- **Description**: Claim daily points reward (10-50 points)
- **Cooldown**: 24 hours
- **Requirement**: Must be in a team

### `/points [user]`
- **Description**: Show your or another user's points and level
- **Example**: `/points` or `/points user:@someone`
- **Features**: Progress bar, team info, daily reminder

### `/team-points [team_name]`
- **Description**: Show your team's or a specific team's points
- **Example**: `/team-points` or `/team-points team_name:Warriors`
- **Features**: Team progress, top members, user position

### `/top`
- **Description**: Show top members leaderboard
- **Features**: Pagination, medals for top 3, hall of fame

### `/top-teams`
- **Description**: Show top teams leaderboard  
- **Features**: Pagination, championship podium, team stats

## 🎯 Point Earning Rules

### Text Chat Activity
- **Requirement**: Must be in a team
- **Rate**: 1 point per valuable message
- **Minimum Length**: 3 characters
- **Anti-Spam**: Max 5 messages per minute
- **Daily Limit**: 100 points from chat per day
- **Excluded**: Commands starting with `!` or `/`

### Voice Activity
- **Requirement**: Must be in a team
- **Rate**: 1 point per 5 minutes (300 seconds)
- **Conditions**: 
  - Not muted (server or self mute)
  - Not alone in voice channel
  - Continuous tracking with periodic updates

### Daily Rewards
- **Requirement**: Must be in a team
- **Amount**: Random 10-50 points
- **Cooldown**: 24 hours from last claim
- **Command**: `/daily`

## 🎟️ Team Request System

### User Flow
1. User clicks button in team request channel
2. Ticket channel is created with staff permissions
3. User explains their request
4. Staff uses admin commands to handle request
5. Staff closes ticket when complete

### Request Types
- **📥 Join Team**: Request to join an existing team
- **📤 Leave Team**: Request to leave current team  
- **🆕 Create Team**: Request to create a new team

### Staff Instructions
Each ticket includes instructions for staff:
- `/create-team team_name:<name>` - Create new team
- `/add-to-team member:@user team_name:<name>` - Add to team
- `/remove-from-team member:@user` - Remove from team
- `/team-info member:@user` - Check member info

## 🔍 Monitoring & Logging

### Console Logging
- Team creation/deletion events
- Member additions/removals
- Point milestones (every 100 points)
- Voice activity sessions
- Daily reward claims

### Optional Team Logs Channel
Create a channel named `team-logs` for automatic logging of:
- Ticket creation/closure
- Team management actions
- System events

## ⚠️ Important Notes

### Database Management
- Database uses SQLite with WAL mode for performance
- Automatic triggers update team totals when member points change
- Graceful shutdown handling to prevent data corruption

### Performance Considerations
- Voice activity tracked with 1-minute intervals
- Message spam protection with sliding window
- Periodic cleanup of tracking data
- Database indexes for optimal query performance

### Security Features
- Admin commands require Administrator permission
- Anti-spam protection for point earning
- Daily limits prevent abuse
- Ticket system prevents unauthorized team changes

## 🚀 Getting Started

1. **Setup the system**: Bot will auto-initialize on startup
2. **Create team request channel**: Use `/setup-team-requests`
3. **Create your first team**: Use `/create-team`
4. **Add members**: Use `/add-to-team` or let users request via tickets
5. **Monitor activity**: Use `/top` and `/top-teams` to see progress

The team system is now ready for your community to start competing and earning points!
