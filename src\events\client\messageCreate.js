const { Events } = require('discord.js');
const { getTeamManager } = require('../../database/teamManager');

// Anti-spam tracking
const userMessageTracker = new Map();
const SPAM_THRESHOLD = 5; // Max messages per minute
const SPAM_WINDOW = 60000; // 1 minute in milliseconds
const MIN_MESSAGE_LENGTH = 3; // Minimum characters for valuable message
const DAILY_CHAT_POINT_LIMIT = 100; // Maximum points from chat per day

module.exports = {
    name: Events.MessageCreate,
    async execute(message) {
        if (message.author.bot) return; // Ignore bot messages

        // Handle team points for text activity
        await handleTextActivity(message);

        ; // Add this to debug

        // Original Arabic greeting
        if (message.content.toLowerCase() === 'السلام عليكم') {
            message.reply('وعليكم السلام ورحمة الله وبركاته');
        };
          if (message.content.toLowerCase() === 'مساء الخير') {
            message.reply('مساء الورد');
        };
        if (message.content.toLowerCase() === 'صباح الخير') {
            message.reply('صباح الورد');
        }

        // Commands that start with !
        if (message.content.startsWith('!')) {
            const command = message.content.toLowerCase();

            switch (command) {

                case '!مساء الخير':
                    message.reply('مساء الورد');
                    break;
                    
                case '!صباح الخير':
                    message.reply('صباح الورد');
                    break;
                    
                case '!دعم':
                    message.reply('https://tip.dokan.sa/sahm');
                    break;

                case '!x':
                    message.reply('https://x.com/4e56');
                    break;

                case '!اكس':
                    message.reply('https://x.com/4e56');
                    break;

                case '!تويتر':
                    message.reply('https://x.com/4e56');
                    break;

                case '!كيك':
                    message.reply('https://kick.com/ssahm');
                    break;

                case '!kick':
                    message.reply('https://kick.com/ssahm');
                    break;

                case '!حسابات':
                    message.reply('https://guns.lol/.sahm');
                    break;

                case '!ص':
                    message.reply('اللهم صل وسلم على سيدنا محمد');
                    break;

                    
                case '!كفاره':
                    message.reply('سُبْحَانَكَ اللّهُمَّ وَبِحَمْدِكَ، أَشْهَدُ أَنْ لا إِلَهَ إِلاَّ أَنْتَ، أَسْتَغْفِرُكَ وَأَتُوبُ إِلَيْكَ.');
                    break;  

                case '!كفارة':
                    message.reply('سُبْحَانَكَ اللّهُمَّ وَبِحَمْدِكَ، أَشْهَدُ أَنْ لا إِلَهَ إِلاَّ أَنْتَ، أَسْتَغْفِرُكَ وَأَتُوبُ إِلَيْكَ.');
                    break;  
                                       
                case '!احبك':
                    message.reply('ماحبيت الا اللي يحبك ويغليك');
                    break;  
                    
                        
                case '!س':
                    message.reply('سبحان الله وبحمده سبحان الله العظيم');
                    break;
                    
                case '!صلاه':
                    message.channel.send(' الصلاة يا عباد الله، اللي ما صلى يصلي');
                    break;

                case '!صلاة':
                    message.channel.send(' الصلاة يا عباد الله، اللي ما صلى يصلي');
                    break;
            }
        }
    }
};

async function handleTextActivity(message) {
    try {
        const userId = message.author.id;
        const now = Date.now();

        // Skip if message is too short or is a command
        if (message.content.length < MIN_MESSAGE_LENGTH || message.content.startsWith('!') || message.content.startsWith('/')) {
            return;
        }

        // Anti-spam check
        if (!isValidMessage(userId, now)) {
            return;
        }

        // Check if user has reached daily chat point limit
        if (await hasReachedDailyChatLimit(userId)) {
            return;
        }

        // Award points for text activity
        const teamManager = getTeamManager();
        const result = await teamManager.addPointsToMember(userId, 1, 'Text Chat');

        if (result.success) {
            // Track daily chat points
            await trackDailyChatPoints(userId);

            // Optional: Log significant milestones
            const member = result.member;
            if (member.points % 100 === 0 && member.points > 0) {
                console.log(`🎉 ${message.author.tag} reached ${member.points} points! (Level ${member.level})`);
            }
        }

    } catch (error) {
        console.error('Error handling text activity:', error);
    }
}

function isValidMessage(userId, timestamp) {
    try {
        const userTracker = userMessageTracker.get(userId) || { messages: [], lastReset: timestamp };

        // Clean old messages (older than spam window)
        userTracker.messages = userTracker.messages.filter(msgTime => timestamp - msgTime < SPAM_WINDOW);

        // Check if user is spamming
        if (userTracker.messages.length >= SPAM_THRESHOLD) {
            return false;
        }

        // Add current message timestamp
        userTracker.messages.push(timestamp);
        userMessageTracker.set(userId, userTracker);

        return true;

    } catch (error) {
        console.error('Error checking message validity:', error);
        return false;
    }
}

async function hasReachedDailyChatLimit(userId) {
    try {
        const today = new Date().toDateString();
        const userDailyData = dailyChatTracker.get(userId);

        if (!userDailyData || userDailyData.date !== today) {
            return false;
        }

        return userDailyData.points >= DAILY_CHAT_POINT_LIMIT;

    } catch (error) {
        console.error('Error checking daily chat limit:', error);
        return false;
    }
}

async function trackDailyChatPoints(userId) {
    try {
        const today = new Date().toDateString();
        const userDailyData = dailyChatTracker.get(userId) || { date: today, points: 0 };

        // Reset if it's a new day
        if (userDailyData.date !== today) {
            userDailyData.date = today;
            userDailyData.points = 0;
        }

        userDailyData.points += 1;
        dailyChatTracker.set(userId, userDailyData);

    } catch (error) {
        console.error('Error tracking daily chat points:', error);
    }
}

// Daily chat point tracking
const dailyChatTracker = new Map();

// Clean up tracking data periodically
setInterval(() => {
    try {
        const now = Date.now();
        const oneDayAgo = now - 24 * 60 * 60 * 1000;

        // Clean up old message tracking data
        for (const [userId, tracker] of userMessageTracker.entries()) {
            tracker.messages = tracker.messages.filter(msgTime => now - msgTime < SPAM_WINDOW);
            if (tracker.messages.length === 0) {
                userMessageTracker.delete(userId);
            }
        }

        // Clean up old daily chat tracking data
        const today = new Date().toDateString();
        for (const [userId, dailyData] of dailyChatTracker.entries()) {
            if (dailyData.date !== today) {
                dailyChatTracker.delete(userId);
            }
        }

    } catch (error) {
        console.error('Error cleaning up tracking data:', error);
    }
}, 300000); // Clean up every 5 minutes
