const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const { getTeamManager } = require('../../database/teamManager');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('top')
        .setDescription('Show the top members by points'),

    async execute(interaction) {
        try {
            const teamManager = getTeamManager();
            const result = await teamManager.getTopMembers(50); // Get top 50 for pagination

            if (!result.success || result.members.length === 0) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FFA500')
                    .setTitle('📊 Top Members')
                    .setDescription('No members found in the leaderboard yet!')
                    .addFields({
                        name: '💡 How to Get on the Leaderboard',
                        value: 'Join a team and start earning points through:\n• 🗣️ Chatting in text channels\n• 🎤 Participating in voice channels\n• 🎁 Claiming daily rewards',
                        inline: false
                    })
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed] });
            }

            const members = result.members;

            // Pagination setup
            const membersPerPage = 10;
            const totalPages = Math.ceil(members.length / membersPerPage);
            let currentPage = 0;

            const generateEmbed = (page) => {
                const start = page * membersPerPage;
                const end = start + membersPerPage;
                const pageMembers = members.slice(start, end);

                const memberList = pageMembers.map((member, index) => {
                    const globalRank = start + index + 1;
                    const medal = globalRank === 1 ? '🥇' : globalRank === 2 ? '🥈' : globalRank === 3 ? '🥉' : `**${globalRank}.**`;
                    return `${medal} <@${member.id}>\n` +
                           `   🏆 ${member.points} points | ⭐ Level ${member.level} | 🏅 ${member.team_name}`;
                }).join('\n\n');

                const embed = new EmbedBuilder()
                    .setColor('#FFD700')
                    .setTitle('🏆 Top Members Leaderboard')
                    .setDescription(memberList)
                    .setFooter({ text: `Page ${page + 1} of ${totalPages} • ${members.length} total members` })
                    .setTimestamp();

                // Add special recognition for top 3
                if (page === 0 && members.length >= 3) {
                    embed.addFields({
                        name: '🎉 Hall of Fame',
                        value: `🥇 **Champion:** <@${members[0].id}> (${members[0].points} points)\n` +
                               `🥈 **Runner-up:** <@${members[1].id}> (${members[1].points} points)\n` +
                               `🥉 **Third Place:** <@${members[2].id}> (${members[2].points} points)`,
                        inline: false
                    });
                }

                return embed;
            };

            const generateButtons = (page) => {
                const row = new ActionRowBuilder();

                if (totalPages > 1) {
                    row.addComponents(
                        new ButtonBuilder()
                            .setCustomId('top_members_prev')
                            .setLabel('◀️ Previous')
                            .setStyle(ButtonStyle.Secondary)
                            .setDisabled(page === 0),
                        new ButtonBuilder()
                            .setCustomId('top_members_next')
                            .setLabel('Next ▶️')
                            .setStyle(ButtonStyle.Secondary)
                            .setDisabled(page === totalPages - 1)
                    );
                }

                return row.components.length > 0 ? [row] : [];
            };

            const embed = generateEmbed(currentPage);
            const components = generateButtons(currentPage);

            const response = await interaction.reply({
                embeds: [embed],
                components: components
            });

            if (totalPages > 1) {
                const collector = response.createMessageComponentCollector({
                    time: 300000 // 5 minutes
                });

                collector.on('collect', async (buttonInteraction) => {
                    if (buttonInteraction.user.id !== interaction.user.id) {
                        await buttonInteraction.reply({
                            content: 'Only the command user can navigate pages.',
                            ephemeral: true
                        });
                        return;
                    }

                    if (buttonInteraction.customId === 'top_members_prev') {
                        currentPage = Math.max(0, currentPage - 1);
                    } else if (buttonInteraction.customId === 'top_members_next') {
                        currentPage = Math.min(totalPages - 1, currentPage + 1);
                    }

                    const newEmbed = generateEmbed(currentPage);
                    const newComponents = generateButtons(currentPage);

                    await buttonInteraction.update({
                        embeds: [newEmbed],
                        components: newComponents
                    });
                });

                collector.on('end', async () => {
                    try {
                        await response.edit({ components: [] });
                    } catch (error) {
                        // Message might have been deleted
                        console.log('Could not remove buttons from top command');
                    }
                });
            }

        } catch (error) {
            console.error('Error in top command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Command Error')
                .setDescription('An error occurred while retrieving the leaderboard. Please try again later.')
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
