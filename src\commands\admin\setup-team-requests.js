const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, PermissionFlagsBits, ChannelType } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup-team-requests')
        .setDescription('Setup the team request system in a channel (Admin only)')
        .addChannelOption(option =>
            option.setName('channel')
                .setDescription('The channel to setup team requests in')
                .setRequired(true)
                .addChannelTypes(ChannelType.GuildText))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    async execute(interaction) {
        try {
            // Check if user has administrator permissions
            if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ Access Denied')
                    .setDescription('You need Administrator permissions to use this command.')
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            const channel = interaction.options.getChannel('channel');

            // Check if bot has permissions to send messages in the channel
            const botMember = interaction.guild.members.cache.get(interaction.client.user.id);
            const permissions = channel.permissionsFor(botMember);

            if (!permissions.has(['SendMessages', 'EmbedLinks', 'UseExternalEmojis'])) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ Missing Permissions')
                    .setDescription(`I don't have the required permissions in ${channel}.\n\nRequired permissions:\n• Send Messages\n• Embed Links\n• Use External Emojis`)
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Create the team request embed
            const teamRequestEmbed = new EmbedBuilder()
                .setColor('#0099FF')
                .setTitle('🏅 نظام الفرق - مركز الطلبات')
                .setDescription(`مرحباً بك في **نظام الفرق**! 🎉\n\n` +
                    `**كيف يعمل النظام:**\n` +
                    `• الفرق تتنافس من خلال كسب النقاط عبر الأنشطة\n` +
                    `• الأعضاء يكسبون النقاط من الدردشة والصوت والمكافآت اليومية\n` +
                    `• فقط أعضاء الفرق يمكنهم كسب النقاط - انضم لفريق لتبدأ!\n\n` +
                    `**نظام النقاط:**\n` +
                    `🗣️ **الدردشة النصية:** نقطة واحدة لكل رسالة مفيدة\n` +
                    `🎤 **النشاط الصوتي:** نقطة واحدة لكل 5 دقائق في الصوت\n` +
                    `🎁 **المكافأة اليومية:** 10-50 نقطة مرة واحدة يومياً\n\n` +
                    `**نظام المستويات:**\n` +
                    `👤 **مستوى العضو:** النقاط ÷ 100\n` +
                    `🏅 **مستوى الفريق:** إجمالي النقاط ÷ 500\n\n` +
                    `**⚠️ مهم:** جميع إجراءات الفرق (الانضمام، المغادرة، الإنشاء) تتم عبر الإدارة من خلال التذاكر.`)
                .addFields(
                    {
                        name: '📥 الانضمام لفريق',
                        value: 'طلب الانضمام لفريق موجود',
                        inline: true
                    },
                    {
                        name: '📤 مغادرة الفريق',
                        value: 'طلب مغادرة فريقك الحالي',
                        inline: true
                    },
                    {
                        name: '🆕 إنشاء فريق',
                        value: 'طلب إنشاء فريق جديد',
                        inline: true
                    }
                )
                .setFooter({ text: 'اضغط على أحد الأزرار أدناه لفتح تذكرة مع طلبك' })
                .setTimestamp();

            // Create buttons
            const buttonRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('team_request_join')
                        .setLabel('📥 طلب الانضمام لفريق')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('team_request_leave')
                        .setLabel('📤 طلب مغادرة الفريق')
                        .setStyle(ButtonStyle.Secondary),
                    new ButtonBuilder()
                        .setCustomId('team_request_create')
                        .setLabel('🆕 طلب إنشاء فريق')
                        .setStyle(ButtonStyle.Success)
                );

            // Send the message to the specified channel
            await channel.send({
                embeds: [teamRequestEmbed],
                components: [buttonRow]
            });

            // Confirmation response
            const successEmbed = new EmbedBuilder()
                .setColor('#00FF00')
                .setTitle('✅ Team Request System Setup Complete')
                .setDescription(`Team request system has been successfully setup in ${channel}!`)
                .addFields(
                    { name: '📍 Channel', value: `${channel}`, inline: true },
                    { name: '🎯 Features', value: 'Join Team, Leave Team, Create Team', inline: true },
                    { name: '🔧 Next Steps', value: 'Users can now click buttons to create tickets for team requests', inline: false }
                )
                .setFooter({ text: `Setup by ${interaction.user.tag}` })
                .setTimestamp();

            await interaction.reply({ embeds: [successEmbed], ephemeral: true });

            console.log(`✅ Team request system setup in ${channel.name} (${channel.id}) by ${interaction.user.tag}`);

        } catch (error) {
            console.error('Error in setup-team-requests command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Setup Failed')
                .setDescription('An error occurred while setting up the team request system. Please try again later.')
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
