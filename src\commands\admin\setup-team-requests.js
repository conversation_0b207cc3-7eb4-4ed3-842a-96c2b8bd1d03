const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, PermissionFlagsBits, ChannelType } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup-team-requests')
        .setDescription('Setup the team request system in a channel (Admin only)')
        .addChannelOption(option =>
            option.setName('channel')
                .setDescription('The channel to setup team requests in')
                .setRequired(true)
                .addChannelTypes(ChannelType.GuildText))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    async execute(interaction) {
        try {
            // Check if user has administrator permissions
            if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ Access Denied')
                    .setDescription('You need Administrator permissions to use this command.')
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            const channel = interaction.options.getChannel('channel');

            // Check if bot has permissions to send messages in the channel
            const botMember = interaction.guild.members.cache.get(interaction.client.user.id);
            const permissions = channel.permissionsFor(botMember);

            if (!permissions.has(['SendMessages', 'EmbedLinks', 'UseExternalEmojis'])) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ Missing Permissions')
                    .setDescription(`I don't have the required permissions in ${channel}.\n\nRequired permissions:\n• Send Messages\n• Embed Links\n• Use External Emojis`)
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Create the team request embed
            const teamRequestEmbed = new EmbedBuilder()
                .setColor('#0099FF')
                .setTitle('🏅 Team System - Request Center')
                .setDescription(`Welcome to the **Team System**! 🎉\n\n` +
                    `**How it works:**\n` +
                    `• Teams compete by earning points through activity\n` +
                    `• Members earn points from chatting, voice activity, and daily rewards\n` +
                    `• Only team members can earn points - join a team to start!\n\n` +
                    `**Point System:**\n` +
                    `🗣️ **Text Chat:** 1 point per valuable message\n` +
                    `🎤 **Voice Activity:** 1 point per 5 minutes in voice\n` +
                    `🎁 **Daily Reward:** 10-50 points once per day\n\n` +
                    `**Leveling:**\n` +
                    `👤 **Member Level:** Points ÷ 100\n` +
                    `🏅 **Team Level:** Total Points ÷ 500\n\n` +
                    `**⚠️ Important:** All team actions (join, leave, create) must be handled by staff through tickets.`)
                .addFields(
                    { 
                        name: '📥 Join a Team', 
                        value: 'Request to join an existing team', 
                        inline: true 
                    },
                    { 
                        name: '📤 Leave Team', 
                        value: 'Request to leave your current team', 
                        inline: true 
                    },
                    { 
                        name: '🆕 Create Team', 
                        value: 'Request to create a new team', 
                        inline: true 
                    }
                )
                .setFooter({ text: 'Click a button below to open a ticket with your request' })
                .setTimestamp();

            // Create buttons
            const buttonRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('team_request_join')
                        .setLabel('📥 Request to Join Team')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('team_request_leave')
                        .setLabel('📤 Request to Leave Team')
                        .setStyle(ButtonStyle.Secondary),
                    new ButtonBuilder()
                        .setCustomId('team_request_create')
                        .setLabel('🆕 Request to Create Team')
                        .setStyle(ButtonStyle.Success)
                );

            // Send the message to the specified channel
            await channel.send({
                embeds: [teamRequestEmbed],
                components: [buttonRow]
            });

            // Confirmation response
            const successEmbed = new EmbedBuilder()
                .setColor('#00FF00')
                .setTitle('✅ Team Request System Setup Complete')
                .setDescription(`Team request system has been successfully setup in ${channel}!`)
                .addFields(
                    { name: '📍 Channel', value: `${channel}`, inline: true },
                    { name: '🎯 Features', value: 'Join Team, Leave Team, Create Team', inline: true },
                    { name: '🔧 Next Steps', value: 'Users can now click buttons to create tickets for team requests', inline: false }
                )
                .setFooter({ text: `Setup by ${interaction.user.tag}` })
                .setTimestamp();

            await interaction.reply({ embeds: [successEmbed], ephemeral: true });

            console.log(`✅ Team request system setup in ${channel.name} (${channel.id}) by ${interaction.user.tag}`);

        } catch (error) {
            console.error('Error in setup-team-requests command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Setup Failed')
                .setDescription('An error occurred while setting up the team request system. Please try again later.')
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
