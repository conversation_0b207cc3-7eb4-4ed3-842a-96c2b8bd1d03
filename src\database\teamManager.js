const TeamDatabase = require('./database');

class TeamManager {
    constructor() {
        this.db = new TeamDatabase();
    }

    // Member management methods
    async getMemberInfo(userId) {
        try {
            let member = this.db.getMember(userId);
            if (!member) {
                this.db.createMember(userId);
                member = this.db.getMember(userId);
            }
            
            // Get team info if member is in a team
            if (member.team_id) {
                const team = this.db.getTeam(member.team_id);
                member.team_name = team ? team.name : null;
            }
            
            return member;
        } catch (error) {
            console.error('Error getting member info:', error);
            throw error;
        }
    }

    async addPointsToMember(userId, points, reason = 'Activity') {
        try {
            const member = await this.getMemberInfo(userId);
            
            // Only award points if member is in a team
            if (!member.team_id) {
                return { success: false, message: 'Member must be in a team to earn points' };
            }

            this.db.updateMemberPoints(userId, points);
            
            const updatedMember = await this.getMemberInfo(userId);
            return {
                success: true,
                member: updatedMember,
                pointsAdded: points,
                reason: reason
            };
        } catch (error) {
            console.error('Error adding points to member:', error);
            throw error;
        }
    }

    async claimDailyReward(userId) {
        try {
            const member = await this.getMemberInfo(userId);
            
            if (!member.team_id) {
                return { success: false, message: 'You must be in a team to claim daily rewards!' };
            }

            const now = Date.now();
            const lastDaily = member.last_daily * 1000; // Convert to milliseconds
            const timeDiff = now - lastDaily;
            const oneDay = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

            if (timeDiff < oneDay) {
                const remainingTime = oneDay - timeDiff;
                const hours = Math.floor(remainingTime / (60 * 60 * 1000));
                const minutes = Math.floor((remainingTime % (60 * 60 * 1000)) / (60 * 1000));
                
                return {
                    success: false,
                    message: `You can claim your daily reward in ${hours}h ${minutes}m`
                };
            }

            // Generate random points between 10-50
            const dailyPoints = Math.floor(Math.random() * 41) + 10;
            
            // Update last daily timestamp
            this.db.updateLastDaily(userId, Math.floor(now / 1000));
            
            // Add points
            await this.addPointsToMember(userId, dailyPoints, 'Daily Reward');
            
            return {
                success: true,
                points: dailyPoints,
                message: `You claimed ${dailyPoints} points as your daily reward!`
            };
        } catch (error) {
            console.error('Error claiming daily reward:', error);
            throw error;
        }
    }

    // Team management methods
    async createTeam(teamName, creatorId, guild = null) {
        try {
            // Check if team name already exists
            const existingTeam = this.db.getTeamByName(teamName);
            if (existingTeam) {
                return { success: false, message: 'A team with this name already exists' };
            }

            // Generate unique team ID
            const teamId = `team_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            let roleId = null;

            // Create role if guild is provided
            if (guild) {
                try {
                    const role = await guild.roles.create({
                        name: teamName,
                        color: this.generateRandomColor(),
                        reason: `Team role created for team: ${teamName}`
                    });
                    roleId = role.id;
                    console.log(`✅ Created role "${teamName}" (${roleId}) for team`);
                } catch (roleError) {
                    console.error('Error creating team role:', roleError);
                    return {
                        success: false,
                        message: 'Failed to create team role. Check bot permissions.'
                    };
                }
            }

            this.db.createTeam(teamId, teamName, roleId);

            const team = this.db.getTeam(teamId);
            return {
                success: true,
                team: team,
                roleId: roleId,
                message: `Team "${teamName}" created successfully!`
            };
        } catch (error) {
            console.error('Error creating team:', error);
            throw error;
        }
    }

    generateRandomColor() {
        const colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
            '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    async addMemberToTeam(userId, teamName, guild = null) {
        try {
            const team = this.db.getTeamByName(teamName);
            if (!team) {
                return { success: false, message: 'Team not found' };
            }

            const member = await this.getMemberInfo(userId);
            if (member.team_id) {
                const currentTeam = this.db.getTeam(member.team_id);
                return {
                    success: false,
                    message: `Member is already in team "${currentTeam ? currentTeam.name : 'Unknown'}"`
                };
            }

            this.db.setMemberTeam(userId, team.id);

            // Assign team role if guild is provided and team has a role
            if (guild && team.role_id) {
                try {
                    const guildMember = await guild.members.fetch(userId);
                    const role = guild.roles.cache.get(team.role_id);

                    if (role && guildMember) {
                        await guildMember.roles.add(role);
                        console.log(`✅ Added role "${teamName}" to ${guildMember.user.tag}`);
                    }
                } catch (roleError) {
                    console.error('Error assigning team role:', roleError);
                    // Don't fail the team assignment if role assignment fails
                }
            }

            return {
                success: true,
                message: `Member successfully added to team "${teamName}"`
            };
        } catch (error) {
            console.error('Error adding member to team:', error);
            throw error;
        }
    }

    async removeMemberFromTeam(userId, guild = null) {
        try {
            const member = await this.getMemberInfo(userId);
            if (!member.team_id) {
                return { success: false, message: 'Member is not in any team' };
            }

            const team = this.db.getTeam(member.team_id);
            const teamName = team ? team.name : 'Unknown';

            // Remove team role if guild is provided and team has a role
            if (guild && team && team.role_id) {
                try {
                    const guildMember = await guild.members.fetch(userId);
                    const role = guild.roles.cache.get(team.role_id);

                    if (role && guildMember) {
                        await guildMember.roles.remove(role);
                        console.log(`✅ Removed role "${teamName}" from ${guildMember.user.tag}`);
                    }
                } catch (roleError) {
                    console.error('Error removing team role:', roleError);
                    // Don't fail the team removal if role removal fails
                }
            }

            this.db.removeMemberFromTeam(userId);

            return {
                success: true,
                message: `Member successfully removed from team "${teamName}"`
            };
        } catch (error) {
            console.error('Error removing member from team:', error);
            throw error;
        }
    }

    async deleteTeam(teamName, guild = null) {
        try {
            const team = this.db.getTeamByName(teamName);
            if (!team) {
                return { success: false, message: 'Team not found' };
            }

            // Remove team role if guild is provided and team has a role
            if (guild && team.role_id) {
                try {
                    const role = guild.roles.cache.get(team.role_id);
                    if (role) {
                        await role.delete(`Team "${teamName}" deleted`);
                        console.log(`✅ Deleted role "${teamName}" (${team.role_id})`);
                    }
                } catch (roleError) {
                    console.error('Error deleting team role:', roleError);
                    // Continue with team deletion even if role deletion fails
                }
            }

            const deleteResult = this.db.deleteTeam(team.id);

            return {
                success: true,
                message: `Team "${teamName}" and its role have been deleted successfully!`
            };
        } catch (error) {
            console.error('Error deleting team:', error);
            throw error;
        }
    }

    async getTeamInfo(teamName) {
        try {
            const team = this.db.getTeamByName(teamName);
            if (!team) {
                return { success: false, message: 'Team not found' };
            }

            const members = this.db.getTeamMembers(team.id);
            const memberCount = this.db.getMemberCount(team.id);

            return {
                success: true,
                team: {
                    ...team,
                    memberCount: memberCount,
                    members: members
                }
            };
        } catch (error) {
            console.error('Error getting team info:', error);
            throw error;
        }
    }

    async getTeamMembers(teamName) {
        try {
            const team = this.db.getTeamByName(teamName);
            if (!team) {
                return { success: false, message: 'Team not found' };
            }

            const members = this.db.getTeamMembers(team.id);
            
            return {
                success: true,
                team: team,
                members: members
            };
        } catch (error) {
            console.error('Error getting team members:', error);
            throw error;
        }
    }

    // Leaderboard methods
    async getTopMembers(limit = 10) {
        try {
            const members = this.db.getTopMembers(limit);
            return {
                success: true,
                members: members
            };
        } catch (error) {
            console.error('Error getting top members:', error);
            throw error;
        }
    }

    async getTopTeams(limit = 10) {
        try {
            const teams = this.db.getTopTeams(limit);
            return {
                success: true,
                teams: teams
            };
        } catch (error) {
            console.error('Error getting top teams:', error);
            throw error;
        }
    }

    // Voice activity tracking
    async updateVoiceActivity(userId, seconds) {
        try {
            const member = await this.getMemberInfo(userId);
            
            // Only track voice time if member is in a team
            if (!member.team_id) {
                return { success: false, message: 'Member must be in a team' };
            }

            this.db.updateVoiceTime(userId, seconds);
            
            // Award points for voice activity (1 point per 5 minutes = 300 seconds)
            const pointsToAward = Math.floor(seconds / 300);
            if (pointsToAward > 0) {
                await this.addPointsToMember(userId, pointsToAward, 'Voice Activity');
            }

            return {
                success: true,
                secondsAdded: seconds,
                pointsAwarded: pointsToAward
            };
        } catch (error) {
            console.error('Error updating voice activity:', error);
            throw error;
        }
    }

    // Utility methods
    getAllTeams() {
        try {
            return this.db.getAllTeams();
        } catch (error) {
            console.error('Error getting all teams:', error);
            throw error;
        }
    }

    close() {
        if (this.db) {
            this.db.close();
        }
    }
}

// Create singleton instance
let teamManagerInstance = null;

function getTeamManager() {
    if (!teamManagerInstance) {
        teamManagerInstance = new TeamManager();
    }
    return teamManagerInstance;
}

module.exports = { TeamManager, getTeamManager };
