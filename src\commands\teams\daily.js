const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const { getTeamManager } = require('../../database/teamManager');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('daily')
        .setDescription('Claim your daily points reward'),

    async execute(interaction) {
        try {
            const teamManager = getTeamManager();
            const result = await teamManager.claimDailyReward(interaction.user.id);

            if (!result.success) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ Daily Reward Unavailable')
                    .setDescription(result.message)
                    .setTimestamp();

                if (result.message.includes('team')) {
                    errorEmbed.addFields({
                        name: '💡 How to Join a Team',
                        value: 'Use the team request system to join a team and start earning points!',
                        inline: false
                    });
                }

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Get updated member info
            const memberInfo = await teamManager.getMemberInfo(interaction.user.id);

            const successEmbed = new EmbedBuilder()
                .setColor('#00FF00')
                .setTitle('🎁 Daily Reward Claimed!')
                .setDescription(`You received **${result.points} points** as your daily reward!`)
                .addFields(
                    { name: '🏆 Your Points', value: `${memberInfo.points}`, inline: true },
                    { name: '⭐ Your Level', value: `${memberInfo.level}`, inline: true },
                    { name: '🏅 Your Team', value: memberInfo.team_name || 'None', inline: true },
                    { name: '⏰ Next Daily', value: '<t:' + Math.floor((Date.now() + 24 * 60 * 60 * 1000) / 1000) + ':R>', inline: false }
                )
                .setFooter({ text: 'Come back tomorrow for another reward!' })
                .setTimestamp();

            await interaction.reply({ embeds: [successEmbed] });

            console.log(`✅ ${interaction.user.tag} claimed daily reward: ${result.points} points`);

        } catch (error) {
            console.error('Error in daily command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Command Error')
                .setDescription('An error occurred while claiming your daily reward. Please try again later.')
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
