const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, Embed<PERSON>uild<PERSON>, PermissionFlagsBits } = require('discord.js');
const { getTeamManager } = require('../../database/teamManager');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('team-info')
        .setDescription('Get detailed information about a team or member (Admin only)')
        .addSubcommand(subcommand =>
            subcommand
                .setName('team')
                .setDescription('Get information about a specific team')
                .addStringOption(option =>
                    option.setName('team_name')
                        .setDescription('The name of the team')
                        .setRequired(true)
                        .setAutocomplete(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('member')
                .setDescription('Get information about a specific member')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('The member to get information about')
                        .setRequired(true)))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    async autocomplete(interaction) {
        try {
            const focusedValue = interaction.options.getFocused();
            const teamManager = getTeamManager();
            const teams = teamManager.getAllTeams();
            
            const filtered = teams.filter(team => 
                team.name.toLowerCase().includes(focusedValue.toLowerCase())
            ).slice(0, 25); // Discord limits to 25 choices

            await interaction.respond(
                filtered.map(team => ({
                    name: `${team.name} (${team.total_points} points, Level ${team.level})`,
                    value: team.name
                }))
            );
        } catch (error) {
            console.error('Error in team-info autocomplete:', error);
            await interaction.respond([]);
        }
    },

    async execute(interaction) {
        try {
            // Check if user has administrator permissions
            if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ Access Denied')
                    .setDescription('You need Administrator permissions to use this command.')
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            const subcommand = interaction.options.getSubcommand();
            const teamManager = getTeamManager();

            if (subcommand === 'team') {
                const teamName = interaction.options.getString('team_name');
                const result = await teamManager.getTeamInfo(teamName);

                if (!result.success) {
                    const errorEmbed = new EmbedBuilder()
                        .setColor('#FF0000')
                        .setTitle('❌ Team Not Found')
                        .setDescription(result.message)
                        .setTimestamp();

                    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }

                const team = result.team;
                const topMembers = team.members.slice(0, 5); // Show top 5 members

                const teamEmbed = new EmbedBuilder()
                    .setColor('#0099FF')
                    .setTitle(`🏅 Team Information: ${team.name}`)
                    .addFields(
                        { name: '🆔 Team ID', value: team.id, inline: true },
                        { name: '👥 Members', value: `${team.memberCount}`, inline: true },
                        { name: '🏆 Total Points', value: `${team.total_points}`, inline: true },
                        { name: '⭐ Team Level', value: `${team.level}`, inline: true },
                        { name: '📅 Created', value: `<t:${team.created_at}:F>`, inline: true },
                        { name: '📊 Average Points', value: team.memberCount > 0 ? `${Math.round(team.total_points / team.memberCount)}` : '0', inline: true }
                    )
                    .setTimestamp();

                if (topMembers.length > 0) {
                    const memberList = topMembers.map((member, index) => 
                        `${index + 1}. <@${member.id}> - ${member.points} points (Level ${member.level})`
                    ).join('\n');
                    
                    teamEmbed.addFields({ 
                        name: '🏆 Top Members', 
                        value: memberList, 
                        inline: false 
                    });
                }

                await interaction.reply({ embeds: [teamEmbed] });

            } else if (subcommand === 'member') {
                const user = interaction.options.getUser('user');
                
                if (user.bot) {
                    const errorEmbed = new EmbedBuilder()
                        .setColor('#FF0000')
                        .setTitle('❌ Invalid Target')
                        .setDescription('Bots are not tracked in the team system.')
                        .setTimestamp();

                    return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }

                const memberInfo = await teamManager.getMemberInfo(user.id);

                const memberEmbed = new EmbedBuilder()
                    .setColor('#00FF00')
                    .setTitle(`👤 Member Information: ${user.tag}`)
                    .setThumbnail(user.displayAvatarURL({ dynamic: true }))
                    .addFields(
                        { name: '🆔 User ID', value: user.id, inline: true },
                        { name: '🏆 Points', value: `${memberInfo.points}`, inline: true },
                        { name: '⭐ Level', value: `${memberInfo.level}`, inline: true },
                        { name: '🏅 Team', value: memberInfo.team_name || 'Not in a team', inline: true },
                        { name: '🎤 Voice Time', value: `${Math.round(memberInfo.voice_time / 60)} minutes`, inline: true },
                        { name: '📅 Last Daily', value: memberInfo.last_daily > 0 ? `<t:${memberInfo.last_daily}:R>` : 'Never claimed', inline: true }
                    )
                    .setTimestamp();

                if (memberInfo.team_id) {
                    const teamInfo = await teamManager.getTeamInfo(memberInfo.team_name);
                    if (teamInfo.success) {
                        memberEmbed.addFields(
                            { name: '🏅 Team Total Points', value: `${teamInfo.team.total_points}`, inline: true },
                            { name: '🌟 Team Level', value: `${teamInfo.team.level}`, inline: true },
                            { name: '👥 Team Members', value: `${teamInfo.team.memberCount}`, inline: true }
                        );
                    }
                }

                await interaction.reply({ embeds: [memberEmbed] });
            }

        } catch (error) {
            console.error('Error in team-info command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Command Error')
                .setDescription('An error occurred while retrieving the information. Please try again later.')
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
