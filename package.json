{"name": "v14bot", "version": "1.0.0", "description": "", "main": "src/bot", "scripts": {"test": "node .", "prod": "node src/bot.js", "dev": "cross-env NODE_ENV=development node src/bot.js", "prod:nodemon": "nodemon src/index.js", "dev:nodemon": "cross-env NODE_ENV=development nodemon src/bot.js", "wipe-data:dev": "cross-env NODE_ENV=development node src/scripts/wipeDatabase.js", "wipe-data:prod": "node src/scripts/wipeDatabase.js", "setup": "npm i", "update-packages": "node src/scripts/updatePackages.js", "update-ytdl-core": "node src/scripts/ytdlUpdater.js", "setup-env:prod": "node src/scripts/setupEnvFile.js", "setup-env:dev": "node src/scripts/setupEnvFile.js setup-env:dev", "commit": "node src/scripts/commitRunner.js", "log-setup": "node src/scripts/setupLogs.js", "validate-env": "node src/scripts/validateEnvironment.js", "create-env-sample": "node src/scripts/validateEnvironment.js --create-sample"}, "keywords": [], "author": "<PERSON>", "license": "ISC", "dependencies": {"@discordjs/builders": "^1.7.0", "@discordjs/rest": "^2.2.0", "@discordjs/voice": "^0.18.0", "better-sqlite3": "^12.2.0", "discord.js": "^14.21.0", "dotenv": "^16.4.7", "fs": "^0.0.1-security", "path": "^0.12.7"}, "devDependencies": {"@types/cors": "2.8.17", "@types/express": "5.0.0", "cross-env": "7.0.3", "jest": "29.7.0", "nodemon": "3.1.7", "typescript": "5.7.3"}}