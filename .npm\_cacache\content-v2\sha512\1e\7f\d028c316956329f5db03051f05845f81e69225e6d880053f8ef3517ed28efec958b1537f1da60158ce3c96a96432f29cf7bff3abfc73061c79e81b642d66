{"_id": "@jest/transform", "_rev": "158-567da888ea908c4710f46f0717d6c496", "name": "@jest/transform", "dist-tags": {"next": "30.0.0-rc.1", "latest": "30.0.2"}, "versions": {"24.2.0-alpha.0": {"name": "@jest/transform", "version": "24.2.0-alpha.0", "license": "MIT", "_id": "@jest/transform@24.2.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1f41003bb0a28858f913a5a1bfada7653d3c60d1", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-24.2.0-alpha.0.tgz", "fileCount": 17, "integrity": "sha512-/ZTdREIg1D9OYU4HBxzDLF02cYTdGEEpiV+av/CeMFIxoMpZRYBIGu0rypl1XvYmJ5wqBlrFvpCfj2Z1+EqN5A==", "signatures": [{"sig": "MEYCIQCcJ5PTkiSWLlvCTnT35gRFaWFmWsJrnCi+q0eLa4OkIgIhANXCqjmtvnpvMp1eQ9hyo5tH+SCPgx5COUFHZU3HnfJw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31823, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfo/xCRA9TVsSAnZWagAAM5kP/j+BZrhvNyXRO41yXOUS\nfDfGdaH3aSBBgBlH1Wm3uVboaSRwQn+0FOuvLHh/311EsGCCR5XTDZikTuIc\no3xebJJNw8rtbk112YPrGjpxbSWfMekQg5vxlZSu50LCl7QCidyQnSrlvgCN\nMkWR106GOy05NHk9LcEmLpb7Xn4Tk9LFCeaujf+MrpUKQD3zPCMqFIKQyBlB\nA1XgQb90Dr9UTOhUTWSxCsTEcRAAavn3eG3bLWkVbd3YUePN4AtKITDDXNj6\nznATG/a72RzwxKJz2Tt2Ly18u0bkFGmwsPvHruU5aGh5qbZ7Y7kFxdQalSGe\nvlM6GtjtN6oGTm40dqK2AwAJAzA2YAPc19OWy5O2VTXCW4V2jOLOQvuQO+pF\nPC+eg3kCXrq/FkF2ICbWkwJfgxL4y4zcqRl2qEbPn4dyfaRiJ9klKTM6EGDT\nH0lXD6yo3MkMrK1oFAXKr3kfEYNrnPY/tfuFrb0+F8ccUwohZ11Ax+I7Lg4M\n+BsTNfXA4ZK+GCl337owjsy3hg3mgJP44ZO90gh5L2HCDaihmc8u17kS9B3b\nSxgZjk2tiowVMkG73k2xR+BshakcqWgXtA4D+6fgLDih4L82zi5Y1NMc2WG3\n4nFHENy65riWNCc3BIRor8GGBHQ7bLvahyMOB7UiIw2XBbj0EW4lRe+LaYh9\nYZgZ\r\n=iJ1e\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "800f2f803d01c8ae194d71b251e4965dd70e5bf2", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "5.6.0", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "slash": "^2.0.0", "jest-util": "^24.2.0-alpha.0", "micromatch": "^3.1.10", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^24.2.0-alpha.0", "graceful-fs": "^4.1.15", "jest-haste-map": "^24.2.0-alpha.0", "jest-regex-util": "^24.2.0-alpha.0", "realpath-native": "^1.1.0", "write-file-atomic": "2.4.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^5.1.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/micromatch": "^3.1.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^2.1.1", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_24.2.0-alpha.0_1551798256756_0.7794990052953017", "host": "s3://npm-registry-packages"}}, "24.3.0": {"name": "@jest/transform", "version": "24.3.0", "license": "MIT", "_id": "@jest/transform@24.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a18bfd18f25ca28566f5bc398551c047199f4c75", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-24.3.0.tgz", "fileCount": 18, "integrity": "sha512-qrOIa34c+C5kqABGslBz7Lcwi9qbksO9/XcoCcYWD6AnrOmVUBRZSFHzo7Enk5iHUXRGnVnXvb8AyBXKlwpdGQ==", "signatures": [{"sig": "MEQCICx87DOPNdnWt7i1Azn3L/wWSImQjsdS+Ke5Z5NtGczdAiBtKzj5s9b2NO4Urnz56GN4f1g+WvYX7//sahs5wmY/cg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgRXeCRA9TVsSAnZWagAAl2QP/306RIeAAT3ntl09edQf\nq3sNLVhuH3OkMO8t4Zul/VYKhoOjpBNuV1LO2OGgDoJ0s7zcOBzAQCof2Pxh\nngLl7+aZMss9HNKlndMaZ3c4ANJNYfyk57e3/fBGNCrmteWsKL3eNPJxUhqQ\na9nBpzzbAiy78ZFPS69xl/69bNCwXYjRSEgyBOZPRe4H8kDTo1ikRzuOdG+M\nrhT2RJsoEWDz488QcgP/qjodUlA2ldftvBbAI7I+MVoDbuoaIxh5p1IvReWT\nR3GNabzp12sZWW7346IB3FW+YHEwV4Jp0RgPRg5lwFmknD9E8h46nGDcOtoI\nyZPFJeXJW6jhe3ahx2izgvk69+gHF2aZxAF8TIk84LFBpgUFjmI9kaFN2Doz\nxS0ZrkBVKD+MSqrBzyrIujLNUUBtREJWv8+yHywYepGO3HmNmnm+58EnGlM2\nEaepWeDvqAWg0VRMbsh4bCNv9gmr6goMoq2PpycHLBXXxd5rBoz/uFm9wRRl\nNBykj29yBF5+io63zvAuf4G1GHmISmeYDZIV4OXA7JP8Ka8OHSOb3t1alUKG\nXGIGdgJ6rGjAvHAfpIJIoS86NJDP+LfMf/nl2cFjLPT5wTMslQzeQ+F1qF5m\n3lDPGhS3R4QK9JmBNznYRnFWd+YZBPmpEpb7uUPObWcQPID6UdrWbQmW5Cpa\n1LG8\r\n=R9w2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "3a7a4f3a3f5489ac8e07dcddf76bb949c482ec87", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "slash": "^2.0.0", "jest-util": "^24.3.0", "micromatch": "^3.1.10", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^24.3.0", "graceful-fs": "^4.1.15", "jest-haste-map": "^24.3.0", "jest-regex-util": "^24.3.0", "realpath-native": "^1.1.0", "write-file-atomic": "2.4.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^5.1.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/micromatch": "^3.1.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^2.1.1", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_24.3.0_1551963613593_0.48659463344811016", "host": "s3://npm-registry-packages"}}, "24.3.1": {"name": "@jest/transform", "version": "24.3.1", "license": "MIT", "_id": "@jest/transform@24.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ce9e1329eb5e640f493bcd5c8eb9970770959bfc", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-24.3.1.tgz", "fileCount": 18, "integrity": "sha512-PpjylI5goT4Si69+qUjEeHuKjex0LjjrqJzrMYzlOZn/+SCumGKuGC0UQFeEPThyGsFvWH1Q4gj0R66eOHnIpw==", "signatures": [{"sig": "MEUCICN9OiTTazVTeLwQSklL1e6OlFqZBXayD+75FNInWD8ZAiEAgGNaLdgXhg6cJkETs4jj1KXIeQ8c5AmwvlMPk7VHbCE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgaVjCRA9TVsSAnZWagAAeAwP/0bZ2DZOXVTGZ5nmRMXF\nw2rtlZ1nefHGbBcgFk758PkOSmrh84FhGGOTOvZzsRNcN/CoDr3H3WLDQSNv\np/44g3n8AlvxOfLQkZQrzuzPf39PesWeuQWu+YP15KuQnaqhD5JnOnQVsWBT\nnkZOCwA2iKxBH3r6NMpOWKByv28oX6fqwUvJyT7mT8Yfy8Yxnt8rSALQIps4\nLqMIpMixBTERT3wGVqZV3J2nhXNkBbXW0IUPwK5NZqRLgpajk+or5KpLMSVu\nVLpgTLJqAf0lqGZyKT1E7hmN7WCla+SQU7Xd/dRMVhR+f5yFtQzuQ2NYR0yb\nJvH+1bTGSPytf+RpaLEDX7XgbyOnRlEWBm34z8bQD27twI06PSxw9iXOVWZw\nG8bD8EJkcGaI4m9wIYsvtSnemp4aURATySt7/EC6Ahm5EPUnCoPtzeXB/k8G\ng4nL0HzOWqzyl876wA48a2w+7TNSSiNLLZc8UhCUsqRQs030KLXrtTxCpF6n\nczulsiqHL9Yxpy/Mrf7/yqdivQRUaLktRBpRW3gFd3LNCJn/OnoFVqLTj0Cd\nLsuGfOjZymaBCRPwWYTib6KbERt2fKoy6F7ESJkv8fiqo04drRVmq5RwQZgC\niBdvsEV9cU6zS3eaDf0mDZ4Icawmb/GX0ej+ScOoWZSYjCEh/oyP5Rncti0P\ndOTp\r\n=XMpA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "65c6e9d5e398711c011078bb72648c77fc8a8cb3", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "slash": "^2.0.0", "jest-util": "^24.3.0", "micromatch": "^3.1.10", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^24.3.0", "graceful-fs": "^4.1.15", "jest-haste-map": "^24.3.1", "jest-regex-util": "^24.3.0", "realpath-native": "^1.1.0", "write-file-atomic": "2.4.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^5.1.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/micromatch": "^3.1.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^2.1.1", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_24.3.1_1552000354647_0.6180327257427991", "host": "s3://npm-registry-packages"}}, "24.4.0": {"name": "@jest/transform", "version": "24.4.0", "license": "MIT", "_id": "@jest/transform@24.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2f6b4b5dc7f7673d8fec8bf1040d4236c58c7a60", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-24.4.0.tgz", "fileCount": 18, "integrity": "sha512-Y928pU6bqWqMlGugRiaWOresox/CIrRuBVdPnYiSoIcRtwNKZujCOkzIzRalcTTxm77wuLjNihcq8OWfdm+Dxg==", "signatures": [{"sig": "MEUCIA79xXBZht5TG90YnztEWX9XJLYQELa5HFIohAg8LjAoAiEAiNR7peDbW7q9cYi0n3NZY2eP7tyYDZOyih80xlGkqpo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchnd9CRA9TVsSAnZWagAA/rYP/1Sls7oBrqcHZd6DIpmN\nuoAyx4/8qZY0Fs6ImYxpukj2lPIJTyljbxqoj/4LtPVWZjsrFiMnDmsbzLxC\nFpCes/kqrCfrvanSWqENYSXSULC4+pQyt9E4/irJjSQAk9gHE8dTdm+HeLzm\n/yG8zov03+tCJbWzsXpNiRqzYAcXjdHDG17A2YKy9PyS+YnVT00A0gtkVzxf\nr1MtGjboEfILXn8KQbj5rYkg2J08XJwoehghrCVPR/HFDliI3RFmlX3d84Aw\nAHNDhl0+1VowFc8vUXuyHwtUDFHBB7dMqIqFOZEBva80qGpEEdPmiEFc8HMG\nM01d0iINJrIzTFKLn82TcOgpMm0mpmU3CPyoB2TJUrpPyj7h6TW+gtrvJaYt\nQbJLiTz/GgvyStQTLF5WtsW5OFMgbf/6fY2PdEChyUeGVPrGfcOVPkP8hBwi\nZs2+DnYtguz0ReXQ43Q2AEu5zyVS0qzekCoDHsX++W1jKLvtZBUyUrUpDDFY\n1kTSODTk/vntfTQwWgAL3jopUCYXGDLoyCmfUJw6lMVIXI1STBAiP5Nz7HAl\nCsFeOKM6kiEY4GSiO0YKo7EmK/aK2DMk+wPnqQtlQ8T2ZrfBGDt5aHqW3Cm3\nDj2DRpkcFYfxdIyA4vS9ke8PLqNnEwiVdCV6ZCaDX6G62Cp7VHCtXwrCUHbM\npWm4\r\n=5QJm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "a018000fc162db3cfd0ebf9f23fdb734f05821a6", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "slash": "^2.0.0", "jest-util": "^24.3.0", "micromatch": "^3.1.10", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^24.3.0", "graceful-fs": "^4.1.15", "jest-haste-map": "^24.4.0", "jest-regex-util": "^24.3.0", "realpath-native": "^1.1.0", "write-file-atomic": "2.4.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^5.1.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/micromatch": "^3.1.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^2.1.1", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_24.4.0_1552316284274_0.7976840365431481", "host": "s3://npm-registry-packages"}}, "24.5.0": {"name": "@jest/transform", "version": "24.5.0", "license": "MIT", "_id": "@jest/transform@24.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6709fc26db918e6af63a985f2cc3c464b4cf99d9", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-24.5.0.tgz", "fileCount": 18, "integrity": "sha512-XSsDz1gdR/QMmB8UCKlweAReQsZrD/DK7FuDlNo/pE8EcKMrfi2kqLRk8h8Gy/PDzgqJj64jNEzOce9pR8oj1w==", "signatures": [{"sig": "MEYCIQDZAxalODdAPnIavLLiBFItfmDPe5Gvbem/Zukm764TGQIhAN2dg039wZNOXth3ZuegqSadifObMk/qZyBpZd0eJmLk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJch+A7CRA9TVsSAnZWagAA8E0P/0ePaIDyjJUguJwBFsYm\naUP84XL72S8696QqHkjjJpUYejHqKs4WmIuwU/x7oPNWeuYjxi4fncXBc2ay\nZs0YO7ohPKVBVs8yXw45q5Tx+Voh2z1mjMix2gJGj4kCxKzaFxw9Xj2IaAK+\nRcRiYZ00ZEVRHTQ/12V7e11t9QISpytWlNQo+nODArW+54Bu0owsT2n/KClD\nqc/emIBZAs9F5aYXZqu2WUybHsO5myKdypOiB9aNk4hxwx31FFj/VaIdfU2y\nhiu7obiATo2Ft42pl5Degv5sm1SV7BhiFJC40yGWkud/G6GGDlsBUORy1smf\nWjvV3eLX6A+9l3GCVN0ImnKX/BVUSPXLuojGTd040oEndd8kYTfZ+fn1oiAF\nA36JLS9ySZDYbJlrgg9Sam5uTqdAY6ga8SM9ekDtFEj1WziOooZ1AXAoH7v7\nrgsGo2yWMj+UmmIxLAFA0eUHV04FsfxPNDg226RZJUKDY3fvU4HsWRVvkV7A\nkE40snqFpPLjYJcJ1g07zGofmsNwpJeq5F9rgdj3oTCUTHSinzI83+wTiGVI\nxtVzQu5Jej1nR+LjjHikA/5luREppXTBxGdyrgnZ2fL8uCnWIVVB5zjWb0Em\n9b6v0iSFAZ5iWqm8yzWcN3tolHeMdYBkWqW1DuCupnvP+FngoUQf2Zpe5GyR\n3aK0\r\n=Gr3x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "800533020f5b2f153615c821ed7cb12fd868fa6f", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "slash": "^2.0.0", "jest-util": "^24.5.0", "micromatch": "^3.1.10", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^24.5.0", "graceful-fs": "^4.1.15", "jest-haste-map": "^24.5.0", "jest-regex-util": "^24.3.0", "realpath-native": "^1.1.0", "write-file-atomic": "2.4.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^5.1.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/micromatch": "^3.1.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^2.1.1", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_24.5.0_1552408633643_0.1650443814330198", "host": "s3://npm-registry-packages"}}, "24.6.0": {"name": "@jest/transform", "version": "24.6.0", "license": "MIT", "_id": "@jest/transform@24.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "dc7a00591651b89c2582602fe5c4ce47a5398148", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-24.6.0.tgz", "fileCount": 19, "integrity": "sha512-aC7Yff2XREV1C/RQCoP1WzO3NU4EtmImIJXnNm4tTgaLoGGv1HJuXziyd5v7zOjBzn96793rF0iLHlFT4w4ErA==", "signatures": [{"sig": "MEQCIBA9ZC+dX5/q9duHKqgaUX5Am1gG8K/LM7JtEeltyKobAiBpShxqjEo170c1XERt0BMzttv26sUfsZfaPvxO2iJjMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcopAxCRA9TVsSAnZWagAAMJkQAJchhTU7h5rUM5S2xqTE\n66Oq4xBcKYHwsFaV9vnUe03jdju3HShykDFPTr55zON4EmQ7OP9k6WiKCy7e\nEtE9NmEyDDXb4+nJ2b8JiYcHOUPAmgm+NHNSQFUM8sXB9jv1LfiJflaSp7vg\ndD0wFHR9i1DAOb0+mDldgbhvZumfnK8Vu+KQlomuw99XfZ1plqx5T98A6QIh\nIlQIroKoxCaVl6yqKV3Z7FhZ4IbeYhIvh/e76O8E0GefqM4zTQPSxiME4nP7\nQtU/mz4Zv89v/LEjTVIBI5ucw16K31P/wuRchitSbmGQTP65HahMXCe1Ew6l\nf5RMhLOKq4Algq8h8e9PY+A7Awwbs9T6xED0CbxOxfNXBMplD/D+FLSdHC5h\nS7qv1qU1PGqAoG6Nk5cdhvU85Gqm14urHM7FcwBiOu3ibvqc/71srLKrcP7q\nBFaX69F4Rf/NZ6TLXnsXbRVLjnr77tE5yuOklPi2V/KVjXuEbQBYKpS4kFRv\nUC1CI3p0gz5zjdgEftNhVModx2Ib5DDV0C2XuB5wiBJgP5KLiW+s0nzggAu4\nTPgfU/1OrInQSSsyRAbcAncYvNdE1zi22fY4p/fSml54Kau8vUXKeTaaV6Wx\nTADsXoSEHEMkce5qFzyCxZt0HFWJ7g6FMY3mXeRdT8mMaa1IyhVTgQPq22Ca\n3KpI\r\n=7kbp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "04e6a66d2ba8b18bee080bb28547db74a255d2c7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "slash": "^2.0.0", "jest-util": "^24.6.0", "micromatch": "^3.1.10", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^24.6.0", "graceful-fs": "^4.1.15", "jest-haste-map": "^24.6.0", "jest-regex-util": "^24.3.0", "realpath-native": "^1.1.0", "write-file-atomic": "2.4.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^5.1.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/micromatch": "^3.1.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^2.1.1", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_24.6.0_1554157616341_0.09081297114717346", "host": "s3://npm-registry-packages"}}, "24.7.0": {"name": "@jest/transform", "version": "24.7.0", "license": "MIT", "_id": "@jest/transform@24.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c74a1ee5c02f532c22bfc3cddf2685cc3ee5a9b4", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-24.7.0.tgz", "fileCount": 19, "integrity": "sha512-jwgjrNaZjUuYAf9OZFgyChqEN9p/LS8YkK6D0vuORLXoxiBSZy76tX0/RkCkSkOjgI8IsFwccOJ6RcYBw45R6Q==", "signatures": [{"sig": "MEQCIEJ/LTsvzXmcfO7gqbZM0XZgIPxaWcpbx+PiKzrzvdZYAiBER6UHSAp8lNTF0RdVZB32Fda47m3pinAlHDcPOYwmOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 227536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpC7GCRA9TVsSAnZWagAAvmkP/0xyDvli4OPqy9jgYyRj\naeiN2AxS/QIbzfAHKoYxTy42gj21ishHB8CdIMWzP9VUsSxfW2y0Gwkir5lI\nOHeQLwhabJpROdU33IEVnL7EcneWqxpIUMIWZS18i+BAH/gInyPlypanhrxg\nBGss7lMn+DnWULxf4YjzRlJZJS6PfL16shVkMbwjYDpiKEgkv7hDWXWYoQTI\nIguqJsuev4htOCqMQkzUcs749VA/YqZev26Uc6bKW286d3RLGjPrXIFy7U2v\nFHV/YXSL1so93CZbU+fR0/NxX+hKIx0mo4SdhGI+//NVxu6cE0Y/ys5B/JVA\n3MhF+ww+gMr6g31N7PZKZ6fgeXaarHdj78p6GZSF3RF4L5ufYCNtNa1aiC51\nBwbyPMwm2AJYI/1NOy9CUkOzzPwxQW11rhzbLRW/iolaqHuN0Itqz8sBYgnW\nkEL3kiJQFOHzU52xWSqHATZ5zPayd4Ll8yUlGckQAlOvwExgiK9ImzBMGqCX\nfdZMSRN3raGTSqDvaroVsioALRMVfHPpExe1h9bO2C0DXyXkuoKek4x013CJ\nyVcdI7NMbhs1bzr5VI42XgffmAlUcFEoTrDKIe+wxgzAiQ32KSq/8hZ0atju\nk3gMWHgD0QrDmVCyw28i5S6g8G0dEPb1itdR0U+KYS3dVUMbUgUxdX0zaANO\n4ggY\r\n=OQqN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "eb0413622542bc0f70c32950d9daeeab9f6802ac", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"chalk": "^2.0.1", "slash": "^2.0.0", "jest-util": "^24.7.0", "micromatch": "^3.1.10", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^24.7.0", "graceful-fs": "^4.1.15", "jest-haste-map": "^24.7.0", "jest-regex-util": "^24.3.0", "realpath-native": "^1.1.0", "write-file-atomic": "2.4.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^5.1.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/micromatch": "^3.1.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^2.1.1", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_24.7.0_1554263749422_0.7990559609555319", "host": "s3://npm-registry-packages"}}, "24.7.1": {"name": "@jest/transform", "version": "24.7.1", "license": "MIT", "_id": "@jest/transform@24.7.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "872318f125bcfab2de11f53b465ab1aa780789c2", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-24.7.1.tgz", "fileCount": 19, "integrity": "sha512-EsOUqP9ULuJ66IkZQhI5LufCHlTbi7hrcllRMUEV/tOgqBVQi93+9qEvkX0n8mYpVXQ8VjwmICeRgg58mrtIEw==", "signatures": [{"sig": "MEUCIF+Z8/+szZNaCYc6i/jVDauRj5v2LszNgtQd1YNPyfTJAiEAh1YT6xQ9TB+06McLzIwkVnSdyLLQZLB0iBw5gc7LlSo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 227536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpVuKCRA9TVsSAnZWagAAWGEQAITlg9tBpzVbHzbw1DiO\ndrz3t1D92BV8L9A63EUxw5DLikBQaMWB3yDc3Pc1tbU5pcLHJYf/5Uf+K2Qd\nD9iIEhqvejmBUpo44mGt4g96+7dSd+vKH5gUJE+wbmkaQDFkfsT8EDpD82UZ\ny8bEn7c4FO6Vh24gOEfzOqvvpRV0lYCl0/kYFQQ6xsOgSqMsRyWx8wnDYS38\nqvSMgVIgJmksmVsgQ6+6sfGUZHfbmaRlLxDBVR5dWW0h/PvaZQwnnQ9rL2c8\nbtiNy+I4CTuF9E8H78AXmXi8eKy9IiCbFwoa6kJQSVfdaEV3fYJX2pLG9u8/\ntLrd49PP8zJjeDArAUkH8gcgEQfs5DdtXJ6BKII4TwHh5+uT9biVQKeavRo+\nm+K4qs/ChKsln5bjcZQAQZqYbMaEcFIpZibKeQI+I5LoKzs5YavmjmR3nwnK\nYknwX9t2GsmGPICUcYUoXF2iY9mE9Yq3ZAl2gIAV+BW8Kp6bc+ouXjnHmuCF\n//vx/XLz+CC+7nF78UK8iingUztdZFrdoHjystAoTL0vguSC6NxEzl4QjnyQ\nqS7EWY4vubSI77JxnZHRUKv2PAB8GxBrTsre0gGMdi5RXv/8tNWI//DDEoTi\neKq3IEY4h3vtCytJ6dDahoBEvwHJBrBoUE+/4ieuzudqR9m77rQx+sEKoNtB\nSy8w\r\n=oqk9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "0efb1d7809cb96ae87a7601e7802f1dab3774280", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"chalk": "^2.0.1", "slash": "^2.0.0", "jest-util": "^24.7.1", "micromatch": "^3.1.10", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^24.7.0", "graceful-fs": "^4.1.15", "jest-haste-map": "^24.7.1", "jest-regex-util": "^24.3.0", "realpath-native": "^1.1.0", "write-file-atomic": "2.4.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^5.1.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/micromatch": "^3.1.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^2.1.1", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_24.7.1_1554340746159_0.32829990735694037", "host": "s3://npm-registry-packages"}}, "24.8.0": {"name": "@jest/transform", "version": "24.8.0", "license": "MIT", "_id": "@jest/transform@24.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "628fb99dce4f9d254c6fd9341e3eea262e06fef5", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-24.8.0.tgz", "fileCount": 19, "integrity": "sha512-xBMfFUP7TortCs0O+Xtez2W7Zu1PLH9bvJgtraN1CDST6LBM/eTOZ9SfwS/lvV8yOfcDpFmwf9bq5cYbXvqsvA==", "signatures": [{"sig": "MEUCIBkHwcqH1UwGVdaxPD+5pdBqd6dL6of14rYxJvq0DF/jAiEAijJVtGgnrmqEM4EPxDzlH7V7dt8lMiFq5HJkP13eSuc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 232163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczkRICRA9TVsSAnZWagAAzxgQAIOtUIlzUGPqUnjVp2x0\nafpMXcgPK8WwHwPJl6E1rtMXhPiIsOm0fZAvkyEGkIR+Htqx2jnDoud0JT92\nVDlwHrwCWjOCC8UPLuAztrcda2I/SCI7htLBrNf72AV4/uW/AsDWC7iO4sBn\nJBiKFEZquIUOu8yKlEDFGfWdZFvBAKsfKzLBUE+t464+jDn/ElAIQI6MSJ+q\nSTtVllbvlbNOhHVwmOya13DpJS5VIGozDn4EWvoA4IgFocLQEpwlKj4dc7n/\nppJgPYO4m3Yu0IpwvP8NTlKAR8BnLWEsYjNvTPO3HF+XuhxlbmabGpvHQCMh\niqlCFShr5wbnFs7qOu3ywj4PX3viF8VMP3BTXLx2Go947DJyBoINYppK+4xU\nBx67Po0ASG7K5exBkNqlQBK08fScl+9zOyy9VA4l/5UblzvWk4J8iYo0lbIG\nxb3egiF1OMFNiB4Vnl2J8ywLStiVPAFDrHAi5lb4SZDuoP9RRYrnjaAIkRLm\nXju0BbSJnK67sm3M+HpJ2x4pw6V5nG92ImczVu2CwGTbT00ao0FlVLwm3Ye0\naevqYHMqsVMtKQ+CA02h8vKRZQrrncajlpEkxpDzdqisUQl9zIULV/FUQSbn\nG46r6n8PvXX8ahmcNt+YbzEhpicw3mWZEfol/LvwVvhcsIUTB/KSjiPSD9Vg\n8++o\r\n=reEo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "845728f24b3ef41e450595c384e9b5c9fdf248a4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"chalk": "^2.0.1", "slash": "^2.0.0", "jest-util": "^24.8.0", "micromatch": "^3.1.10", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^24.8.0", "graceful-fs": "^4.1.15", "jest-haste-map": "^24.8.0", "jest-regex-util": "^24.3.0", "realpath-native": "^1.1.0", "write-file-atomic": "2.4.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^5.1.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/micromatch": "^3.1.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^2.1.1", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_24.8.0_1557021767746_0.058788338560890274", "host": "s3://npm-registry-packages"}}, "24.9.0": {"name": "@jest/transform", "version": "24.9.0", "license": "MIT", "_id": "@jest/transform@24.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4ae2768b296553fadab09e9ec119543c90b16c56", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-24.9.0.tgz", "fileCount": 18, "integrity": "sha512-TcQUmyNRxV94S0QpMOnZl0++6RMiqpbH/ZMccFB/amku6Uwvyb1cjYX7xkp5nGNkbX4QPH/FcB6q1HBTHynLmQ==", "signatures": [{"sig": "MEUCIQCClRg0K/nvD0gNSh50lGndalFAZTcv+ugZ24igILAKywIgOwvSNbZY5ug5de6IbPnzMUZouzqg8GT2quNYD4wpjRA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37004, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVku3CRA9TVsSAnZWagAASmYQAJXHN4r2o7Csl2R0VAyo\nbanu4aLxWyzqQWmZfFacdDYpAFhB58Q/4nl+KzE9mTO75/IsBSYD9nLXEvPc\nz46r1vF8ffZsIY/tihLb8jwl/gzpeE9eiD7YEoOaHct3KFD9O3TpleatbPNG\nPo5BKC3PEr+bUWRIf7tFE+aBY1gOpttgHMeOUiuZ6pD2htH7G8rTYiXw0qcF\ndly8UC6WCUsmWECJn43vVRMV95T9g4uYlDWu8Km4AbP34ebFT8tul59z5C5y\nnkCST5vJp0NCvbU9p+i1PrM6KlMDIvsAH/0nz0MrpAwWypMzfJGIoloAfG3l\nCRENtA1EQiU/8GIFkpFz+d3IYHzXD/bPdOVKJoHV0fOqNR4Jo6Z6skQvJViW\nck8ez88HXX/5/fNZdM+cLQPCzAJ6TPaSopMrLBiyMSW2DyKm6Xfgclj5skEb\n7hAgbeTyonr8PN+Goa5Zw4j77sQNcFN+Ehh6AA0w7uO1AkD+a34mnJDRj7Or\nzdFNiGQqwOG87m+d3GV/mbpgmc9y5kARseljxRdD9xguXQnHCHAmkoCrIz79\nOpC5BANZlmWDBTbYn/vJIUQQvqJ3iwAa2BpLPyC0eCqjIBtTEZlktqY7uzYb\nqGZyPCB9RTjXrCxGPDHrDLL9MPUyA86k7xidPB82yrvsTzt16YTLDTCY2sGE\n1KEt\r\n=BL+4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "9ad0f4bc6b8bdd94989804226c28c9960d9da7d1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "directories": {}, "dependencies": {"chalk": "^2.0.1", "slash": "^2.0.0", "pirates": "^4.0.1", "jest-util": "^24.9.0", "micromatch": "^3.1.10", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^24.9.0", "graceful-fs": "^4.1.15", "jest-haste-map": "^24.9.0", "jest-regex-util": "^24.9.0", "realpath-native": "^1.1.0", "write-file-atomic": "2.4.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^5.1.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/micromatch": "^3.1.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^2.1.1", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_24.9.0_1565936566569_0.7001445855676198", "host": "s3://npm-registry-packages"}}, "25.0.0": {"name": "@jest/transform", "version": "25.0.0", "license": "MIT", "_id": "@jest/transform@25.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "21791a9716917fbe2b82d4ab90436e1a174058eb", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-25.0.0.tgz", "fileCount": 17, "integrity": "sha512-0/x368Ls1T8ne8liDDOAUUjO9t6PflznzpmIBJWkU36F3qMW9SfK1WvTGPcYBuICdrzW5Lmt3DLDPENrfeOrNw==", "signatures": [{"sig": "MEQCICNVutRvskyht0aYm++3mUoi0fUlJije2OjPKrwa2tufAiA76XmI0bF4biwvAEK3BG8nqIjxv2ECrjab7gqdzwxc+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37604, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXgrpCRA9TVsSAnZWagAALVkP/jZi0G/gu1OaFZVxzfE8\ncx+0t1xaYPJQrXNxQZsQ+ruW0sWCUoZq9ngwT3O2vN6U3bvYEZJSxYhheNA+\n0j9g6HbKCkXYZw0LrixnFYpOHEnQATZfUr8VlJURcnFbZaPWWmjHoS2BeArE\noo8bE1gXTiLCVDGBU8SzwNWuPTJbYbNqq7Nf8ar1keAkTkS4EjDl3q+rbvg4\n6fHXg3qSatRPXTCu5N93zCwT12RB8scLTaduUGlhv3mZ6MWpcIN7U3uFIYk2\nY8x3h7JlydAs6AcQAYdRapKMuuBR6h0jvUKBzY+473Mt13l41TlriUzBSIPz\naPNpmaU8RHH1niGcfbWwwx0k8qzAm8BpvTNy2smIMGtehi0B6XjGXwkQkeoQ\nzytsCbl4ZckPd60VYpHFYOBVrSV+J7XVLzbMVQdmDwX4ZkGcCk6M+aRmhUdF\nTc3wMGe/mxYf/Pa0WrHJpiuPhRzA6vA1MPWvBAAXahRnuZAuJLuyU+Y+3xvr\nG9C+/fyOUNaFS2w17HTgMg88jPcZvUg4wYuh1IXVa/bFJClaO+/JCEEIenL4\nPxgxQRMUDKRaVIEyBcZRrnLu5YafexB578TPrIzIdFvgeD+k8Xo7OUj0o94+\nU/kvXDoqiZaiwA7LVtQI/wyi6lGaApDOkLQXa5oilvpXJPiIhgaOUIX0RuyD\nbyMs\r\n=+4ET\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 8"}, "gitHead": "ff9269be05fd8316e95232198fce3463bf2f270e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.16.4/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"chalk": "^2.0.1", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^25.0.0", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^25.0.0", "graceful-fs": "^4.1.15", "jest-haste-map": "^25.0.0", "jest-regex-util": "^25.0.0", "realpath-native": "^1.1.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^5.1.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/micromatch": "^3.1.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^2.1.1", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_25.0.0_1566444265184_0.9729667126433079", "host": "s3://npm-registry-packages"}}, "25.1.0": {"name": "@jest/transform", "version": "25.1.0", "license": "MIT", "_id": "@jest/transform@25.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "221f354f512b4628d88ce776d5b9e601028ea9da", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-25.1.0.tgz", "fileCount": 17, "integrity": "sha512-4ktrQ2TPREVeM+KxB4zskAT84SnmG1vaz4S+51aTefyqn3zocZUnliLLm5Fsl85I3p/kFPN4CRp1RElIfXGegQ==", "signatures": [{"sig": "MEUCIGqSi7jgJitCW40hUbi8spItshnQC/3sM/JHVU/VmZEtAiEAtamoiq0aUNT0BLmxlYAqbT9a7DQyLFiV6lyWXM9jqiY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ56UCRA9TVsSAnZWagAAdPYP/jQxBs9pZcz6F1clOk4c\nfQlPG5OpVVIrlNYA+RIhXCDtVtFD3HMdcWWjmZifnMoxbWWXxlskKsfttjpj\nH1hoSArJKaVJxYPXiYpiOD6cqwNJdnLrWhIOQS4WUdWY5JuMCL6U0+/a1t71\nvvqU6pLVp3gfESB4Bib1/HPiImQa8wO6Dth5oNJpOrukka1aDwllSB82Xeqw\nJFL71D+Dugs9oq15gyCVbDX3qiMP31a/HIMg1SX+u7xVtx/7K8bvYGY5+CoW\nuiDhCjRmderMnVSExUOxwmepJRz1NpnwMYBSbYQSU1262HJDkjxSjgiLdXCp\ndpP0JjQIUwcerpruw74pYVgmFvjdH+SC4lQuWew/zHQBvjfoFSXTwuq2/CFy\neGlb7/SwKWayh7tR1kN5VK3KvuK6X0zBjZfRKGKFYTwXO4cUcE8AZgSP7YbQ\nn+C6OkGqs7Nr8HiCJxU+vYLt8S2EtnI4MMgM+4HV2WbAlJ8amR1gXPb4V5w2\nznOJOaRHXi+/7EsYVjLeNQZWh/uYm0xwb4lauAmwIR1yVGkZNSy9RqkatsE0\nTV4Wk4hVx8BRQtrD4WZEOd0ebuUNEF661SMzRJZNGVSigMfCM4BH8U/y2/Wt\nANEqZZdBwUqC3QP/Ii0mPBKRii2x9apH3LGoWeA9Gz52O+/+GKqe7B6b5epT\n8gEk\r\n=/Pho\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 8.3"}, "gitHead": "170eee11d03b0ed5c60077982fdbc3bafd403638", "_npmUser": {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v10.16.0+x64 (darwin)", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^25.1.0", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^25.1.0", "graceful-fs": "^4.2.3", "jest-haste-map": "^25.1.0", "jest-regex-util": "^25.1.0", "realpath-native": "^1.1.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_25.1.0_1579654804517_0.7283585954738745", "host": "s3://npm-registry-packages"}}, "25.2.0-alpha.86": {"name": "@jest/transform", "version": "25.2.0-alpha.86", "license": "MIT", "_id": "@jest/transform@25.2.0-alpha.86", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "972d70e27c1114a88da692f4b62ef7d7081c880d", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-25.2.0-alpha.86.tgz", "fileCount": 17, "integrity": "sha512-wul8R0pmXp4WmvH330TtPzq0jd6EvqyzS3GMCI6evYqkZG5B7t26akOvqmT99d/wj020+l6eKBY1F/eXtxovsg==", "signatures": [{"sig": "MEQCIElqHWzKRUlnw29L2n7sNscjbV5MiFaPqbC0ImTN5KxmAiA4OQdJhuIb+aVCvuYcOIskOREecBBNVIGTbRRu+cwp8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42065, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5HxCRA9TVsSAnZWagAAmq0P/R7GqwSYNzxBO8VB4Fy6\nc47Vn01Med1rJlDZsTprR5pn65zi6EXd5GeMvFkralzhpPXqDKqneZyKLezh\nSlnAHBfd3SnYLqKLkJDf6t9x5pLliHtLeEzjEteM01Hmjna3ZkP8f9/B7an4\ni/Bs5IEgl657t4IXrzJC/RNSgsPdUHlkj46G0IYNqUb48H0EO0C8jWv/CNr9\nJxWT0VacxZdbyWiiPbmnRJwMEqHqV0NPeD7BkeVqGMWKS2EBFaNzndEfFG+X\ns85hWhC/R5rBk88wUimv571u/NTXbY1jbzvqBXSn7iYw5u5K9gjKphSDbwS8\nCGrReNDZrqTSriV8Bz6c1+Im3xDRPIsBApVsk6rNc8+LaNAfXQ4FiFHU4n8R\nFfx38+3dtxImA8MFoypKj/ZXSTvDqRj+9XGeVX8KceYob70as3pWE5C4sHEi\nzYfNEnFcRzjJI5aIsZB+na8x5aw2886Jdg4MlZ2fkHEoEaWI6929f5xFn8F3\n6Eitye/0V5p8zrBfLjx/pyrJy03Ek28fKvKphlOiQ/ZCLpWqvaN2bVW1bp/b\nRn9TO2HQADhiNzuve2NfenZemrhOVvGg3VLHsHXWvrjf2V9mviQ/2XD60NV9\nP00wDn3jB98fZ8PwlwgJDDRL6KG/I+uXqL6WMVFQTmnU/d0F71InvhZQLyuF\nM6ax\r\n=YmNO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 8.3"}, "gitHead": "cd98198c9397d8b69c55155d7b224d62ef117a90", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^25.2.0-alpha.86+cd98198c9", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^25.2.0-alpha.86+cd98198c9", "graceful-fs": "^4.2.3", "jest-haste-map": "^25.2.0-alpha.86+cd98198c9", "jest-regex-util": "^25.2.0-alpha.86+cd98198c9", "realpath-native": "^2.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_25.2.0-alpha.86_1585156593427_0.5657028120998071", "host": "s3://npm-registry-packages"}}, "25.2.0": {"name": "@jest/transform", "version": "25.2.0", "license": "MIT", "_id": "@jest/transform@25.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c45ebbdc0414b1b20050b1fb58aadc67054a8643", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-25.2.0.tgz", "fileCount": 17, "integrity": "sha512-PVElAX0TILnRB4iS81Yus0kvU1g/M4+jwies/joBg4Z6SljFRaWnz5ZEcb1io194hRp6G5VI+em8XTYNDVWHoQ==", "signatures": [{"sig": "MEYCIQD73aNKczkZUaWfvmO7WZzAG7khPRcY0FWIMqBSJ71TxgIhAJZe8+7yQfmov71dNnPX9Jn5wcjs7idEdtVFY7mkZTJZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41970, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5vCCRA9TVsSAnZWagAAlv8P/jDdoZtinsh0yqznPyP0\nSn46CxHeU1l6EEjQdjB9LTJF+xMBbO19nMvBc5hvU+doYg+6xRVVHXh95I0q\nw219NK3MPkS7xhoObrflSw91PhttVQVJZNzXPCIB377UMEMtHQfLRLSdAjVu\nBX0ekYiBGY35EU42d4pUs48C0hyhKQPfnPsQjez+jwVCJ6HbMPLWN5R3gGaC\n75YmEiFbakVjGB0QubcedDzF7WBzB8hrSKNeDsb5hXLSyIz8mUgdBIn16Eql\nxryQtRYPzZcLNJZ1z2Y9Up55qko6nH185/WxaATqDFIG96Ox86Eux9Z5xH+N\nHwXJyhe5q6q7VfddSxJMKc5qeN7rNLYj9NnM+9SR7lVFfokL0PUownW3Gbfi\n+cJ3ETzQiPBBDjANNSIn0gERRHCVLzYOdVAMQrDVhH6iZXAw048i+/nZls3c\n0nayL8bZ7EomQoxxDG669GKhMyWRSvBM1c1CvG9H55JlIjUD26joGT0o/yG6\npY9up2waX9lYdN50bSV4+GuKE2O5zj0lOU+7d8an0UXp1LpYCbYHwgnx2KIl\nmt87ovjwr53Os3G9mGEKvAO7iUHEdwLgWqFNB8O/fAqftkfECiSdvJ+i91vP\n2lH9sr2t+RoaAK5ucm5ezoQWRODTlPD2jhshIFI0gk8qOfnGy8b5wBG8dcdZ\nIZwb\r\n=0Pod\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 8.3"}, "gitHead": "9f0339c1c762e39f869f7df63e88470287728b93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^25.2.0", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^25.2.0", "graceful-fs": "^4.2.3", "jest-haste-map": "^25.2.0", "jest-regex-util": "^25.2.0", "realpath-native": "^2.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_25.2.0_1585159105647_0.35342896631833365", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.1": {"name": "@jest/transform", "version": "25.2.1-alpha.1", "license": "MIT", "_id": "@jest/transform@25.2.1-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "88b9eef776b857b992a79f1f1ede355e241679a6", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-25.2.1-alpha.1.tgz", "fileCount": 22, "integrity": "sha512-yaD2AnBtNCRlYrYLSzmHNKQRM14mTeQ7eiBI8mIIr1u2bjEoiCcqZM9v1x41vI/6ZJfPzRx9lU/kYa3iZ/90gg==", "signatures": [{"sig": "MEYCIQCRbAxZnrntvrjs3UERGnyHmIsUBlZLNryFjfeyoAwIXwIhAIENprRbMdhaqNNjiq9QOUEyzF9ZAnmlbT891XZ7scvc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefF+2CRA9TVsSAnZWagAAHMoP/3lRF6qvjJULvy91++xD\nAr1GcqxWXoMdt+HHKnY7K/S7RC1t4vedLivmBDHtCbHP1zJs2iq+qXDkCOkw\nPHs5kq3ptldPC4jSRo8ByRnu/O+OkPlsedGXhBg1XdTjy3IK8e8ktyPFziuC\nI3O9L3jICQW3jbnHGrEgHvp5odrT8XkWcJAMGVkgueHNswu8JtgpDXxDbAyO\ncqazZux/OYKq3XvcNcZdP/XdZ2AXwXBMrNLKP9gjGcuPCKRX1p/8Wg2RMIz2\nkHLKymMnzU5C7CnYiNgHDHlAmDDnLNdlv0fmYyMQIvC6NkUzLyVLEQ4QyfcW\nDG/qS4cXgy1TcmfMPcuGRQk1V6sffnig4cwrn4+FnVtqBzCk/fvDEjlJHFeC\nuEZxqECK2jcX4RE4Ms/kBsx6MwoUtVydNNfa5TreIl9sXC2e7rzlbnuwMIl+\nPPF+HjqKE+XgKdLgwsKJDmF99TWr5bE2jbC+Mv3xnKIHqjuzQsb6osmyIsNK\nQ7Si/2tme4+ttb3R7U7LUWuhe82RKhIxPOSb1H4y5+RvV2kQRUN8j5JHHNyq\nl7xl9zBn+de7MgbtKMk0v/TCaG6x1Z3ah7g1bCc0KvpovDjJiz4JrtCqjRZD\nbHW/E70juGWsgFZPwbeMrbz+2kuBByNHC1QywTkn3/tt1w1s67ilXx6+hGPw\ny9TB\r\n=s88P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 8.3"}, "gitHead": "5cc2ccdacb1b2433581222252e43cb5a1f6861a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^25.2.1-alpha.1+5cc2ccdac", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^25.2.1-alpha.1+5cc2ccdac", "graceful-fs": "^4.2.3", "jest-haste-map": "^25.2.1-alpha.1+5cc2ccdac", "jest-regex-util": "^25.2.1-alpha.1+5cc2ccdac", "realpath-native": "^2.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_25.2.1-alpha.1_1585209269811_0.17847374900732138", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.2": {"name": "@jest/transform", "version": "25.2.1-alpha.2", "license": "MIT", "_id": "@jest/transform@25.2.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7da69cce91b0f0dc23c48b7744f10ad5eb89d3c6", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-25.2.1-alpha.2.tgz", "fileCount": 32, "integrity": "sha512-NXlAiZRm29uIUytRuK7StOa6T0K9u0AJk2Zup3xD/maeFJgg+VDsrmwMRsmaRRRs0lxBY5y+vPy/Hhgq10ETaQ==", "signatures": [{"sig": "MEQCIBx/6joKHYjdnJfQyCMo6fgnmgGgArcNTQnnANrLXCUsAiBmvUH2XurQtQMuFyhQ3Rq+B//d3X3b/wCVGnTpDv/nig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56632, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefGN8CRA9TVsSAnZWagAAQzoP/3xXu7Cen7dZOxthaGgu\nY10b/UyRZ1ZcxEKwkPuUOALxGc1tRh41xvbu3dmmAaWbDBDfIljsnI+eUoeR\ntsdP3sMJkm0ELACqDIpVT6aF1Al2oa62EtjLHADFoLRLrsIemt4VKB1D1D+v\nSBlNEJWof60Jqn7ZCiwZx0oNjnMC6NoIf6uLRTLPN14+QXETwkeDQL6dMi7T\nBvsa7N9yL+sP2V+1ZMNOERFdEn4A1e0SVmGJqBv0KWoOXtyRl/95Dl0Z42DU\nnud+airSHg5qj+NUe4BJiW/5+iLwF8FIg/1yZrKW4bTBJWZc/CbmuDaqiyqK\nizKSGWdlRbr4TSOVxZlMHondgot4pX2gYAFxaHaottbe6nID599gkVNy3Cau\n40g1p20ymJqjf/I2y8ljY1r8mJBGN4DRdUEBWWRRbiBVWRwS/Oem1atPo8MS\nUVQ6kYvHGyWkgxg3A2lqIoWlDa0ot4ysXlNVPwNLg8eSdp1U/2TvfryeABDJ\n/++0kfQNy0dfRvp1tuquLfW7lZZ+g1LHRwNhMbqiVKTQv+q0hneyoxFv+NCz\n9eJg5WT15ceGFPkkBFoPxYD6GbQFqAtspexK8/3+PHSeXPCG5WIMURvR6BKe\n56KYJQp0KRePqkSVPI4TSTqX5Rdm9SHm0RJL0esN5jIWhJmmHcuX+/XNLRh/\nDFvu\r\n=yWf0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 8.3"}, "gitHead": "79b7ab67c63d3708f9689e25fbc0e8b0094bd019", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^25.2.1-alpha.2+79b7ab67c", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^25.2.1-alpha.2+79b7ab67c", "graceful-fs": "^4.2.3", "jest-haste-map": "^25.2.1-alpha.2+79b7ab67c", "jest-regex-util": "^25.2.1-alpha.2+79b7ab67c", "realpath-native": "^2.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_25.2.1-alpha.2_1585210236219_0.43254522825712116", "host": "s3://npm-registry-packages"}}, "25.2.1": {"name": "@jest/transform", "version": "25.2.1", "license": "MIT", "_id": "@jest/transform@25.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "08481795277b6ff9d7cb703eb4425ed46861bedc", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-25.2.1.tgz", "fileCount": 22, "integrity": "sha512-puoD5EfqPeZ5m0dV9l8+PMdOVdRjeWcaEjGkH+eG45l0nPJ2vRcxu8J6CRl/6nQ5ZTHgg7LuM9C6FauNpdRpUA==", "signatures": [{"sig": "MEUCIQDtOoE+sP2BxL/QTLfGldVzi1KA+y9k//OGm2mUNtzYuwIgUpBu9E8TFaqtur1i73zx9b+maPgC7KMUF5+TsfNZhEw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46982, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefG9iCRA9TVsSAnZWagAAjC8P/RFNR4s2Lzaqpj5+QjK+\n9uJlYPsw8PEQwbR9EAxq5l+/4fqy5iHrkjvD1S4XgW0VjjFPVrzyrRa8DI5d\n93Zug6iSKSO3cZ1T7C51DVPIVhT2sTBDysVfm5MGOQ/k4dm5Gkm8J481wHau\n7qxGb6D4BPtgBbY/ngW+95ArtwS6mN3sKfC2oCsSJRfSOPHCmxq6bbCOFZ9O\ndrYiVhEl/R4v+0IbmEIm2izMjuXTjDTHxayoYI7AGwCIciHycQAb5V6CUHZm\n3hVD7pp1KJ54PJD3IsYl+l0Hf2zYSG0LcSfL/K//tX+3FcY1uf932c+bgCYl\nMubwtn52QCcbpZ2sWNpKcm4kVrqwbzKkgb2wh7GzaAcVrAODY1zt/arZupig\n2zMoY4GD6+2d9uWOHRSyVaC1ibSJn6NHagkxyQNGrTPA9KKLA2z24sUXs9lY\n+f6VsiGzAvLbLZ3YzOEc50VWIr88mGwcs+WlHnNw2f1PFitmSgFk7R5069/5\nqi10UVEysKzOHomKYyrgQuD0XtIAk1+tOR8U9h5uaFUIiqmLO4jcodp4AVk3\nt+sdCTLp2Mm75Z32Zbh78iM1yM853vLxdikhMJWR5Y78Ec13DTkFg9f1UrzG\nEEDfpBxM+iD6OS4dT38PdMx/X3222jAdTTIIJ4O2XDZt2Ei6ycca00MGPa5p\nwDeN\r\n=dRS0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 8.3"}, "gitHead": "a679390828b6c30aeaa547d8c4dc9aed6531e357", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^25.2.1", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^25.2.1", "graceful-fs": "^4.2.3", "jest-haste-map": "^25.2.1", "jest-regex-util": "^25.2.1", "realpath-native": "^2.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_25.2.1_1585213282507_0.6363453195964786", "host": "s3://npm-registry-packages"}}, "25.2.3": {"name": "@jest/transform", "version": "25.2.3", "license": "MIT", "_id": "@jest/transform@25.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f090bdd91f54b867631a76959f2b2fc566534ffe", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-25.2.3.tgz", "fileCount": 22, "integrity": "sha512-w1nfAuYP4OAiEDprFkE/2iwU86jL/hK3j1ylMcYOA3my5VOHqX0oeBcBxS2fUKWse2V4izuO2jqes0yNTDMlzw==", "signatures": [{"sig": "MEYCIQDr+yy482tn+8aFYxDNSYNKo+5PeEJSl/r8WL1nbL/KNQIhAJUSJCR4hTtMNl9KFtpzad8FD1+AELgpxWourmXckMGu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47291, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefQ+aCRA9TVsSAnZWagAAQh4P+wXvM7o8JjsECayoFYrJ\nyXgLAaHkbxGcDvi9uMDvxkfbR3xRUXSxxetJtyW0fYZMf9oG3WCQ/22ahQ/I\nKJJpXxBRPf04V4B5pGlOqE+qfkuKvSrHsc+WwkkVhJnaXLLQNIgCuwE+5vNJ\n3X3R7qJ2BSf73bI+z+Xf4ok+pmen/hE5C0Ackn1VCBbtcApfuXW/5e4pyfBN\nsZ062Olbaq175wLZpgw2dvXkiNISqeQtVIAqzblQxot3AEwldfUUxd4rbMU9\n8pxp2LQpAvmHcANy6BHn2mDn9oCsszppHPvtYEJXgJVgwsBtTPYcN+5l/c+q\nyHU2Yjt23cDevAV8RasITUo7uItmsAvi07UGjbZWSqIhqBc2nRYyiLTwYJjZ\npMX9c48sGgeh0daAjJOax/2FsT82bLpoRmXul+sEx6IB+obw+enRfp5oJl7D\nFxFaRUGW7CCum3utZHYqAbieQmDHfpVfJohm6gOCD8geSgmRql0seMy0Te6A\n7EzzNz5HyxNN+opJn94+wBwZKZ+tzNQDHP2TC3uWnyYYL35Z/gncggzT7IfB\njantQPFpDmXQkp58atyJlGAOjHtCUx56y2MkKloCwKpaQjk3G88kuS2hPq9M\nY59xpUpKHPnT8kW4UD90TQm4piiDGaTxUtiasRv2kIvWpqCgSVZDnWeUJon9\nT4wc\r\n=yC1L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "6f8bf80c38567ba076ae979af2dedb42b285b2d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^25.2.3", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^25.2.3", "graceful-fs": "^4.2.3", "jest-haste-map": "^25.2.3", "jest-regex-util": "^25.2.1", "realpath-native": "^2.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_25.2.3_1585254298041_0.37899837005344184", "host": "s3://npm-registry-packages"}}, "25.2.4": {"name": "@jest/transform", "version": "25.2.4", "license": "MIT", "_id": "@jest/transform@25.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "34336f37f13f62f7d1f5b93d5d150ba9eb3e11b9", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-25.2.4.tgz", "fileCount": 22, "integrity": "sha512-6eRigvb+G6bs4kW5j1/y8wu4nCrmVuIe0epPBbiWaYlwawJ8yi1EIyK3d/btDqmBpN5GpN4YhR6iPPnDmkYdTA==", "signatures": [{"sig": "MEYCIQCK8vdZOIlrMBwqUAUXPSAYZCCLkexe0AaYoUmvJG8Y5gIhAJpO4Al6WFrsxOkh6ytzHmxOA/UFiLeFDg+NdRfR3OkM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegPk5CRA9TVsSAnZWagAA9vkP/0E/6cm+kTWgKfM/oZf9\n+fTIu7NTONUvy4ogaamKK77CrEZ5cZFerfqJnYZw6FkJhBBgVqFcgfN7CQK7\nQOH7Kw+pmT40JXkf50URCDoMO2ETk0MslVAfXRSCAAOMadaml8Rxn/C/b14a\nq2Yl8MbiUc6oles5i0eCdK5RVd9buJEFVHJEfo2v/RQeic7JPMoYMlr3hqjq\neub0ww/rtwzcM54SPPTWgDJEu7VpQ9IajioIs+WpRD9kRXo4Hu/TmuA5VBCx\n7WxTWVKBPwUl+iXhjINSH5SDDHiobf38fEYYSH82oQhLX+aNYUoMrYJoLFiC\nFq+HlVMtrvXtlvl1m3VrciVyIDiGaoET4XoG+Zef9ExPnpsfNPmIhJ4OYN1Y\nLMjSvVFWKLroSu9Qm1ASr6Pv4NFSXe7ekNfv2TMD21Cabg/pb1EKI6NEPLfh\n/vPVVO0SfnFaPlMRYrIYB0Y02ViKMMz/T5j17nrYi6VOP0My3F3/IrVJe28T\nrclvoZneFZbaDWExFXZXahPGUsKk9GrZHZZOkuFPtnkuvOIzGtyhBJOWuKLX\neJLGnS6Jh96jDZLKPPGSANX3P5tHTZzagvt/q4Qk26XDjF2NZ4lAq+Rm01/D\nUda13yg3jd45zb5O7KUnJd24PN9dz/zppZHnMIqV/IFqINnTcKTO6ne5TI0G\nAa8Z\r\n=2puU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "324938561c608e0e9dddc008e5dde1589d7abc68", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^25.2.3", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^25.2.3", "graceful-fs": "^4.2.3", "jest-haste-map": "^25.2.3", "jest-regex-util": "^25.2.1", "realpath-native": "^2.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_25.2.4_1585510712870_0.1436873992953469", "host": "s3://npm-registry-packages"}}, "25.2.6": {"name": "@jest/transform", "version": "25.2.6", "license": "MIT", "_id": "@jest/transform@25.2.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "007fd946dedf12d2a9eb5d4154faf1991d5f61ff", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-25.2.6.tgz", "fileCount": 22, "integrity": "sha512-rZnjCjZf9avPOf9q/w9RUZ9Uc29JmB53uIXNJmNz04QbDMD5cR/VjfikiMKajBsXe2vnFl5sJ4RTt+9HPicauQ==", "signatures": [{"sig": "MEQCIAbnEpBSlpG/N+JmLP+DHwNca4CTpBpVQhBDIW1tRsPgAiBPOezZ7AmF+RsCnx9j5rB43QnG6+1hWDXdrUdeio4xtg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehb6KCRA9TVsSAnZWagAAm4wP/29Nvl+ZljijM+0Fae1S\nlq+3c4vDZclXT89FdMn0de/g2mGWFSJ+rJyr2sJLZpyatB0t1F7Edl+DOh3V\nvE8gihWwsJ/PN3KSCJ/Ej6jgMc0RkIFwoNUzo7eb2tdsrdC6cpklF7mNYEfB\nxX87UrE36lONBWV1eianKiJEtdeCEB/Vc9JH+kyMb6r2olc8c8qNlKrxTBfv\nBXZaVciMM2h+qxoO5BWENtnaEyNiylTnS4hHnbM1HjWeO/iCA5q2zRl9mPQf\nExEx6IILf4LUCPa38vTh/L/rwnhPXwILJnT95qGTG0AWEHceqF0yRNAW4/jz\nsKevSmMIf78O4IHuMsqNY3Au8qxeYgsqaSUomFWqGlCCBMwOwucSnRt6xkqD\ns9pzFzA7IKyUUGRKl/qlqeZuXcQymXrRUZrNMGdSU/fTEpJKhh2jrzkDUJHj\nAWRngBjMAE4Zcm/aG6A7DFvmEbeGGo9BPJ4NoxIhOaS9m64Lqft5K20E37Q/\ncYpvGhg0t/m6VVoDqhlM52RfsuhERMp7kGT/FCbe/dw/1yTiVfBaR31Id6Nd\nN4LIVcL2hJg6aBfMxCV8+KxfKnemCHw3mCvjOiBPt33X6gFJStyVq+o8Mfgm\nr5xFDAlPJ08Xl4hVgTJgVJcIsFMSJvqYfJk4BtPIJcKFJ6iTZQRLA9wZKNCB\nnFsb\r\n=OVXI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "43207b743df164e9e58bd483dd9167b9084da18b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^25.2.6", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^25.2.6", "graceful-fs": "^4.2.3", "jest-haste-map": "^25.2.6", "jest-regex-util": "^25.2.6", "realpath-native": "^2.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_25.2.6_1585823370247_0.044884702672115084", "host": "s3://npm-registry-packages"}}, "25.3.0": {"name": "@jest/transform", "version": "25.3.0", "license": "MIT", "_id": "@jest/transform@25.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "083c5447d5307d9b9494d6968115b647460e71f1", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-25.3.0.tgz", "fileCount": 22, "integrity": "sha512-W01p8kTDvvEX6kd0tJc7Y5VdYyFaKwNWy1HQz6Jqlhu48z/8Gxp+yFCDVj+H8Rc7ezl3Mg0hDaGuFVkmHOqirg==", "signatures": [{"sig": "MEUCIDY8pMS2iy4VEjDc7I9W6RkoOQa/Mz3tI82mNPd9T7cKAiEAlD0oj6TR4MyxYtAhS5w3H4iidbVk7teczo5dJFC20BQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47349, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejc/VCRA9TVsSAnZWagAAA0QP/0p8jm6l1kRnzY6U4PI8\n51fkO/RtOi2Rb9TWtU1n+Is+EdW4XDpoGZHMepTL90s4GAeRvCFTVqcYWCkw\nNWOrOpYgZZHTmiMsC/bcrn+hvlsuZNOpiG4qdat47uG2cWZsDxic/Ym6iGuU\nlPPJesqVM97ALZJ8s11AJ1hjS6uDwiwIE439GuPP+N8sPXMmuuryu6ysrf0b\nvOvi4Jy3FhmPRCRrrJKTfoEqnIq6vHeFxJojm1i55+lje7Y1V2DR0W5UGi8B\ndks7/i+axre1ndjiKge2O+b5atbREw+jAdz9YVr/QzZMzpQITbYjf6TYle+d\n6RPgXlOlX7evYorJuHP3RrnLQzZCzyY83E0908q/88GypZ1/Ci3Hq5U2FMyL\noUYf3Ebl9wiU9QIRAfKKpnOCNiMa4mk25PKBfIL0295b89Esa7ku/vKOJLhL\n7x4w3gtHnwPo8zTwQXtk3hlSqkG75fPz0s7jSqN/ups4gUgdtHIEyb+luEwb\nJqAnscPYbwvOU4+cIJzPgEFUAXkCkdHPA1TbNEAuMDWHGYzEyGCNmj+pO3Fn\nIWVO7GDRPToIvOl2TfmbP9bCOlTuOoC+PuviZSqmrR3rwukf+QtWPR3c9WY7\no0fSZBjGllroCgHy0AqyUzXxllS/dCWRag0TrgvGJVbuGezVT1Or9+Wth6d6\nDpcF\r\n=9L+/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "45a4936d96d74cdee6b91122a51a556e3ebe6dc8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^25.3.0", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^25.3.0", "graceful-fs": "^4.2.3", "jest-haste-map": "^25.3.0", "jest-regex-util": "^25.2.6", "realpath-native": "^2.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_25.3.0_1586352084902_0.7937223224321708", "host": "s3://npm-registry-packages"}}, "25.4.0": {"name": "@jest/transform", "version": "25.4.0", "license": "MIT", "_id": "@jest/transform@25.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "eef36f0367d639e2fd93dccd758550377fbb9962", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-25.4.0.tgz", "fileCount": 17, "integrity": "sha512-t1w2S6V1sk++1HHsxboWxPEuSpN8pxEvNrZN+Ud/knkROWtf8LeUmz73A4ezE8476a5AM00IZr9a8FO9x1+j3g==", "signatures": [{"sig": "MEUCIBuqQfjS6uId9QwYif7PbhO/vZUyM5d/p+k7R26YKQagAiEAmMpn7rsnyHFqPMQg6MbLjtRLZrOIbdRebGLy1JnLAv0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42838, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenMeqCRA9TVsSAnZWagAAN1oQAJJbMD+vopx9e9iRp0SL\nJkQNZKiK6bz8D2U5E9dWy/3YVpK3Lq/YHIpiqlQ6OinkRlobVmkOvgnVHFiI\nQk9a3wXz4bnE0cktPtgBjU92NfXvYmV/YHUKuG2sqCOxfuFFFawL12tNs8kp\nz/kYCs2E9oOdpQtzUOuEpoUmxgYLXGe69V/WFhkiCtyBuEO5jGP9DNxFTIo8\nKDyD9E44qUYaVS6XawTCR0jAk81MPxgT58F5dp1w7vRFQaGwCokUDG/f1NNU\n3Figt8wwt+PMThT7qhFhEbxLzPU0MbkfGGhXO5hEdMFH0n5t4lSJDgLNoyse\n0N5Ice5DkXMpWfc7uHKUxLV8+hhB86F+XMPuknyXgdoCGvsdWlOajLsdExlE\ntyiDNy/Ymj1K+QkD5VjD55vl1x4gRRng39djkSIeP9unetnWG8ixu4OCSayH\nK5pGlNSzuH+fXGCkHy3n6Oor8Pgxk804rl7BODYqhOhXbO0qzxcxPZ5FBGMD\nG5IaVQ/y8/nThj7lIWFk9+GGuYjdWlFQX14ylO3KGhGJPwShl7ynd5y0L5Rf\nP8o3prXAp4ekT1211oRLfqSTUhRp0x9uG3kSycFocSRfe/2VEP/zz+gDqpG0\nw2MJ6G5sZgCqSYIRzYKBq0Jz+N97W1IvZkBXGzjvdf6iYg6V6DrZYSscZta9\nRNcz\r\n=WrVF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5b129d714cadb818be28afbe313cbeae8fbb1dde", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^25.4.0", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^25.4.0", "graceful-fs": "^4.2.3", "jest-haste-map": "^25.4.0", "jest-regex-util": "^25.2.6", "realpath-native": "^2.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_25.4.0_1587333034538_0.9939888638031555", "host": "s3://npm-registry-packages"}}, "25.5.0": {"name": "@jest/transform", "version": "25.5.0", "license": "MIT", "_id": "@jest/transform@25.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a0f3719a1e158be442a99988d3134d577cb1c1be", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-25.5.0.tgz", "fileCount": 17, "integrity": "sha512-yqxpmosig2JWKHVbyEl8I7btXCinLIL8b3ENJYMvl9TqzZ9KulnV2t08wp4Wbv/pFKYqTd5NWyEzi4xCpcQ3mg==", "signatures": [{"sig": "MEYCIQDm2iIfUolutuBgNjIz4u1k6AyNtyBCTRHgMQn3u5u2yAIhAJO9vi9DcCTRLx9xyxdPDm4NAs4fT2DTQcQRpiuiS6/K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43764, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqIfgCRA9TVsSAnZWagAAk9IP/jbX5e9A1s+DJWM1hHGb\nw9ggZ8+FiTxaNgXp7WTQJX0C3hWAazkxw4ocsuvy+RVMDSE2ZQirHdtIA0XN\nr8tJx+U653yxh1fgHFmYsJ6wc40Erx+VSrNdUfi/ODjtexQdL8DtVVZ6uhdx\nlTGPKz8Uxi7QGeltZg2TOO5CzG+FyRSpM72aflA4tQU5+sYafQPQ/OBDW5j0\nkIxIR+y8VDmiS7YFkxumKUpf7TpDnx0/1nIl1zJ2KrlXNGdgvJ5zXcMAIzR6\n2PPOFjhZmkrMhmNYXOBbR0MVNtBgwbcWJYkCdjPketrD6DZ5SC0lOM8Ytnx2\npkAD02PH8rKgKmhJZuNM0EE4oXYSnXeUqpAqpRVh4DXRPSRM7Lma2pjFUCET\nkeoakiLz7l9Dw9YZy2a+B0WMIZlxDPg4bXWwRlncOzEZcfA9JUsQ2ELpdnM2\nW2srZedSj224nM4QC5l4+FuE+t4QUh1st1BycE7XrfuZ/zyag1H3M1btUBhV\nZ+QyA3wKT3mMkf4mXbQiV3mpl6Q11Nmi9ce9Z9bvF+ElcDEoSKz94mj9Zoag\nLqqyC6udurcVwkQkVjklc4LYmR4soRd8zZDcSBLyh9PhDtpk3bfpc2haCEJM\ngs+mSwFfmgsREJlgNnc637u7uGpv3w/dq1e/zVNqPAssKNlo7iCQe16t6VvU\nEevT\r\n=T/cm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "ddd73d18adfb982b9b0d94bad7d41c9f78567ca7", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^25.5.0", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^25.5.0", "graceful-fs": "^4.2.4", "jest-haste-map": "^25.5.0", "jest-regex-util": "^25.2.6", "realpath-native": "^2.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_25.5.0_1588103135842_0.36424714370742395", "host": "s3://npm-registry-packages"}}, "25.5.1": {"name": "@jest/transform", "version": "25.5.1", "license": "MIT", "_id": "@jest/transform@25.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0469ddc17699dd2bf985db55fa0fb9309f5c2db3", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-25.5.1.tgz", "fileCount": 17, "integrity": "sha512-Y8CEoVwXb4QwA6Y/9uDkn0Xfz0finGkieuV0xkdF9UtZGJeLukD5nLkaVrVsODB1ojRWlaoD0AJZpVHCSnJEvg==", "signatures": [{"sig": "MEUCICRrUU3Hd7AQsj3RBvoKfipjPOehgYNj5y0cj+PLpJsOAiEApL/YAcBGthi/8r1pgtLcNTkFU3LrWgiSFXFYl59Mv48=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43764, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqV0PCRA9TVsSAnZWagAAPvUP/0GaeGFCQNWMgaSQmu25\nkoDvBHIM1FO/NLtGFl1EnMR4mgjOR8IugzPbp/h6rAGSQJkqqknCf8kTQtoX\nOKtdfhwaZMAwxHApcgczBRQiS6Irxj8YjrLmZhDoOL8GiVBtop2LcuCa6LC6\nDwAfmT4Zx2nArKjx2+DPkNHvp5wZ9/t8fEfD4/aWGB1Bqq8w9c0zCdeWJ8Oh\nQ/SUF8poVx6bZpdIL1z1nnM5JUTr7duyWGyJDCK/B9qXZjgKzYhDudqDJ9tT\nPJDZUFpi+7dCx9vnvLa7fPMvkG2pasjYMYyol6RZIFkywkbl/urauxIvHnzR\nS113/fN7JFWPrOhqdGMa9LZDVtzz8ni6C9mQ9fQImyhjYyll1+XC0FNegrxc\niHa+/OrHPN68pppg1OjH/HLtTwjxjovFSO7UYwO+yQjcEOTUJD62fLnR9NCi\nOtYJ6a1F0Xmt1c/V+nJyT4SCiZ9lLhKdVyDhPRa8n6nN3le35FjkL5CrzScB\nDEVhy0bmUsOCi2z8Zu1WtHVIxqEXTtlyQwYvW0F9Cvztph+9NxdxFbIrGTlm\nc/sNxCL5s2ZPoHSRjwyFfk2wmpZ3F/61HBonU2LSVSeUv6i32AXXWlHsZ5Ro\nYoYLZ/lc5CPREff1xzwJeYalKKnv5fEpkH9QLmKzTrYoZ5OoBTOPu85kT7Es\nN4Rt\r\n=cA7D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "c5f2fd756331895b8177a19304feb49657687e22", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"chalk": "^3.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^25.5.0", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^25.5.0", "graceful-fs": "^4.2.4", "jest-haste-map": "^25.5.1", "jest-regex-util": "^25.2.6", "realpath-native": "^2.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_25.5.1_1588157710876_0.1518271132367348", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.0": {"name": "@jest/transform", "version": "26.0.0-alpha.0", "license": "MIT", "_id": "@jest/transform@26.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "68a3b812e5922d0a03c7396bf15913f660ba7ba0", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-26.0.0-alpha.0.tgz", "fileCount": 12, "integrity": "sha512-Mz39lsOLTBpNWFTHoY39WmJVLGU6YCj1icsU6+KbiQ7RdiMUQOs+LUQllnhU06TqGQ5gnT8uTlWq1TXn8dnTgA==", "signatures": [{"sig": "MEUCIGdUmgPcnWwcazBpfS6KryuKPSHV92QLYBRKYgOzBcghAiEAlkgReVX1DOi6HYrsNHMQQABvxX4G59mfwc56RlsU1Pg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38545, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerWPcCRA9TVsSAnZWagAACdgQAJoF6JPR51B5lPj/iKWE\n4DnmqtvRFa969bDLxvmQu7iS1NGjEMVuFOm2YH5tS4y0IpXMik4zhEohO63G\nWNwW92L/cSCHTxU1vCuJxKK2ts9zm7hTpgjgG4pnqNE0k9c6d9eVwwnPS78f\nIQNEKDe+z+rnc2xImzqFJkQPGXSVtjjiRfX9l14Vsi9DU4bN8nkzPVugeDFX\nzUF7LmEtVW5Tl4EVMtAvhsrbXiyjl1mQWd6f7tu+O4CcChicHmDGL6TtrUgh\nyor/5eruwceCrVWdYsEdoPCmd2CWryX1qQtZK53lKQMBiylwC3fCKBGuouc7\npE2DQToc4B+NmFDl+52ZdQ8XDKqr/COub9j/WrgmSP2HP53WA54xIJpXbhin\nz1cqinzhUm21tV/kVZA15Ub5e2iV9lUxxoE0fXXreHNDNUQXwrw6Hgz1BeUI\n4Z4CkUIvprAOPVQoUgLjKCvP/mYgPJp5yDJF90R4W+COrNLuqqDH7XsQBn4+\no2Wyjgph0gYgS1Q3lLijxUtogZTnprJ84s24lpxXsVeAiYtHujdFxHELPrbi\nHQ6wyHmuDsfjdaFpYhrH6WgAHYdHifpC8ki8YC6hz0VQJBu/XdEF6d3W4UFB\ni7kjZRtuwhaEMYM24RQZzkBkIWF2MZeBQno2TSg4VkmZ5DmbDuvyQ+AhmF/A\nNOfC\r\n=KEnC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "ba962e7e9669a4a2f723c2536c97462c8ddfff2d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^26.0.0-alpha.0", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^26.0.0-alpha.0", "graceful-fs": "^4.2.4", "jest-haste-map": "^26.0.0-alpha.0", "jest-regex-util": "^26.0.0-alpha.0", "realpath-native": "^2.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_26.0.0-alpha.0_1588421595995_0.49707301625594336", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.1": {"name": "@jest/transform", "version": "26.0.0-alpha.1", "license": "MIT", "_id": "@jest/transform@26.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "eb92f5d4950eecf8fe62d082774a021a4d2b07f3", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-26.0.0-alpha.1.tgz", "fileCount": 12, "integrity": "sha512-qnBzZsdTF6IY4NXPRRzo3jyAFaV9uKa5VQxJ/MK7tdTWoMC0Ck1WdmRP8XuVL/jk7OfYord8sD6J1YHsD/nc2g==", "signatures": [{"sig": "MEUCIFVyKxxCkOdL6pZbv6Z3rs9m1EBxskxVIybj0eQYtzT+AiEAxC5Q2QIMHciRChYivH5FP5csIoXRLt0DW4vAnRn3rkg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38194, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerxHtCRA9TVsSAnZWagAApAgQAJXYfLBlKh6SVEZRxqaM\nqve/h6GhIvvyt81vwGfD54NdN9ZQ1DO1tT+fO5FYiSFTZWn/EIVIUTe5KYVJ\n11rbMBjJxMn2YHo9WKlYIU3R4+OZNCyQIgJNK904sI9VWlj4Y16oSXXHV4Zb\ncMlxmAYIatCrwucArlU31BI5XNefcZJveGIaPty3N09BmiR+lLEt90/YLhUW\nC3QOKNSMs3UIQ5BufwM/XrK1vVZCU64Yb8Y8OT8y9CH0ch+osiTxBctloyIa\nkgAkvmoJkiWKUMv9156JboO9wY+K9ENiNUuRn6tRscC+Sqcn+qVz4rgjb28U\nAheoRo2/xx72Pa6o5uMwrH0hIqnZHNo+MunmZh2fUAIGyCfD4/HAXin2z/xZ\ns8PCyADbgNpwct0ll+86EIDeJ5esddM6d9nFVW99w4sQ5ukDUJGAoTDZvb3D\nXdkXGxThIPj+V0hB8W6OfTfXoxv2znuNxVW9Edy7joOQRTx1FA9QgB3rQV5S\nPgZtAUIYBRmGoZv6PUjKh+mwyg7KoeKXhhHQvQJDJUmXg3CjhtP4yPGOYGlZ\nTXKDC/z7e9xMcYiNEsbAJUK7DyKLeOz+YQKJCAu+7BOFdjIzyme7vNvbwCZg\n7MBJ0cVhBLzQ/0EGbSGt3cZf1c+HIEI4PWY0wmNV0TL+OpMmdrN/+zILX2iz\nmVwL\r\n=cAki\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "2bac04ffb8e533d12a072998da5c3751a41b796f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^26.0.0-alpha.1", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^26.0.0-alpha.1", "graceful-fs": "^4.2.4", "jest-haste-map": "^26.0.0-alpha.1", "jest-regex-util": "^26.0.0-alpha.1", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_26.0.0-alpha.1_1588531693581_0.5913240983770516", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.2": {"name": "@jest/transform", "version": "26.0.0-alpha.2", "license": "MIT", "_id": "@jest/transform@26.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "27cbe12b10b8c2af37a7c1f940e2aa05074eab82", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-26.0.0-alpha.2.tgz", "fileCount": 12, "integrity": "sha512-T0yUnMnYj93NN/IjfjS4O7kmq1DMjAJ5LMmpjgHCMQv23lGPlNYZrvGhoTghzcWelw1RnsslY9D7mB4t75lLpA==", "signatures": [{"sig": "MEUCIBjR5GNLa72iL3CV3JLlqENHaIR0oV7VrLnXKg+yKYvtAiEA9sIeVE0Bp5LUoAR/Q0KuCfyV1FbMJeukwM2BLRXwB+0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38194, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesD1QCRA9TVsSAnZWagAAxLYP/iGI6yBBmHvrVEEFJrfH\nyNCG7JwfAZkXfI42l+3xW+ZCVcYIFvp00sgF6pqCPXNWnbnVxvTWLoXEShED\nYVcyiaQwUh/MFzSeaebPu4N6oWRAPgTKstEwjQI0Lx/LYYqFsWVVIQZjYM/Z\n9UuSNx/78JaUXi2wM3tggUncQX1tPSu20Suka1C4vRxRVqMFuhc1ywrDV39o\nGj2ZvjclgsiGdMdu/PDiahnh8UEzYjLWkCq2L002KPBOvoP5ZD7CDUFrHsHA\naGFWM7aG99svQuQO4p9VRdFeN3YTajQtPcpnAahT+vnNTZeKvTg/Oort5bLa\nGjH41unrReL2hEfr/GiFka+ExmZzV23rq58DfqpalehenY2rbMEwf3Tm1sjY\nafHHSpXKyDr0D/9hkT2yQ1IcO5rSgycP3JIU7Eul8JIWMXUiDEjYXC+VU+F/\nZDbjmd8BoNjAiCqJM8kbcZUvKMGHhHrl6kftsdfCkKP3LxDDQdNh06Qj+pRh\nXjGayopnqk63w/3iOUksGYmLNu7v73WVtz1cgh/xl/FaSd/HuOREunWc9/P5\n4v4I5cX+GBG5tOk+LtkJV7HvGQfENRf7cr9ghQDfmEksqk2SL6Seoe+J7zDS\n2btcsuKrzQjjJB+tvh9owOgctmfK4VcK/s2A/1Z2Pt64e+isiNSJ2L/8pR9v\n7iqt\r\n=15z1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68b65afc97688bd5b0b433f8f585da57dcd1d418", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^26.0.0-alpha.2", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^26.0.0-alpha.2", "graceful-fs": "^4.2.4", "jest-haste-map": "^26.0.0-alpha.2", "jest-regex-util": "^26.0.0-alpha.1", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_26.0.0-alpha.2_1588608336153_0.8633634919480577", "host": "s3://npm-registry-packages"}}, "26.0.0": {"name": "@jest/transform", "version": "26.0.0", "license": "MIT", "_id": "@jest/transform@26.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "89ec1bb1056155f17811f53b2165f977ef50e457", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-26.0.0.tgz", "fileCount": 12, "integrity": "sha512-HrHIC8OWfBdmS+To8rQ7HaC2y96Uj+1I5pvkAL1hd4gPYXoooHYWBsVKp1eWsWVDeCUBp1iUYgfGCiRgcW9nzw==", "signatures": [{"sig": "MEUCIAtms6qzxl+QvUOjE67bwuj/qlasUufuNvmNO40Pjek7AiEA6fdXkdjKSVTCqbVYZSXt9TzeY1D2h/GxvbuWu6S+ZG4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesFaRCRA9TVsSAnZWagAAGA0P/2pZG9d0pCLSu5J1x+DW\nzRneWwv41QdwAkS4eYwD4tdLUbJ0IdQCwH9uDQJwLTLaHLVs/9yk0S+VzzcN\nU6Om85icSGn8wnbCA8v3hi1pxzx5uJ+U9AVbhu8xnv0bELdiAQbLZFKt80BR\nwDXwoYdvnQm2GhNfZx8UdPpFn04JRM+gQvMVA/Qow6feL/RqEyxvhGyu7UH0\nNrrzpcGotD2JHD4XfCB4lO5Nas9XIF57y6S8WQGYkDR/FlItuSdBbT27kS9O\ntWVVxGMpWZx8Ncal7yPN/bXi2ohhcgc1CueqGR26jYztEHReUum+VnxccZ2F\np3tyb0Dgs3hhx2FT1VgvE0YZ1VBo4aWrrORndwRmXM7JQh7I9cej603hCfAK\nbibt8OQBDHAhliLBYkfeQlxwIL7QEjVOpmwmVzIdpE5mAKKAuMwMKltUtx+v\njUIXzBOo/8jWk3Jiap+3Havg0N1GMISWpdebPZ4Z0ytns3olOV4dxOH7jkRJ\nYXMrVt3U67ntqRdgi+RPrYUUad7xGlxltrAWM8HEepRUaFwiL2Y5tN0A5EV1\ncYxadaIE65EOwvhiPdCc2p8Q32mzjVpHTSnBM3Sm8yZVay+esqsgBgYAbC0P\nOct0woe223TXTY2Vd8nWII9yuASPFS660fEsrdl90JrROhzVAdholhabnbU3\nhkPV\r\n=hOt7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "343532a21f640ac2709c4076eef57e52279542e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^26.0.0", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^26.0.0", "graceful-fs": "^4.2.4", "jest-haste-map": "^26.0.0", "jest-regex-util": "^26.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_26.0.0_1588614799571_0.6641632282426371", "host": "s3://npm-registry-packages"}}, "26.0.1-alpha.0": {"name": "@jest/transform", "version": "26.0.1-alpha.0", "license": "MIT", "_id": "@jest/transform@26.0.1-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f6283ba2e767d488c8f83adcf93a2bf9840683c7", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-26.0.1-alpha.0.tgz", "fileCount": 12, "integrity": "sha512-H8kJVzJWBCZ4pv6KF/GDAUFqE7RFaEdji12wLskwL1ZZG6+9Qy0cHtsWgwLK+/DYveuCD3SQXxrjU46dBXN/Ng==", "signatures": [{"sig": "MEUCIQDhNMLzqAzCQFgqnntClJwas4VYFFJr0Rh2FJzJw1+3hAIgai4oaCZdz/JE6vy6mthC0KNzyRP5sZAy+73GWTnuv5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38186, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesJQkCRA9TVsSAnZWagAAN9MQAJ1cmtafCGwoU94CViVl\nbP/e+9L71cGCpiGaA+znSQr6MJFe2Q232bKwnYdijvNg0V9NLPL0bThyvAGu\nWRi0OP5yCnRqsirJ8a6LcFWss2BXkH9mtDQrCrMAuxtQWItO7Aj72TRqkhQg\nkT8B428DPEzGBLqpLsHmw2V5gTuw7KKzyRAf6V9FzhuFswSYzSoS4Rqk79zI\nahCvZs9ecRHgWX3KTT9HkNXwQEvmdh208FC8ZqcEXwH8JCgt5FHpiXTHFtD3\n/zIjsIXfMP/TJqQOBqYab/wwcZc8CgitDH7lO2JhI3Sj4h+P3S8dyv3pNf1g\nQvVhJ3ta7ITlyBvyM7Z8wPnZGjTaQhNXleIVIKLjCaM21Wcgv1/5zfy7dumY\n1z7TmBbBCIClT9Di509IjKL0V3tOEThMzaAcLetdZQfnLIgewvgewlrT7yRh\n/iHiV67ET3vUkDLr/ectgmG00C3Gr/Gs1//NTGcbgggzlWkN9Ow3chT7rW6U\n+O+fH0bb1JTb3UUgJWbCiL70tOpdwqVjvVdknDXMXYVTFYSOPO65ZWcsFlYH\noVb6tSu0wmnStks4nJPpk4xAI24n0wnyd/3ccSmb6Pc0A0KQjQajfR6gp1YV\nAzrSQcIHXGIxaKFR9rEFiLsP0igzPh3Le1LhV5K5M7MMopgY6AIlrDOBs0nd\n5Hf9\r\n=fbZj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "fb04716adb223ce2da1e6bb2b4ce7c011bad1807", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^26.0.1-alpha.0", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^26.0.1-alpha.0", "graceful-fs": "^4.2.4", "jest-haste-map": "^26.0.1-alpha.0", "jest-regex-util": "^26.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_26.0.1-alpha.0_1588630563956_0.15181014294215012", "host": "s3://npm-registry-packages"}}, "26.0.1": {"name": "@jest/transform", "version": "26.0.1", "license": "MIT", "_id": "@jest/transform@26.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0e3ecbb34a11cd4b2080ed0a9c4856cf0ceb0639", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-26.0.1.tgz", "fileCount": 12, "integrity": "sha512-pPRkVkAQ91drKGbzCfDOoHN838+FSbYaEAvBXvKuWeeRRUD8FjwXkqfUNUZL6Ke48aA/1cqq/Ni7kVMCoqagWA==", "signatures": [{"sig": "MEYCIQCQXAp45pnW32GH8VwPylA00nNiB9cV49f08f61A958+AIhAOwNN7EsUqaBuNS6dguzeFH2voRR56nv7CHRlP5ii361", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesUK7CRA9TVsSAnZWagAASCMP/ikH4Lq5dNqe4xoKukcf\nlSmBNMS7DaJh7ttV+TLCbZz5bBRVGsM38wXTdiW9uNABtOVhlAp8KHbwwo3a\nxigDG59m40bFdMfgGaX+NJ4pFOGrfA/MriOPkxVHydoURptuvzQyRdLwaCbK\n6/9FqCeUSFBnkS1F7J3Ntyt97ni7qU22wl8c9WeKq6zH3hgobBhmjOVEZVAi\n9f+01MbYeUOZqA1YaYtjAshcC/O1fJirV8JekO23+crGBtnPNg0Wz0ueSdGs\n46LDXVCnK/PBwEDYCpvdBvCVMYLiE87E9MxTkvKH77AGQ83YxI7iW26fFvjy\nKNZ/uyn9oQWefIyg1UnYwTHRTmUfSoHIm3yc/LlpjHpfk+zj1qfV0/FAkGNT\n8pXhMHtIrBYgzv79MZjCu62IdxLI4C0RegwmhkpShDFspn7Hr2RQe+FF/iC1\noRKBzdF4mUODgZwI3GCTBJ0Sa07S1F+cvBM74P2FVvcs1QYzvytynaI6aEPJ\nF4oLndBqxlmNqgqItNbSF6hZxaI/sCSjonCMjgz1qO5hHlT1x2djF46TnfiH\nATVr7uSbJfe6Upt+8HuZ6M4mnovJEaD+DNNnc07NLrWcecZYW/l2tauE51VU\nOmgQwLNeUTwCAQ04f3xnmt35RnhSrEcytPhdmMQ02JdiGB98MX4Ws8H051Ut\nh8bp\r\n=vb8f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "40b8e1e157c9981dda5a68d73fff647e80fc9f5c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^26.0.1", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^26.0.1", "graceful-fs": "^4.2.4", "jest-haste-map": "^26.0.1", "jest-regex-util": "^26.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_26.0.1_1588675259095_0.9217487881661415", "host": "s3://npm-registry-packages"}}, "26.1.0": {"name": "@jest/transform", "version": "26.1.0", "license": "MIT", "_id": "@jest/transform@26.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "697f48898c2a2787c9b4cb71d09d7e617464e509", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-26.1.0.tgz", "fileCount": 12, "integrity": "sha512-ICPm6sUXmZJieq45ix28k0s+d/z2E8CHDsq+WwtWI6kW8m7I8kPqarSEcUN86entHQ570ZBRci5OWaKL0wlAWw==", "signatures": [{"sig": "MEMCICsBFxlqv/rRAlG53qNkP12oWWpApgiSrQhO84rplkSXAh9vZNdqqTCQd+AL1Ou58rSkVWPg0slha+/zoF04gHN4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8hyNCRA9TVsSAnZWagAAXsoP/RxxYvQxydtzUeKi73J+\nFwtACLL5KuO+i0yf/0/IiSiVpU87JlIpyvn7pGFOjuzScjzKWJz2i9+wVpiV\nynUHw+BkJGRK2Ur+kP3CCZ754UBYb2v164+45V5gD8EifXCF+o4VU5MimaXA\nMb3JJSlyPrdtcciaV2kppb0m7bwjXnTuG38p8A+ix8qwFKy4SFioEJghGDLx\n2Yf86DYVrhVIkZfhrcIXrDvFvH0XhiqGLaFa1QaqfV4XjjAH2VhEQM223yRS\nFgJW3NkebHZGpDyVRAd36uurwTt9ffDfgjcoKPwUEFn7RbGncSA8yq4wGeWW\nVcQ3SLv3nMh0zAvJAcujEahlxAXI1oDrsT9V79qT+BBvMhgh1jEdVMWXydGc\nUrkpyNrdTJggEunK5RQz6X5fcT7Y9I2apJ0VCh3rr7PbSAWPZKOvp0O120xT\nGc9P1RMKelRqB9jUY/DmwGuA0VvHSm827qbE/h34u0WOVz6q79MrN5d8b1WB\n2gHzJvF/Rcl6+SsLDN/I48q4BAwftxhGGjs2eGJxXlhyoRb0VxSH0sVhpPiA\nwiWwsKiKtYCCO+7+IwgG8LQA33iP2WkgujG90kMt2gCiz5yPerY9aEy+jJeD\nowL3C9hreM2MvTzxWNibbQmu7fbSqCDqwKzJnpcP/v8+CoAhLx2XtSyd+3uB\nv5ZY\r\n=5P1H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "817d8b6aca845dd4fcfd7f8316293e69f3a116c5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^26.1.0", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^26.1.0", "graceful-fs": "^4.2.4", "jest-haste-map": "^26.1.0", "jest-regex-util": "^26.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_26.1.0_1592925324594_0.908598100955494", "host": "s3://npm-registry-packages"}}, "26.2.0": {"name": "@jest/transform", "version": "26.2.0", "license": "MIT", "_id": "@jest/transform@26.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9395dea95d6be4bddc4e74f7993a6c14f54bb4e8", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-26.2.0.tgz", "fileCount": 12, "integrity": "sha512-6uh4QiC5ldGKpTCbSGHMgiFA4AzzjFvcZIYPah62gO1PxD7zAzOyjTpKi9S1Qw3p23kwQbb+zRjFBNjCdfvn3g==", "signatures": [{"sig": "MEUCIHJ1YXLt86gOndsFFwlLdq6vnXX/kxMLKQdDllFYdLpuAiEAo7gYmDGy8TXSULhpq24MdgHPRM8lgIMsxsNyLYUBIuA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIpzrCRA9TVsSAnZWagAAGyIP/385fFBwEgpwBLURqahp\nm2MstPngu8CCorRvZcMBtuuVE2Nxauv6SgU4smzo7xN3N6RICWKO6qfvZAMB\nBNYqpqaqzWxj4vQP8RJ5Peus2CTOvvvIcS4JtmIkyhgkb0vdEnROi5cQKBQY\nygEGhQfqhj7HV8dEB0jcOXiVwYXhNd5HNgu93KE6ziKg9CInUoqZ3tSDDQCs\nMAn/cXK9ixSuC8iUi4QfZTME/z2KgXxCQB+DjSf0SnXTl0IzHKNrDU8erRRT\nvWAuGfOOQ35TncfZ7wABtAdAucg9pJKeGt4MasAS5MbjLYCCsBj7JKTdZz0p\n6DFA3diHb3tJ7S9GNNsf5zdxEHMNQ32NHdRFuRZZT7fpOM6W2oNNATv1ThJV\n1QL+GI2t6ezRfzJWsQrkfNeeLP/Z59tW2SmiG1kHVzr+hsifA0VGLtpjIUbj\n5EGKRkjEXcsYyi8uYXSwashBTL8zJ3CIXl2aMbX6LUx7329edwdU/eoMRTRP\nXCizeFRnj9uiUwuD1P+pUDs7i9kA0fvolBr6/wr4XZmqIIgK0oEBT4PBzPpH\nUS5RWS4Ic4vU9pcuqBs0flD3IqkukseqISyy5gSkO+qYndTXyR5JMTqm9RA0\nz/T6mZOBgpK8qk4WxUGHPt5Bey5HNRSUnFmi5VLjqBFtMoaShG6aMg8HF3kw\neeVz\r\n=ZcNq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4a716811a309dae135b780a87dc1647b285800eb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^26.2.0", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^26.2.0", "graceful-fs": "^4.2.4", "jest-haste-map": "^26.2.0", "jest-regex-util": "^26.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_26.2.0_1596103914638_0.5876231551129778", "host": "s3://npm-registry-packages"}}, "26.2.1": {"name": "@jest/transform", "version": "26.2.1", "license": "MIT", "_id": "@jest/transform@26.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "11b435660137852f14bfe10155ce111ed2135a14", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-26.2.1.tgz", "fileCount": 12, "integrity": "sha512-lwPHjT9tIHB0B66/FEv4lr4+GPIT0/0RPOFEBzk7NsnrAKoemelTVyDI99x1f6Dh3juyQ5kK9z8cAyCstkotOQ==", "signatures": [{"sig": "MEQCHwIP9FN+6K8vlYzeqY5YeViDJgFEq2RjAw/5Hv/tHIICIQDVaIEV3QWBKykb+vTJ2vGDI01G+sIa2gGFOtPc/aSOqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIrCKCRA9TVsSAnZWagAATqAP/2pGbZbc57BQIJNIE9c7\n29/WxQgQ+slbgRuW54OqtkS55c5ozN81YorNSOkNscKZhZwo/OK63tvRa0gz\nLbrJoPlLpxzz41O57T/sQuB9JWwpxU2Gre2lglH9cdUs+Y/Cgc4XtYWHgJgp\nVQIjXzGhAUjuMCGCLpge7Z70k2PuUGzS+rVih4b++IDfZjJWa+3Ux4ivKbn4\nq78peC5gER9QUUHGMjnXJa+0oSncC98LG3f1Pp2B5eA+Vgw+ecTEAad9vLoo\n9fGn2O9cliv9gxiLY4rMveM7rppRz0wOps1zoJYbyTiwUfMZ0jBBRznTFE0I\nKsblHJz8l6w7RuTltWgGiJjPevJ5wAojfV6ADXKGDSuepXnr4OPdjEUzNYhq\nc+izQuxQu3EKLfvD1kM3j4Xk7rIEdil3x0NYp8AzeiBLY0FrAVwK653F0qgM\nal9ybdSiXOutJCq12eNAOan3V2qjHraqqjfgXMGxuXRfHDHfnG4tEI1LjMFy\nATT8l+S9LhMXIjceGVWVF0wNfAERGBAWUaisC4/nVXedFKjErxmlttwEpd+y\nDX2wubZ2p59J3oESl3cnoY0pZDaZNgcwurpGZof3zB2ptT5sDO9wBGre7RL1\nEQSQnBvpIiD90pKTDrhArCGhMVV1bmt42RjejrY5AzgB+I3DKqzxSO5Ab6xP\n0a81\r\n=R8YM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "da61421faee6fdbf2a2b355b10d4e6eb1a842233", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^26.2.0", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^26.2.0", "graceful-fs": "^4.2.4", "jest-haste-map": "^26.2.1", "jest-regex-util": "^26.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_26.2.1_1596108938472_0.15026823462690797", "host": "s3://npm-registry-packages"}}, "26.2.2": {"name": "@jest/transform", "version": "26.2.2", "license": "MIT", "_id": "@jest/transform@26.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "86c005c8d5d749ac54d8df53ea58675fffe7a97e", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-26.2.2.tgz", "fileCount": 12, "integrity": "sha512-c1snhvi5wRVre1XyoO3Eef5SEWpuBCH/cEbntBUd9tI5sNYiBDmO0My/lc5IuuGYKp/HFIHV1eZpSx5yjdkhKw==", "signatures": [{"sig": "MEYCIQCfKIz7rfThriw6u8GY/t63iFED88ChKq72WDpPOaXGZwIhAIZZv0lZsG6kPAXQeFDa1ZCV+Zi83VxQ5AeX1y5Lmc6V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfI/hECRA9TVsSAnZWagAA98sP/RTYzNmUUUHVe7xUtYxP\nL4uQZ7KA5fnPwWlFaMt12J9fEsGwxKb6C/2XRlVG0+PtPXa5UISkGWfwA/Q/\nCReNFDts5sbstEDVTgR8D9aqCwhmB8tUa6HkKxigIYWGXl9RVueS68XdM03B\nkKwwRVugbKny1ZrBl1QBp/GQBvbUFqZgAQLz2728DdE//lUtHflwQWlTJ6JQ\npwjfjbedVER427oqTgEryoA0qCRhHMRxRWrzhoEEocZk+ObyBbKEaVbkJBNO\nnGzb7GWdHh9LWK2UPde9kv1DZsaw9yp1/OCzL93fe8er2rJe0wRyn/s9yuQW\nMgiIOkVk6IgA/MsxYTUgKT58mL4k5sfNjNovdodVJ7wo7ZB9o2M4Ir7vL3xA\ngPfcWNvyovZSwYCLG+c1Lq0dG6WFOeMLF5KfnJj7uINUQR4iobruL3sBVcsd\nBG2AHRxiEDjAYq3snK1JIrSzf/uOL52w/yPDG1wYz3Ga2o/Bfq1C++L28W87\nGpprvN495G7lA4t8W2pxgF/DjzzGADzWnKdcByT0kRoICzs2gjDXJppHVP1p\nGlGP8uhmaD7lXONmxhwzX1ld/lKAJli/vv0vp2ZH41T3Fuoap+zpRmuQINj4\nZt7iFMay0mX0fT7+gaEMAfcGHPjpejSwgeKd691oWW5FlSjuPG52R3q4xMyN\n6GPn\r\n=ligY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "a6a5abb7ef47ed2c31bf8987771a79f97ae65430", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^26.2.0", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^26.2.0", "graceful-fs": "^4.2.4", "jest-haste-map": "^26.2.2", "jest-regex-util": "^26.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_26.2.2_1596192835984_0.011964388821649408", "host": "s3://npm-registry-packages"}}, "26.3.0": {"name": "@jest/transform", "version": "26.3.0", "license": "MIT", "_id": "@jest/transform@26.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c393e0e01459da8a8bfc6d2a7c2ece1a13e8ba55", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-26.3.0.tgz", "fileCount": 12, "integrity": "sha512-Isj6NB68QorGoFWvcOjlUhpkT56PqNIsXKR7XfvoDlCANn/IANlh8DrKAA2l2JKC3yWSMH5wS0GwuQM20w3b2A==", "signatures": [{"sig": "MEYCIQDwQTdt2Ies5sfRGaopczov+XCqB03XEzBrx41JXRPGOAIhAOxqp29uN/PkhsCE4C2dUB4tEbG2Nwyk6OZ6/nNUVzsE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMTAwCRA9TVsSAnZWagAALnwP/i1O8sdofa55SZ7NqwRI\n15iFwFnGSAF0caXGjmOqEDIWc1xHqIBr9EdEz2DD8ZbnvMAR+LCLX4Vb6OgN\nFFZFgAyR9a3NFbOngoWt0UOIXhrxqW/mVymNBF15yA8K+HaKSK+8vvlOXlaT\nqMOHH9BOpmtspqAki9MtgE010KcNXkoJUeJ0nmeG8lZNUUw69NW3oQ9JEqXN\nujtt9wwZ6ijAXRzpDmhyug6TlI139V34Ie/FRVvWCyBXH6c5jf5vZyXs7e8g\n7nI8/6vLsfgwNvhHvSwBMtyDyjo/MGo3cMmI0IfpJSYmcjHvxJEtkqmIJI08\n0xOJ4vTsDkXidH35eObyLtse2ft9Kb7VntXYIkHSyyCSv1u/ZjXnrrdvcMXV\nDAmM0DrN6W2wwRdeNKRHiOnlKik/PuKXq4Dw+KuAWlYj59isJyIuwABtBWdi\neG/jFrrdxeOI6GthVpqFn2IQr7YYfh+msoU9kXl+BvfcdQ5tk7a8D30bdFqG\nUwIqIzEIITM1GfHWN8TVYV1XknbAuPxcGJFPbZ9YKJOqP8mm2oPg+KodWPZq\nr36Smp5XKny8WU2WeXQ/Q3CxriZ+vKsGOdmqib4D2EF3cXBV/4b+m1skhWxZ\n/kaKIqxh/GbRm2B+V+jIvgFPyZDV33+ASCeOtzc85VNabtEO0bZfb1l7YgZ/\nnM9P\r\n=QGU4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "3a7e06fe855515a848241bb06a6f6e117847443d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^26.3.0", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^26.3.0", "graceful-fs": "^4.2.4", "jest-haste-map": "^26.3.0", "jest-regex-util": "^26.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_26.3.0_1597059119733_0.8542020773407537", "host": "s3://npm-registry-packages"}}, "26.5.0": {"name": "@jest/transform", "version": "26.5.0", "license": "MIT", "_id": "@jest/transform@26.5.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "16404eaddf6034fe713da37b236756fc6b956db0", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-26.5.0.tgz", "fileCount": 12, "integrity": "sha512-Kt4WciOruTyTkJ2DZ+xtZiejRj3v22BrXCYZoGRbI0N6Q6tt2HdsWrrEtn6nlK24QWKC389xKkVk4Xr2gWBZQA==", "signatures": [{"sig": "MEYCIQCHslUcaqaDbm8CnkttsrqkHRSZWscqfSet3PZShmPHcgIhAMy6Ucw5WgRdLEK1klnb5kWgco8NQhnpxbseqFll9a3a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeuc8CRA9TVsSAnZWagAAdQgP/1CaaISudN3b9CLo4109\nC/HwGSpVkB7pgUMNrnhJpaHyS6XB7hQnvyl3Q1kQ9T7oXeQ7POMi8iGCsYyr\nSvd9HnFeDQcVTJ1BbCACtr0nU7+ADE7nDiVAt8FEcBlxT7go9o7BH3eIFp3m\nxlEeIeQI6lzFNeNpqmsFx7T1ev0HZyTWGK8ZMvRIXxbpWyyCMqC7fitBnowM\niVco6NFjyrZq3ZBoKpEUXDTzlHemMXYuhx55cmZ8MqJCIa9b8nTkQ9s2Sd2j\n0RfeYmESsUvXpzkd5HFxcwysNLhnz9FntqHb/z28CGNWsOgx1uyarXrF9oJp\nsuPTeaxAlTYY3Gs2xK9zdTyhonynmShtCWLFWHgkhKF16/1vNC+fOklD+ct1\nL7vSeXdQVqUTwbAxG8chlwZ3+6uyVoHx7O40KtojnPFl7BFWAKZ4f/ZbSaVR\nFoc4Y5wkL6c6UyIBb/K/o2Pclt7uVe6ZBUD6+6a2J1KR2MYX687WjEzlQS0G\nOiywqcPITJnC8/Ns8ukcbLOQfvb6jeRo3uzp7NPKyfyiSuBLJ4Y2VHmq9i3P\nX9pzvv4bWfmyMdAdS2u3hZoHZWnKc0fCwc/lzg8PLx76Gpl6HfUicJ1aVXSs\n/MEyZvg3YFfcUNxOLv9oiMpoG16dDu66EC2p4+KLb4iF5yC2PPkB6CHTb8cg\nbvqb\r\n=4+/Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68d1b1b638bc7464c2794a957c1b894de7da2ee3", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^26.5.0", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^26.5.0", "graceful-fs": "^4.2.4", "jest-haste-map": "^26.5.0", "jest-regex-util": "^26.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_26.5.0_1601890108129_0.7283396631912074", "host": "s3://npm-registry-packages"}}, "26.5.2": {"name": "@jest/transform", "version": "26.5.2", "license": "MIT", "_id": "@jest/transform@26.5.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6a0033a1d24316a1c75184d010d864f2c681bef5", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-26.5.2.tgz", "fileCount": 12, "integrity": "sha512-AUNjvexh+APhhmS8S+KboPz+D3pCxPvEAGduffaAJYxIFxGi/ytZQkrqcKDUU0ERBAo5R7087fyOYr2oms1seg==", "signatures": [{"sig": "MEQCICmd+uxaSUHK3jW4EpdmUJd7ijpgJQXda9m27dk3AkqHAiA4g/gTg/UGb3+BU/ODq0z7GX8QCNGtVDQBhRgX3ELceA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffEyICRA9TVsSAnZWagAA8YoP/iDPZbMlKfMsBuLF1HiC\nTRyWc6C3LIJtXO5gvqDQcdYCwrMC63LDw2Tzgc5pb4hZk4TZ4Pk8wMOqM0Uj\nGxtFp4hlrOwlmEL6dsCga5aW17MHtpYzHm2+rMcnGM9jREpW81XnudKpnAsU\ncwC6VqTV1mxBKtsCOaHGl7WhDI47BcMIBhISKD9BTb+dkAUiB7x+bATVGIn3\nAJp1SVSnIoBT5qbUOSPSVfEyxrYo1KcjssBUBY1ExDK4JMYnRVTfvQTrOTh2\nlp51IM9ZMg0MyRCLXhocQr1FUz2LNeZNuWKAwhhwasVZRv4Mg9rasvhqz1HE\nUgkQSzZp1hs8X/nivX95WRbjuWlnGcsTnejjU6KiRCBbvgYyn8pEtyFpGE2e\nYunp5ZA/c01c08sbEE7IinniqcdzKlu+UBOFA/tFKKgcq/rpcrNxwSQ+4PWh\nv5iaKb9XNdtRatbEhiwqfOGzKaYWfd6KiXK63m0/AUcDsrxu/3kROQ1GSZIu\n6qFzyrwYYkSwGKhPkPLxp1pcxprTFuc/pgxrhB8PZpPjNwrpN7WoBj1VnSER\ngUm1u8ACA4/mRxQy7GE5rbN1SWi3yAIyFIrDx9xau5TE6Wk7tI7kfHiHT73s\nnl360X2Kjjoxdv9vppAbraYFWKGf/KLczZN3QM6aNPxQPsjKR9SIthHs1vjZ\nrQZQ\r\n=CZ6O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "d2bacceb51e7f05c9cb6d764d5cd886a2fd71267", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^26.5.2", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^26.5.2", "graceful-fs": "^4.2.4", "jest-haste-map": "^26.5.2", "jest-regex-util": "^26.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_26.5.2_1601981576411_0.8422780963415122", "host": "s3://npm-registry-packages"}}, "26.6.0": {"name": "@jest/transform", "version": "26.6.0", "license": "MIT", "_id": "@jest/transform@26.6.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1a6b95d0c7f9b4f96dd3aab9d28422a9e5e4043e", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-26.6.0.tgz", "fileCount": 12, "integrity": "sha512-NUNA1NMCyVV9g5NIQF1jzW7QutQhB/HAocteCiUyH0VhmLXnGMTfPYQu1G6IjPk+k1SWdh2PD+Zs1vMqbavWzg==", "signatures": [{"sig": "MEUCIFF7Ut185+6MNOnWkg3msk49EF8vMclapcAMUas7ssGFAiEAomd56VhXux387j2qRn40iVc4tPdOYumjaIZeoYiUXlE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjX97CRA9TVsSAnZWagAATKkP/RB1DYkyCx7KRXADdCRw\nj1/GF/cSKteaBc1+3sBop/35phHeaEX3RLTv8FgyzJgenM47BPpUDTewdL6g\nZ4/9pMJHcc18kf5EL55gvyE82Bc3YY6avdpjF2npE5O9jm9AoJk7KRTaO8if\nfAGKJ5HPsVy6RhwNrDfMyOPBXIZX1V1mtwXeKtW69aZ8jNXBN+jJeDI+O+74\nskj2mPHhyngM5325pNtqyeoAR2It7gvtskzLBQ+BiGbgBYKxUhNULspfkwE1\nc3xSnxRF+fdJPyry4bwTx1qFH5L4JvHyIi5XYv4yvWmbMhLsszKbC+rEerE/\ns1NHbcwqOvplrWmBkM0Ktadqy9K86Qx6jrCEdhR4tIfIaslNCXdG2Gw6jzG4\nsQ3RbTwKEvcavP1gXCzKej0cdkYTJGXkjtt9JMYKAT8il7we1VhZPVNe7cGz\nA0fvDzQL44p/h6AyWpa7yDTGAR957NQao8/ZCjfXWDMZXnRmX/O1VJU0Lo+Y\n9ilcP3hj8IcNZRTWycfc5nDWi3ZJUGvCxaRlD1ZUrcWWp1Gn8YF7nPivQEMW\nKmFz6sfjx55jhc/ZA2nhlGCiKyuRayJuWpdfBurcNmlVYt2voiCPF5dQYRdI\nfYZ0zi38rEBl6v8YJDtj8NFKK+4l/yHc7SDeWdD1wL5Z8SGfbPUbqZ6YFph3\nAEb6\r\n=DAfz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "b254fd82fdedcba200e1c7eddeaab83a09bdaaef", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^26.6.0", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^26.6.0", "graceful-fs": "^4.2.4", "jest-haste-map": "^26.6.0", "jest-regex-util": "^26.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_26.6.0_1603108730804_0.31598595850539724", "host": "s3://npm-registry-packages"}}, "26.6.1": {"name": "@jest/transform", "version": "26.6.1", "license": "MIT", "_id": "@jest/transform@26.6.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f70786f96e0f765947b4fb4f54ffcfb7bd783711", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-26.6.1.tgz", "fileCount": 12, "integrity": "sha512-oNFAqVtqRxZRx6vXL3I4bPKUK0BIlEeaalkwxyQGGI8oXDQBtYQBpiMe5F7qPs4QdvvFYB42gPGIMMcxXaBBxQ==", "signatures": [{"sig": "MEYCIQCv+8yNNOvpRSPwBMuZBU0azamE04S/sgGiA84Kxs5LYQIhAJ0QMtZhiaEGTeVAa7Qu1mzU+K5TmyMZwfT3t+/Ecgw/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkp0oCRA9TVsSAnZWagAA6TAP+wSECmwdvL7KC+UCAU3/\ndzsoh6LPubobsxccE89UTQbCAWZaHVYU5g6Dav21UgzNHTlvqh/GJLx1LFET\nF8I7kOWtPwzQe5dfG5usNfKvSaSfd0iFzF+YWNBXJO4HUkrxW9ar6dodNbLt\nzn4Pt+/04r7AGqxWuRry1eyZoukneVvMfuErzpCDlGMYjjLqvgLrEySm9Tsf\n4A8oQxlNFrty4xveqOI05J8u8s1xFah4FylNRgRCwH8tQB0B2v0JsoI/tmNX\n6qiBMtPhT+oAUObW/B5k+BrhSynWfdqIdJ5pJ1b6oYK24WG10CdB30aN6oVX\nWdxaWefnSis+2sKF6bU10lDhI+uM0OxV5h5ODxls4Qf73GIhgKrwifa/UzSb\nHpB8YhZYy8jgXI10+NwvaMK8u88YpXe3DbtxQmi1gYGNWt9JHND4Z4zLFjJ/\nkjd5WSJkVql/j81K0PDCz3ItF1wLZIKGJRDwJAVF+ZJxhI8BGLCpwx0rM+xy\nhcutRxMJlE2x3TIE3EvSVx0ZRfjOnFlELa+nu+iwf+W12aOLSj6mXRZmRzMl\nfKEFZn71O3xdhncg3eYEq0bU0XPFVmKCg0Qrm/Jpy0GnkgUkGSP1UMH68k8B\nGNTFs1YepLZYmbbB9ZYSsQd6NGQZ89SAuSLUso70pGwBKlm7o6aMZe4byqxD\nnUqs\r\n=H+vw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "f6366db60e32f1763e612288bf3984bcfa7a0a15", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^26.6.1", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^26.6.1", "graceful-fs": "^4.2.4", "jest-haste-map": "^26.6.1", "jest-regex-util": "^26.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_26.6.1_1603444007866_0.17801125554948527", "host": "s3://npm-registry-packages"}}, "26.6.2": {"name": "@jest/transform", "version": "26.6.2", "license": "MIT", "_id": "@jest/transform@26.6.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5ac57c5fa1ad17b2aae83e73e45813894dcf2e4b", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-26.6.2.tgz", "fileCount": 12, "integrity": "sha512-E9JjhUgNzvuQ+vVAL21vlyfy12gP0GhazGgJC4h6qUt1jSdUXGWJ1wfu/X7Sd8etSgxV4ovT1pb9v5D6QW4XgA==", "signatures": [{"sig": "MEQCID0ADDxNz7lPzvKOjM6+uXZDRE9el2tm9b3zuixD4yH/AiBmM8zA2tjDo7QZ5v9BwjnnEedPFg8rh2g7yTSBOdPi3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoADZCRA9TVsSAnZWagAAmQ0P/RPUgTk5WLi35VUqm8YM\nZ5bOyKEB7W5PP4PAmYB5lOHPP6XJeu8QyQid2KoPMMtdp/gUN9HrZp5+sFDz\n5tPOVJAtGPWXiqvAXhZBJ7vw+gIeMKk5v6zvybzjx0zLoGPoynOM+zENq4OR\nlJzeZFlNgOJQvlkXv1EghqJqB/hZQlaqgcTPaCHaO6jQaMWdGRe7J3nvzCby\ntud2A+mifMfeU+jHQuxyaZoPBwv17bDcUmm11t02h+JVWSiAUem8lYVcC2dr\niYjbwm9A9KTzrd4JbfYLl9vX38Pmfcgwy/kydxTSA3YxxNiptFHjhcdUvDER\nfzIsfU3ifxRbVjtbkqo7nN3twdoGllyGehxxWAb9XP78X22clEDxpjfDbdPB\nMl/u9WB0nVef2baYTbqVzEFNsd0JJhcWrk2D+S0JdSKsAmFkg+/U4PNrwb0Q\n9sJQfiu00hOKLT+WAe//67PryeOwwsnmckx1Up7UVp3aMiyvayStnhjfEKFq\nqA+96PYn5ANe1dddPcG+zzmH1GAYBg6CMT3k88ln8rC0kUIqNE/2XUfzkg/R\nLnYqhihZow14aNjr72lpizjX1pRSW+5PCO2Ct9UbECfgYT6lefS87yQ/Egoq\nMkbYrJS/ccV9mZG9BVc6Qvhv/YS4v+OtZzHpDf/vkH3MN3olbiBRJPypF4m2\nfaMO\r\n=pNRI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^26.6.2", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^26.6.2", "graceful-fs": "^4.2.4", "jest-haste-map": "^26.6.2", "jest-regex-util": "^26.0.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^26.6.2", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_26.6.2_1604321496707_0.5944189436331482", "host": "s3://npm-registry-packages"}}, "27.0.0-next.0": {"name": "@jest/transform", "version": "27.0.0-next.0", "license": "MIT", "_id": "@jest/transform@27.0.0-next.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d7ae7d388f88813b51faaacc763d45ec35646b2c", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.0.0-next.0.tgz", "fileCount": 12, "integrity": "sha512-q7v+qQfws+ko/2yO+q4jFU0FttvKTbCyp0WoI05lL5owGdfvIddqd1scUWlzgcB9noq3N8epEh2JH9KRZ0gsTg==", "signatures": [{"sig": "MEYCIQDj8QZUcOJ6GaGlupxwhfhCi1J0L4weGy15scL0MFu+kQIhAPMbXnh0T3trwN7Jm8igyMhryKfZJNOjeaMSe5C6+F4w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39011, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy8KJCRA9TVsSAnZWagAArvYP/A0a/qwnboX/PSvZ4s6I\nUaU3cGRmAvHXKG359clYLCuC7blBOG6KhqVSVVch2dAzmJ5Vp2zzKjUrv5nz\n6vrJxHYqyXirPgnxqLORzi3HE5/VsgPg4nYa21MdpXYojP/4o6w7BdYlnwSL\nyEgpk7cqwhG6aYQd4KtTFhxufV57hvD8/1g9WZEx1anA8xSQ8nQAJ2KLLRP4\nab5K9ILlu4VP45rQwuUSUynGyQ04MB5L0xqS6b7Jp2LHAoCPpvVIuDhzKIyC\n8EEwR09jVMlE8PGRMYY3Z64qgON2uKhG2YUMxeItn7396SRHYBrlut8Io9hC\nzGWjwOwPg5oynIylfbo8M9CcOtQV10Gx4ZMvFLiQekti8ed4iHQhZvU5KoY7\n+jOnRaPZ+kNOESCQbXBu4Jnl+CmDijlEwNgsggy12GEusuDLcQh/jK+3E5kU\nRPVNB4cz0DSCoBn1tr8NMH65Kfmljyy1uXbMy2ICl6Hkw9FjbsXbtV28fjaf\n1lI8WA8BYfuSvByrf9OPcSLuQbtZqk5znejU57sQ8d+7RJEkAUMHJm4dYw/Q\nBZ4KdHIQOwCwBhVDjDbpTEWRgF+6/oKvTsDp/IznVWzgOieZIQRynGmiqvil\n2kEoLkWlpmQQVRdabjyKa3zeHgvo2QufDt34zz0yDlDMzrcYSAMfhTbDnw1U\n/xn7\r\n=SsV6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f77c70602cab8419794f10fa39510f13baafef8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.0.0-next.0", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.0.0-next.0", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.0.0-next.0", "jest-regex-util": "^27.0.0-next.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.0.0-next.0", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.0.0-next.0_1607189128945_0.2512677033364459", "host": "s3://npm-registry-packages"}}, "27.0.0-next.1": {"name": "@jest/transform", "version": "27.0.0-next.1", "license": "MIT", "_id": "@jest/transform@27.0.0-next.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "584bf23b7f28b5bd398eb38f6fc0db9ddb87722e", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.0.0-next.1.tgz", "fileCount": 12, "integrity": "sha512-Pf6U9vsKu55Qp8zTRDxgRYwgsAMxCRUNxRUDqSOwmCHtmNvmp2lVHQ3xthUDWAp14KaHwJlQ4YzU4i+uXSBFTA==", "signatures": [{"sig": "MEUCID/lV32PLvGLM7EpYwiqPO96w85HKtOTaJCJgxesgmOHAiEAyTTFXyc7jZbftuiJ1DmEnq3FSAKMAAyINgxm4Xndr34=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfziNyCRA9TVsSAnZWagAAfcYP/iW7uaBwTeHXyfAy0WWY\npBeK0PNmcK1v+i9eeBhvgtGkWFg4s4sDO3nwM+BmmDSd0IYxrFFWna8Ce8qa\nMM2DImBvHFeCjKRKmnqvkkOuFvR1p4/KddMnmiIRWpZzJRzvV4nY5zozNNff\nqyhmhtisH9SG6x1wr+Knzs02dojpuMFsR3bAknKb6RHhQWAf1CoKhIzJ/cXa\nexKgnGJTKD9lWSS/PHABmdbgnVXB4bDHkU2HBQ2O/arVSuihW2Qxw8udvq/a\nAqUORcS/oO2b2pQqolL/wmw7FKwprOQu+EhZvDjJZx+FkvNAkbQi4PsZ0i4i\n0Miq+ojv6o2sMdwVFKePF0B5ChMfWfHG98IW4aDBwQTlbaabGCVLebUux6mz\nJiAXIgxNYucLlupHqZw8+au0B7bU7QRn2S6cJbDP8vmuHkPayiIuxeMhmJ6j\ndxGWeGGrQq+0Xmelza1IbVzn4Dhsy1TziqhVKmdCkUAm0TulpVV3I3tDLsO4\nicmPH9BNPBgpZzGIBxJYK0nwiJW5e930OvlE0WSycnM9qnl5Jk8JlAjN+AMT\n0iUkUkVodaVkZ1XHBXjkE6ypKVvOiR96JZ0+K5JByIutvYyUnt5/kqQQVUMy\ntMl6Y0DY8fEwh56kSifHjIWeDkjvnRINPip7uQ0yj8IVTHSUPz1LHMCMEDMT\nWnZu\r\n=YDyW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "774c1898bbb078c20fa53906d535335babc6585d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.0.0-next.1", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.0.0-next.1", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.0.0-next.1", "jest-regex-util": "^27.0.0-next.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.0.0-next.1", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.0.0-next.1_1607345009768_0.03736885350559693", "host": "s3://npm-registry-packages"}}, "27.0.0-next.2": {"name": "@jest/transform", "version": "27.0.0-next.2", "license": "MIT", "_id": "@jest/transform@27.0.0-next.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "561161fc5697840e87b9e2f62b743cef86274e0e", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.0.0-next.2.tgz", "fileCount": 12, "integrity": "sha512-3QnpdRTDDgn1fxOhrI5KFdLf5vn5r7gH2nimTyHMjwrvO/qFMjQoKVxDrRaCC3mCfBcmCB/guPet9RveoCz6fA==", "signatures": [{"sig": "MEUCIHOAJHv/PquYIXKNAbU/30n1WGg9ulJLVxlSwCjA3O+5AiEAm53lfVER+6bXgyGF/j/+kHrJDscTQbgkXrqiW/a17to=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfzj3ICRA9TVsSAnZWagAAzYMP/RJa+aGT31KMkfuuQ2nm\nC7nrgcs4f6+R/g0qnfBVZH70c1sZAGNDSBNNN35T5EWyUM001eQqnafVEvOd\npoiCI+IvqLwV6Hqjepp33zU15W9R/tGKNgxHqsveYNjBWcZw/GeSUljwelIK\nqZiUNmZVOqF7ZZrstEXcG8bB0uBwuXNcMWqEWOVySGpUTb/5tMn9oKBrFCDR\nD9OwNTpEC1JtT7z2ODTLzA5q10xGB4j14pjM5HD5REnzKFje7gDGkA3xGhkF\nRn5NCwGPCdi5s+M1s+CG7svEimzbkRkofA89QiA6q3j/3nwXid4V2ZYvcryR\nz65mQlbI+RvVfNRQONTKJdc+utEgV0kJewS1JcQ64Ce9NZUXDATrrPL4FcNL\nwH+k4klnT1yub3aMPmyKfA3rTY2Js76f5AV/f6MwIPrELKFmC+ao7EYeVDWf\nKChhFiSjGOPoUi2huJBLEnxdWDZuwe6C53OwAM39XaBV9kdpE21/LL+3OYV8\nFG4N09fVrUlodw5xL7bxycQsqB5RkaGW2Oy6GXhXBl3+hhEewCYW8HNuTXHk\nVa3x+ihqyqKpmO8OJAxyq2TduK5KSTT25x5NdRDB79J6ezIZU3loXzsvlpHH\nM1oFeoyz/tX8u0MHYvEaXyu09s9nitF3qfKBvbm30nZaOMfXP5jWyoVpO6U9\nSgQl\r\n=YUqF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "0006b152354237416ffbbc26d78c0b10375c0a49", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.0.0-next.1", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.0.0-next.1", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.0.0-next.2", "jest-regex-util": "^27.0.0-next.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.0.0-next.1", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.0.0-next.2_1607351752074_0.34835786402843927", "host": "s3://npm-registry-packages"}}, "27.0.0-next.3": {"name": "@jest/transform", "version": "27.0.0-next.3", "license": "MIT", "_id": "@jest/transform@27.0.0-next.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "57c0941ad2a33d0b1fbdffc7082597dc12303cd8", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.0.0-next.3.tgz", "fileCount": 12, "integrity": "sha512-uyKC7hPacpkRD8wM2w0Q3YaSNn8DL1BupxysktURj4QgNQFjFvQlsSZi742AKtVPMNoXmvDi9CHsberChZskQg==", "signatures": [{"sig": "MEUCIA9gGksnaY9897fqXeC9dCKy/NnTSaQx9D4Df5mbAVe6AiEAlEc23OPaZ4mr9sr+m7OR15Icgw8EpjYNYo3CHIMePiQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLuW7CRA9TVsSAnZWagAAK3IQAJQMXWcHcqXxSIrdeAvO\nkwzttxi2Ib3bzUcDwiRxStq4e+Mziy6tHpJro5zH5jCEKbCDAOznNvsEvtCt\ntydbSNfRF9OZxsv+Xsurqd5txOZAKcen0U7BSzC2odR2he/ZhIdhsfGos9cp\nEnjRXZPJW1wPjDu0hQoOujKRdsgJDNW8+nkiDKxQ79007WCfdv6Hn0eDNujv\ncU1AipV1ndAS666HigXy1ASPrR0TEPh0Kpm9zQs+7rPmKjvKhC9Tcv9yAWHG\nr3y4eAKCEip+wUAfoAwumLCEzTxCioYIjtDMfFsxYx+TYJ+qeDg/9LmSxMDC\n0qjn5hjr383JZ+SMb17GgSZTCtdyBUDBe+haJ1Wa7yCI/JdAjRwfx4874uHW\nZLKfoV/cPmvctJ/J/KXqVICK+lpOCrYncwPrURDGLqTRc6iVY8zDQQQfF/11\nsAC54/Y3I6oZumONW08B0eJ1xNbFP31hIXSmZGAfrhLUxcAtw3MqcRCxGceI\nMwIooboYxawTbPBd0rQveIkpaDqEhe29aw3jXsn6LO1RE6hKTvozUJNsdG+T\nbvU0wYaLDWADKJk1VqX99HFd0Myy9C8JFLoD1t678tkVcdliMsrrbaJwBimj\nYGPdAZ865XmPmXle556RhcVdG/xivh28P4NvBrbx+ksekStuhCFbDh8rLtV4\nQKDw\r\n=a6gE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2e34f2cfaf9b6864c3ad4bdca05d3097d3108a41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.22.1/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.0.0-next.3", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.0.0-next.3", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.0.0-next.3", "jest-regex-util": "^27.0.0-next.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.0.0-next.3", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.0.0-next.3_1613686202582_0.3182592336947472", "host": "s3://npm-registry-packages"}}, "27.0.0-next.4": {"name": "@jest/transform", "version": "27.0.0-next.4", "license": "MIT", "_id": "@jest/transform@27.0.0-next.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "980104d242e52eecb0dd1b004f13c44944ad2e54", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.0.0-next.4.tgz", "fileCount": 12, "integrity": "sha512-1rI+X68XhWlPMaNJfr2CkfwL4BWum1KbMUvn8uYKdwzVHFOQNcg/uBS1orypaKDvawPbXRzbUshIcf48A0SUPA==", "signatures": [{"sig": "MEUCIAGuSsP3lqkzS5i65aCr3/0RAzpDmyI8MFsSnuTHD95WAiEA60P7axxqLl3GEPSPH+QgNdBhjL6B8IzTavaKB3pxmqA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39279, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgRipwCRA9TVsSAnZWagAATUQP/RRpeypZSUGgaqOF8EL2\nxGXbm+MNHOpzqMl2fIEfx9Rzy5xB+dS10koBGSxX0rb1n9amP8o5a4CQTdml\neW7nZoGUzGakPY/N2q8T1/vlNOCDYWKoUNFOyJn8Nq1O1J9J1k1ykZhWwk0U\nqGrlEa1BjTQ88xWSAOG+R94vdHe4P8O7fRL3QNvecWxL5zvBNdTK6e4SrD96\nOwcq7iiPzsU4HVXFoJK9N4EBahIqgpQU6VcUnOqaH7zCVHM//5fG6H/esxnH\n6SR+kwKvT1kkAZFJ+HwDUt0l5G0+pXEsvaCWl85Cg5S3smMAwHaDypyCWcCI\nAbM3u0L/yf3+/7I2yP7F6D4hGhuTbXklb8mHwAKUZEuZXIeoaphZUjeP+tKf\nJrgKP7HDTmyFSzc0bAKoU4QIfGAh5QrnFFWUOGg40/spGgxCL7NINrjOIyZi\n2pi3BO2xVU/I5G1Pv6DsBHRNkJzRQtAH2GvuzZIdX6vB7TxxwxkzAnvagG64\nTwbYzkw2m131N7QUc02vRHy7UuBBWoRVsWG5JpZ5usdyV74ukAnOoKyXyBP9\nUUETW7M1vSJtadFfowmfJRAdv2q1PcvqCr+I1yvAJf7Y6ca3Abkf3uoVhIwT\n2w0vBTiQwLWSugIFC9Wcvqe2j3xLVJEylf23EzQKHwiYju/M+REVfltW0L3Q\n+XeM\r\n=W5JR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "941c414f0b757fdc619778c46c21fda26b3e5504", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.0.0-next.3", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.0.0-next.3", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.0.0-next.4", "jest-regex-util": "^27.0.0-next.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.0.0-next.3", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.0.0-next.4_1615211119749_0.17249827489880976", "host": "s3://npm-registry-packages"}}, "27.0.0-next.5": {"name": "@jest/transform", "version": "27.0.0-next.5", "license": "MIT", "_id": "@jest/transform@27.0.0-next.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "19fb38668b0944ffd8c844487b38d9a88fb07bc1", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.0.0-next.5.tgz", "fileCount": 12, "integrity": "sha512-v1mr0O4DtDoWS2FdeOl6ml2atrQMiy1CARiSp+qvSnCWKVWtWd5kvfKmnXPkvbFU2vmC5esFL6FdQWl8LgHxog==", "signatures": [{"sig": "MEQCIER34bKmemFdsKwkV1yxB/z2pTgUEkVOpKumCrWnKgSaAiBIs75DFlRGbBvJEmm2/hVkvjqI5fgcfXeNXcO6zkIpvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47169, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgT1sgCRA9TVsSAnZWagAAG9UP/1im0E36n91eQlHVHHvg\n/g0fllg5uqeDcs2lviqQqdHPOr5+RffKBsm0XUQ5fknAccVfPWmuGoZdQYGS\nkWEYeQw3aVfRDA6uM/x8qOV6ShLQ6yamRRCv59a7cQHgw9opWS+n9ia5LfT1\ncjv2dpDsPivzi6qUBdPgcSl2fsx0JcYbFMyWgmcUhdbLT1z7WVPNsDlAIxN3\nzvyxW1p4PfzzkSOE0AcXy0ekTYz84srkapxXdjkDuyrAb0O5A/2AkPjH/fxm\npTtYmryQjegNPsFotZVaftcAIKFBWFqQYbQQtc2Fw4dvb7DktihCaVo3kSyN\nbJhMDPv6LmxlMTdCLwuLC0xMKqL3MkBaIUBFeeWEdAipzk9nPWh7fJ1OZFPF\ntxZeVAryDjThJzZPmWb+KIxj3g7A/d8qjRQZBGuLNhgnPykYOqgrapbmx1xB\nQ1IKB+sMeoKZcCF9g/OlDEKRWJagyb0VT1GJQBV6z3UVelPXm44VxFFxQqH0\n7y5rOXZHclkikgEnB+53EELoEDAlMaIFsfk6xnHFsAo6nLTwEsyrnK/O3XN4\nVLwNDi82J8sMPoCaLy/WOZHiYyCjj4QpiImb88yc+KpouXFJB0C7KJVEaZvZ\ntHg+Kb28cYF5nljoU+ymcxASuAGzxCoX2SEcVC0RvksHXsyb6cpm4cy9QxaI\n2nUE\r\n=Z7wt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "0a2b94282170b6d4cc26c2d2003cc04ffebe5e3f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.0.0-next.3", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.0.0-next.3", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.0.0-next.5", "jest-regex-util": "^27.0.0-next.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.0.0-next.5", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.0.0-next.5_1615813408434_0.8662195616325288", "host": "s3://npm-registry-packages"}}, "27.0.0-next.6": {"name": "@jest/transform", "version": "27.0.0-next.6", "license": "MIT", "_id": "@jest/transform@27.0.0-next.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b94b08f6aa2e54bbd09bbca39ca65c4f5028d6ab", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.0.0-next.6.tgz", "fileCount": 12, "integrity": "sha512-DrMnKiiZM4plkNbWulNX1L23twkpbznnKvUBVvIHhus+IlPUKhOZIUeigKckfRhd/ErMfMsDtkb/XMq2QbyHmw==", "signatures": [{"sig": "MEUCIQDDIahCvpDs67RLNFgjQbfpH93SImoOMsB7z8aMndZ2pgIgUHpvCflS+tKbARLJCZvrWiAU/qhOzQB8AlK5xezWG+8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46360, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXOcVCRA9TVsSAnZWagAAI3QQAIogBO9jLnXWnP3bCeQK\nwvDA0mamEfLmYOMFmJxi0uqgRckOuX2LyqwjDNAfmtc7KQ8iP2ir4DAha+B0\nOLh88d/S8u3dF6XQ2ofThzA3R2PDtbELVu13mlPUiDg7VmRD/CwMczv9V+Hn\nGsDB4Ovlxo1N4ewvo5+Q+Kpec5Km/hsRuvRmrJdzhzMFcIOol1mbbFmsgcgr\nQCbjbhulLH/n8mbm0uOMLx0w5T0wRgUtEkZFtvr0VlJvawvGC91eSOg7ihkN\nIEVQ7oudDqAAsFbXAD0kcd+xLTJk2iEIM43P3JmxPQvQ7uUzqdisiggdnKJD\n9j5yRTnoFYhum7VoBvBF+AW00k9lovjbP3dMK4zUCg0scEWCKi1AJ3BfaV4N\n8xB97GPFdzPVutHhmnpaW1qPJBzJXt8CWn5Z2JIoRD53oljS8E8zc1bA8Yoe\nyjT7WAzI3xpXsW9X0qbxzuN2N21f8HjAN0xEpKPfAso3kboZGgWEU0oPzqbV\nxu06TjsIEI7o2LdP8B+T4OBG5lMUFaXfkSMnC8TMFbZlO1MlenhIa9QMNBAM\n+fMhIr7fahCZFv6Rf03jKmLSQ9oMXs3EGe8kyHy+vMvppxqJb9/bNzoqU0B4\nhjwSwOqZo4ju8Bqyu8o3NCZY6r8LauI4pqGhaeBI++u/UueschH3aI9N1CZz\nUo6f\r\n=tC9J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "974d2f22b7deeb4f683fb38dd1ee3a0e984916df", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.0.0-next.6", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.0.0-next.3", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.0.0-next.6", "jest-regex-util": "^27.0.0-next.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.0.0-next.6", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.0.0-next.6_1616701204848_0.4936098649732046", "host": "s3://npm-registry-packages"}}, "27.0.0-next.7": {"name": "@jest/transform", "version": "27.0.0-next.7", "license": "MIT", "_id": "@jest/transform@27.0.0-next.7", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "541f2b061c2b0b7197fbd5994dd9582ce3f2fd16", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.0.0-next.7.tgz", "fileCount": 12, "integrity": "sha512-+upXoG4LbB0XmfFe5c//Vws33e2nd6IcLq3DBr7vR2BBKh6YnqMzhYLjLa3dcy2i8358gtbi5h34qao/38Bl4A==", "signatures": [{"sig": "MEYCIQDYy9VFom+oPDjxgWXFpev3Hf+ITCA7Gto7Qv7RSq+d6QIhAPwtYyXVuFaPAeeD1GVl0YeT+DQKEzGWqQUf6SZhLQMt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZyCSCRA9TVsSAnZWagAAe5YQAIhPlgX8fSs19OAt5Hcj\nZPdaSd1FqjD9Lt9QraEPnjx6yrIrmcS8TFHEO9H9ETZYpKXolL5y61bfYJYg\n/Mzl0qtVQJqak78tVdVtvhV54ev36QXWUMFrUaxb9s3KDwuhrdxlZGmaHfz/\nZrgjrvTitHSrVOiipE3zxfmrhMPF6eNUO+5EHgYU7+8t8kgeSmft29bJ/q9E\nsmHkqvDyv2wXyXeGwS9iyqiFEUnzOt9Lu0KqLkwxCwhZVO3+Cm5Mp69dtjvL\nwrqTptQBlDWTHInkHLSwSPfBTSARyxeqfMieLfEBGNAU3sAx7jhRU3pfjZyd\nXQmGMXBreVXWVmjZiGgsOgNfMFfUnHMjv/tp0zgbPNA6IQTc6uty++IqAhSg\npnZ0F05GLrPIjS3hoQBE+gjzQw5KJI7bkOWh2t7CaQa4jtLDLgeJEJmGHmJu\n5nmuV+UQf1jfmv/f6QPq0+15MtK0ATJfx/hZYrLg0az8XMthKtn6hxbx6BL1\npzzVnQlHwR33dLjpcIhtKgnsHd4++aMLPEXy/PGFEm8QafmlXjmM5/00yn55\n4GCnAZOsxOaQkvxVlmF6bM/DQqOjPffSuqGh7DLgqZgeKSnO4Eii+Oz6uWDA\nz/B+ELAYfyR7EuqtHa36GEIvlBdQ7Ju5XLys8Ln2jCtkh2BKg3rJ3VdPTNKA\nWPho\r\n=rD1B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "28c763e6be8f57bda89238b95dc801460c2d6601", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.0.0-next.7", "micromatch": "^4.0.2", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.0.0-next.7", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.0.0-next.7", "jest-regex-util": "^27.0.0-next.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.0.0-next.7", "@types/micromatch": "^4.0.0", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.0.0-next.7_1617371281774_0.08101414615207658", "host": "s3://npm-registry-packages"}}, "27.0.0-next.8": {"name": "@jest/transform", "version": "27.0.0-next.8", "license": "MIT", "_id": "@jest/transform@27.0.0-next.8", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6826f63ad850f7c2b485e81bec78450c06654458", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.0.0-next.8.tgz", "fileCount": 12, "integrity": "sha512-Nb1NABH73C36/7yn8ii6h8bNtP2lkoUTHkvq1/V8UlMsdG9+h6uMBmSmYyubblZb/gZpTb1pMUuEQUD+srwrSQ==", "signatures": [{"sig": "MEYCIQCfAg98+WpkzBJeOXemgRfPlxNORGXkc2K5HlI0POm+yAIhALlalS+xVXAQpy2cC/HCFZQbHmVDjhn7uHoWYSDpqVOq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdMzdCRA9TVsSAnZWagAADEgQAJ7LF8HOWcb4Fj4azKX+\nL8UWxHAr3AGBZfP1Li8jE/I5sSqs+05UyMux59IeWFNFn9aopn7BTtFJRQp7\nCOk4XweTyZ7qEjNFnfwx1d/bp5k9pYPYCPX8G3ximIYIeUXOntnUIhn7nqTu\nhwIyXgCbRYBbMDnJA1igz7JDo20DmugydtHndlH1HcSQDfbMt6RgyodiCCk7\n5zADnB0uFmq8CCMTdA8UpW0Yntz7MDr+x8j6IpPdOH2yGCLIdmM8AoHmBc+S\nnzDkK8GkYDOotWMab/D+enb8H/ROFmcAG8QabVpzABd3VmN9W+DZgbp5ojdt\nE9uktLAe8qFls/kIvsRAESqCSRGBitCi03NE4yrfrdZRPvanxNjsyJIdef2o\nsDrTgwZhybew91p7wyTVK2PgZkDDhn5r6/SFAvT8AD4wjoR+0DctUyB55PgU\n3xjJN/igNj7Ad/491KpJUgZKdsW8oyWEuB8YMR9/RdvKkFsApiEhSu/KonKO\nz2EmhW433mBek5jdFELuaFuGMFItpNIGLKGtOXyLsJMonYGqy/4hVmePpmdD\n+cAJcQKV8hgfTVbwrPxb7urrlUKbubY6rRWygVweV8mnUFRO8rVgrJAl+Cjw\n3XKGyMvXylTiYie/X5/5L01pi4/XN1SgZJo/1tML4cj5tjmSoeK2FUOM1N+2\na46l\r\n=rY6h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d7ba5030e274b52f029179dfdb860349a36eea37", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.0.0-next.8", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.0.0-next.8", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.0.0-next.8", "jest-regex-util": "^27.0.0-next.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.0.0-next.8", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.0.0-next.8_1618267356650_0.06482076864587838", "host": "s3://npm-registry-packages"}}, "27.0.0-next.9": {"name": "@jest/transform", "version": "27.0.0-next.9", "license": "MIT", "_id": "@jest/transform@27.0.0-next.9", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d408c49501ba4798c2d788a1519164f043ba98ec", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.0.0-next.9.tgz", "fileCount": 12, "integrity": "sha512-BLqPh+J/RB/zURuSHZiPswNvMReTUSsptHz2rbIaz7L+opDBYMY3dETh5YTkcx3W4ryYSMoFrwpSOGxgUU5QfA==", "signatures": [{"sig": "MEUCIFw0O1lChE7E4yG9P4UU39/8KAW8j4JLy20iQcKJjYo4AiEA+XSx1chtkRUysdsoyJtn8fEfjQDnPQqjE3VP3oPJWEY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkOjICRA9TVsSAnZWagAAQ3oP/iwcNFKl11IlSkFRIsYs\nquF4nxawrb0OqZj72J+3lOTqNnJ+R4s8EhPh60boLUmdR3psaM7bHqz5LTaT\ni5Cy4bD5q0MlhcyRVsXdf2vZtrgS8AFIdymGkx7wtAfnj4A6mzWLJvL6yiiS\n08jm8bTEyC44d9l1v3h+cL52M3kjZQCWQIkw7JTEU5pFR0YnbKQjclD2reFE\n5s7P7n2JfDfPQvS+8TyxeCjpGA9Gxusz96szxM7f6WxFs6c+NITIAncTJN45\n1mdarG+kpZKesnIbC+uhn2MO2n2trAi59XFh14j1sECbM/t0YJirOvYZYrlA\n6ByTqx7c4tywDjEWVdvZM7oeZsmhiqGMJI5zHng1XOKm3FfwBmLiEIPhtiCA\nvwFXQ38blRHoC0eakuHy2sUVnw6CfkGEY2rDlVjt70vzf2klax6mUXUXOG8W\nYcP30O3z6vvVr6l4uJztNn2fuDwrs4hj3Gok4QXWlcNPSy6FGZrr5mtoqyOh\nXwi7a2TUxvK0iaxaufYUCbT8SIYBhCuWcIuCPF93RYgrLHgEyc8jJZVEWUwg\nn3DhlSVdbNiWG7NeHI8l9XXRHOQoIAteubfuUXxY043Z5A5nvAYZE7zfUDsg\nZPQU4RDDLJjyGwcNV3oWdrtBT3N61ecb6bw9/Bt6vvLdwoQO24xJygO/4er/\nWxbC\r\n=VhwC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d836f33f98845794b4eae8149548a81ddcfc6521", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.0.0-next.9", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.0.0-next.8", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.0.0-next.9", "jest-regex-util": "^27.0.0-next.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.0.0-next.9", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.0.0-next.9_1620109512370_0.5122162263049661", "host": "s3://npm-registry-packages"}}, "27.0.0-next.10": {"name": "@jest/transform", "version": "27.0.0-next.10", "license": "MIT", "_id": "@jest/transform@27.0.0-next.10", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "af8ef00ed60aa5ef8c263f400ec15630719060cb", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.0.0-next.10.tgz", "fileCount": 12, "integrity": "sha512-UpG5vMTF+dqnN6TQLFIwedV9odhfVFwWA/kxwvx3WnDWFIkwzEJrfJV1m6lIKVK7cih/AwP/5ejC3gKcqKVn1Q==", "signatures": [{"sig": "MEUCIHxQ8hiVjtM4+X4hRsZVf6DD+tZwn0e6PxDgy3hejP5OAiEAwYqWg/lfFDd8ZsBgm3Zlr/EoCZiQ41yWtYu6cYOLLps=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47202, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpm4PCRA9TVsSAnZWagAA3nIP/RSeBxYooEaSUBDnvT/+\n/SCkWQA9EgM7QtQBHwaz8uvr/n9NsIG7Q1eg87KnoyB+NT6t8A5R4ZBPum4Q\nSQ9gEKfFsPPDVw+H651+o5skjq35m78UtNrmZrM7AyuwX5Ivnl4yAulCKD7a\ndOLkBDgLcgexdgNIWZ/JlWVWNi6mTS6X3uXMjAxrwgwK/6AESyVmOGv7Pr0x\nZ45tLdD2rr/W3HoZ4lRmlVAYa4jnShNeqr5wvp4ZhQQGXeMpFVjJfRKwTiEQ\nuk0HbAfZ247f2K5/EgRNafyq5q/bLcsecfR3S4Ha0U6MG7+Ti3RQmjOw/lCk\nGCeTrJ/oqJKbVqN2gsgrvaoT0pLGo4AOVLDVoi25l50QPig7L6zRyAnLFIiH\nRLlOySRjGtA5EQVR454Ueod0XtNicZbTbbaVK+z+JSCXiBtq3HXW7dh4sEem\n6I547bivWd9PUM0/vpm2JQV2YjJ9+rZrN/jGG+TNWNxtVWj4sU7h2jGe3w2V\nCZ2vQ/PhN3r2GImhaKfyzte+C3656Wf/wvCJdwdPTrqAE2927R4FZ+vsM737\nO9qDpZ8/CgI5L4P8voJEBArPNrGU4oIV5raVrvI0ICBWqoL+qBFfz0mQxdJ8\n0/+IVRL/5CKdHlvRNTzVk8Jtb/WAicv0DASa4PFbMd+fG1Fv1rTjGkYMFj9s\n8VMy\r\n=oF9I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "6f44529270310b7dbdf9a0b72b21b5cd50fda4b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.0.0-next.10", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.0.0-next.10", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.0.0-next.10", "jest-regex-util": "^27.0.0-next.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.0.0-next.10", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.0.0-next.10_1621519886932_0.8982538156729731", "host": "s3://npm-registry-packages"}}, "27.0.0-next.11": {"name": "@jest/transform", "version": "27.0.0-next.11", "license": "MIT", "_id": "@jest/transform@27.0.0-next.11", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d1920a64da3579aa579eed1c81df0db6bea7d1d8", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.0.0-next.11.tgz", "fileCount": 12, "integrity": "sha512-QXVeQbc1pzBPnoVBP3e5cBnVOQmnbQvTXMNNY8/+tzWf811RPJnYmQ21E5FSDLbkpKPtXBHBhaxis2/0a7BYVA==", "signatures": [{"sig": "MEQCIHfO2d2xPwZSqGgt7/AfiOnltQHsQz5HMSCybSOg+zN2AiB0Ungv8iI8kbzwgJ5OpMwHdN3wmfUMzGDBBrhYZbhCUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47202, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpuKmCRA9TVsSAnZWagAAOPYQAJyzVSQQ4jHsGO4wlAiv\nz1XhQS1Isj5NLrZK37ZsVaBM3d7lXxbKKTPfp0Qm9db1d9uKTRaA+X6EmR0q\nPV2VODNpe/xqwnCi5TZgRTwUwUBAxVKZfl2xXvI5sE1+voax4WBij88exqbF\nEo3SAc4nWfC2HQSkOCP4eji5Z7X19wpvl7NRVOOH17HqpRE02JepSM84fwWv\nl1JiipI9KDVfgGT7X5pCRqnBOS4cNaHRGIrZVV9IKgAP9V70cK/tZJerpxty\nuETS5Znb5jJPkeNsSM04xTgcvFp+dJb9rEVkETnmeB0h1fMoYliXYg039acn\nNwj+L8Sbl7RtA+zDp5ZXc4IEOi+AoZW9tcwodxfOwiiUerG6JdtoY6g87tdD\nGyqhsiDxiOx1yWTglsu3WCDxbAov2Z6TZgvQSrKDxmQldkc4cg3DuVjf3cQb\nNRCyNbXkNEgZs+jnKSTV0+XmtPzC3QAZCZREqFfMKTUTjNi+DUirTgc/rtWb\nOAq0Z3Z4onFbNnwtgJGquHicPfQPvBi8hij4K3o7u3G8XJE33weLRr6TqQ45\nblY7XCUfeRdAmaBQoxeMXS0mNfAssH/kuTMd8gkni3Td5ZVZq43PLB8Aw9eB\nEZLt9dHcBdF3JYkS9+6ZWnGaemHTLanceOY2ad6/0rZ0A8JGNwNHuSRqWq4K\nPdPg\r\n=KXdw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "e2eb9aeee8aacd441f1c8ac992c698ac4d303f60", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.0.0-next.11", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.0.0-next.10", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.0.0-next.11", "jest-regex-util": "^27.0.0-next.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.0.0-next.11", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.0.0-next.11_1621549733642_0.6468442800332967", "host": "s3://npm-registry-packages"}}, "27.0.0": {"name": "@jest/transform", "version": "27.0.0", "license": "MIT", "_id": "@jest/transform@27.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "88f856750fccbb8edca2cff0dd0806588bedee17", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.0.0.tgz", "fileCount": 12, "integrity": "sha512-4E9dIpqb4dBfj0ph08NqGONKJvUm8A2Vb0Tr8bsJcztTsafGwQ+qmy3fRLYfWTGwbGuRBojpwueN01vXpGVu8g==", "signatures": [{"sig": "MEYCIQDu5xDzaVlh4DyuIc4PXS7KqKxDgCemHmbarSWeSmZtaAIhAOFYJABELPV2pG+BdoyEnOTWL0i9cwTUgy6U2ShI16jT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47178, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrLISCRA9TVsSAnZWagAAAEwQAIMWIQkZeW0JQiRxLnLj\nX2P9+J2+CIDt+MTnxvIBdxMsrq2QSX3YxAuDJEHK52c59X7l2d3XTBdIiIo7\n52B281EghrYM5mY3KcydCZs5T1/hbGG+PnbT8+BDugZPJXF1Oag6vw31LFEo\nybA0zPRzcoY3woCTe4MP+3pmBWOWEd4EnSwDMMJvSkxoR/5DpmSwa9Kmn3ic\nhyrkG7W3FH3F5n7i3evgmAh4/O8xqrItxkrS5L71vH4s/AqiOxcJ6b7xv0oc\nS7S/vu2Uf+/tJcbM7m7a+2+N/a5yXfLdwnvTunpksAJGF+D2abUkLZ9gyx73\nu8FkdLPGbEf5aMT1uqAr+wTrnCfSrdGczZdZJaKpsgMaJ2NPln1j8ZESGRxd\ndA50MVEsFkV18VnyIHD1nykd+fyCD22O3FPDTm+by+ezosdLpEblutzQV+su\nj831zSTR73TNbEifCR4qbuIzFnqM5DOxXohu51My93ynLwTdz6qvPiX/qUtM\nIJJG+bp6kiY/WEuy+h/tP8jlQbNTwPJ5N/06hRM9M+VDqT53Z8VCeKS9pI7D\nRvevGTdOkP10QTZ0ondy4+pK9IAqcoFev3w2UxGB8i7PWJdkuddIAk/MdZAi\nxKRMmJNqE8aURFdENw5OnUbxCAmcwD9dy9TbZGSUmw7cZcJiczEf6KirInwL\n0E/S\r\n=SNr7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "be16e47afcc9f64653b9a47782cb48a5ca243e65", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.0.0-next.11", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.0.0-next.10", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.0.0", "jest-regex-util": "^27.0.0-next.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.0.0", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.0.0_1621930513846_0.290453236732372", "host": "s3://npm-registry-packages"}}, "27.0.1": {"name": "@jest/transform", "version": "27.0.1", "license": "MIT", "_id": "@jest/transform@27.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a9ece291f82273d5e58132550996c16edd5a902a", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.0.1.tgz", "fileCount": 12, "integrity": "sha512-LC95VpT6wMnQ96dRJDlUiAnW/90zyh4+jS30szI/5AsfS0qwSlr/O4TPcGoD2WVaVMfo6KvR+brvOtGyMHaNhA==", "signatures": [{"sig": "MEYCIQCDnsojqOeOmelo31eDIyEipfN8m12YiWSMj30mveDonAIhAKYXMXWARlTYx0IsSEXSLK43iLyCcEDTiVWoisE1idP6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrMwtCRA9TVsSAnZWagAAzRgP/iouY75ECYVxey8cRS3L\nTgN1Qq9UUnGTndG+QBDG03Ef/BTAnbOT492KCVvqkQ9TLhF5OhWHExkly7ca\nKkUGl8lIXAmf0keNjd+FqRqWpxfL4mOZo0mkeO0wrZofmyB9ffKwkwCFxcJt\n1j6J0ydqWwt6HeK0lDK6P91L9HD2jYI5cItjZvEZQSGbFZ+N5NFgeUk4Hc6w\nt9MKAyVieb276j/zdYjc3ewQdPI9PcFT9+04OFAIEA5B2YawvoNEL8XjYPn1\nWIeaOcRRt6VbP79XYsyUtkdPjY8R7kUVUFbXb2fUGI7xcib/cbzUSONJguKH\nNMFj2dhyEmb/tE4aEHErXCfbGw91RpjTqIvYt9ypxRyykSJtVD0rJXY/PBzx\n0AEhIdSvduPGXQYtgYt5SEryC5hhqDXMhBZCR5ztPummlfiKV3aBTM+m6h9C\nFzJGnczejn51FNZlTq50weE35C++afUcYLDLmzx1YzC+t5/SriqV8nsJq0h9\n1q8fUq5b2ev8S9qTZmhbz5wHNzIVirbr4j5gNJgPGJtBmWh2kzXbPi6XhJ/g\nkEPhZQEpD5h7PK8lKog/3Zj5L1XP4twGskLfNqmyO5GRful/9hqXhmqnN+YZ\nJ7dAYzZNgzVO25hqlkMwVb1KOLQAUjqhuRYQYsrH+yCpqj9Dg9v1Bi4ZUy6F\nGLVS\r\n=ZYNL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2cb20e945a26b2c9867b30b787e81f6317e59aa1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.0.1", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.0.1", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.0.1", "jest-regex-util": "^27.0.1", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.0.1", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.0.1_1621937197237_0.032924457880809", "host": "s3://npm-registry-packages"}}, "27.0.2": {"name": "@jest/transform", "version": "27.0.2", "license": "MIT", "_id": "@jest/transform@27.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b073b7c589e3f4b842102468875def2bb722d6b5", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.0.2.tgz", "fileCount": 12, "integrity": "sha512-H8sqKlgtDfVog/s9I4GG2XMbi4Ar7RBxjsKQDUhn2XHAi3NG+GoQwWMER+YfantzExbjNqQvqBHzo/G2pfTiPw==", "signatures": [{"sig": "MEUCIQCcGGsiXGW57G+QwsitpOxsLMME/4Zmf3KySbBl4X07UAIgCbSgGOOhWhOT2TDHbB/vHHPIpaL3aihyfzn0+gRLFKg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47174, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsi5/CRA9TVsSAnZWagAAxqMP/jU2b1H/g5vOBCncM0rv\nd62WZJ/G/miTF9AWGJPu1beouQaL1EZE/jYKnYKWdJPEAW8v5ucRGXGnNBah\nRuLHgs5+Mt/wi4njJHS0zD3Bn1talH036LGub6p9ZYIkUNP3D4KTAf2baK7v\nCRptTsk2n+k77HXKy02Fh990Ra47x9jTenCocMSkfJcX/ey9ZxKE3ATlu5CQ\nfAFVSpuY+xxfTRV3Cg5f1zHY1me1XIYiDRTBZiZ6qxVOxtI3sNBGxKvIxZvT\n8A6Rrb6WBFKlUQNdqpKH5UL3E3iQZoobg5a0PixYMY77nX7C3FyTxCtHMzcS\nOEuNN1VX3hkGwDU+NTZUbr/ebolNYTdaShfRLEPvH5McHeNlCwdf+89BEeWV\npeYhfeB4qVkOJMZpSxOBCPpz7brJk+8y7lMdU3cbVN5586KPFOACK122qIGA\nzAp5qzRj29kVr709aU4I86JFIlAMnCr8z9f4+4WI7AgXGS3fbRurjU9ZI/Lc\nV13o/XvqTRes56pwIs7dFKsuHqCb9L1QervykMWdeKRYmxo0gIytAN7L0BIL\nsQjLXHJUiBYf10GfD4iGYBwgln3hFmm+ATkesF5bFy2eCshFFffuEwGwV8am\nqy3VOUbHZNSburhrPJLhPhnv8tPUBZbZ5unZPc4xzd2PGz72PeSvjdrn4b7Q\npHEz\r\n=EX0b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "7ca8a22b8453e95c63842ee6aa4d8d8d8b4f9612", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.0.2", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.0.2", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.0.2", "jest-regex-util": "^27.0.1", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.0.2", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.0.2_1622290047379_0.4076150111508676", "host": "s3://npm-registry-packages"}}, "27.0.5": {"name": "@jest/transform", "version": "27.0.5", "license": "MIT", "_id": "@jest/transform@27.0.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2dcb78953708af713941ac845b06078bc74ed873", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.0.5.tgz", "fileCount": 12, "integrity": "sha512-lBD6OwKXSc6JJECBNk4mVxtSVuJSBsQrJ9WCBisfJs7EZuYq4K6vM9HmoB7hmPiLIDGeyaerw3feBV/bC4z8tg==", "signatures": [{"sig": "MEYCIQCQmLTInLvDvAawJUnKmIxQeNngnVgWR1ZgbWIQrH7evAIhAPcax8fIIiKHMK/T5BF5YAmMd78UBKS6smNyvVhNVbz+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47174, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg0cU2CRA9TVsSAnZWagAA96sP/3b7GISc8z7ly1EFP8wN\nRCf6QDCEF8wuIJZiq885XpRB1t85rpk5GGLyoqQkqKSwS9NnZOiQLtLIicJD\nd9TjjjKAuPDEzu7TPiC8/pElD1izVo6jnWlIUr/yF5hyJf6b6XnnChOou7I3\n5afOl2QXPB+2dbPaw5yQnOMh4ULZexekdbFtPfem30R4uP2gaW+BRA1AkJjH\nCeqmRRUuG5+EBOUSYeBMva1ewReiGC2t4BkGl0jc7PRivLL7n2sqe51xZHJr\n0BtN7hZOygTeCG0YOYOSolcGHrxGqjAGeiR/00C6j8C9518zW/6JgwakVoTc\n8E6kYS2ane5DkMftZFYVoWmEybtR+T0eKpYMAvGQYfljT9kGWunrIc56tXVi\n/twrCrxZgqVbDNFGLRxFTLPBZ9weWAWUtYntH9lsaBruSSdJ6rkHRTYgUH4i\nnFlQUeEFIyTyUWJkY9sDWkogjtrWGY4IXR6TVG7qd069Z9h17zRQNxVw9PZa\n9zjxxV+ow81gbwO5xofn0i/Qk2t8NTni48AhwnRA9Gq/G+bjecuhWuN3D1C/\nhZTj06wjPgZKum/keU4SDdZMspghEVwtdhvIi4IJviNH8TIBW1pZgrLki/Um\nqd/XV8cjCkJvKW51eqvO2PbEWYIjV1S8Kh85AwLcB/xsUk0DUAFNs+07LML4\nFsLC\r\n=u8TY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "dafa4737dc02887314fd99e4be92781c9f8f9415", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.0.2", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.0.2", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.0.5", "jest-regex-util": "^27.0.1", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.0.2", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.0.5_1624360245959_0.1133059735271782", "host": "s3://npm-registry-packages"}}, "27.0.6": {"name": "@jest/transform", "version": "27.0.6", "license": "MIT", "_id": "@jest/transform@27.0.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "189ad7107413208f7600f4719f81dd2f7278cc95", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.0.6.tgz", "fileCount": 12, "integrity": "sha512-rj5Dw+mtIcntAUnMlW/Vju5mr73u8yg+irnHwzgtgoeI6cCPOvUwQ0D1uQtc/APmWgvRweEb1g05pkUpxH3iCA==", "signatures": [{"sig": "MEYCIQCvz9ttzw8ud/RPoVD4Z4hV8ct4dLvF1fVGN5dAGBeAygIhAJiUOUGb1R+5TrlS6M0zo0cXTOTPCzwl5JI67kSTWSiM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47174, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2gFsCRA9TVsSAnZWagAAy08P/2+UiROB/borF8LHYT7a\nyzAHb6VHPWQfHqI1wtUQ+rGHplxijY3FtH2PuA9nPEqufHcRItM/rX5Roz7P\nJ140s9vcp+fSlIE3XZkALlu7vTX96SjP8Ls9O+Dfh1muBUpif89I2XuWosO5\nBnWE+nxl6T+uLyNFNlCUHS+6Ts2XsGVicDpjgkezCQVOeI3qUOCYfOdxUV9X\nI1M5RlxbBOJdL6R8DkAiklgzlIFgoBUZhDBWpmA7P+MwT9PGI1MnwFP1gQsl\nQjkeG/HDc6huZBZhPK05BkzTBvA5x3UHSApGI1flxpCGrYK6h+W6SLoCeXDb\nHoveyb1NVzzU+u48VvG0F/zfheXxJyl638umFm+xgCiLtVhL9yE6rsZN2i7T\n9o4tG7nphvI8YDMaMAeU6GmhMBf/MjELPexnb3wnGLnOVNXnlquqJaR5tlFr\ntJnQ2DRKotuf4SQ4mKKf3I3sAj9GfsuwPV4u70Ivq/usHvXi4kCeo1BjfywQ\nzf7dqw8ipIeUomlM2VocqkN/oXCjfCY8O3/M+Y66vo1E1lD/qOKJbnases2M\nzTlrC5zxfsQefNJUJxeagr3YoHlAxonv4M8KYBk6JUM4piBLVjwPDdVWBLhi\n8myNlaFCRuetkYReutqHy9A3O6HPWU+wa5Vh9AjvHEXyit0Xe5WLs2Aug3HI\n08M+\r\n=QAcP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d257d1c44ba62079bd4307ae78ba226d47c56ac9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.0.6", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.0.6", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.0.6", "jest-regex-util": "^27.0.6", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.0.6", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.0.6_1624899948414_0.021642638990232443", "host": "s3://npm-registry-packages"}}, "27.1.0": {"name": "@jest/transform", "version": "27.1.0", "license": "MIT", "_id": "@jest/transform@27.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "962e385517e3d1f62827fa39c305edcc3ca8544b", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.1.0.tgz", "fileCount": 12, "integrity": "sha512-ZRGCA2ZEVJ00ubrhkTG87kyLbN6n55g1Ilq0X9nJb5bX3MhMp3O6M7KG+LvYu+nZRqG5cXsQnJEdZbdpTAV8pQ==", "signatures": [{"sig": "MEYCIQCtLmNsnOUPQu7rAnSjhkNOMZrGZ4Oj+Aj9CnWpv9YLpQIhALmSlmBTU4II1f8+PpaMYP0uL0rUMmVIYKRI4me1Jnnu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47174, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKLeRCRA9TVsSAnZWagAA7wEP/A3ZJnRii2JWA2fpkbJ1\nEBtTAn+Q6kcC9bi45vItWbc2lkdsWmQ0MCAFurazIodDv5zsclfnP3zuWcLv\nFCHTISc6LVWQyXtnVz3OBrd6yJdnZNlzY9e+fj8R804TKLLUhjnmjfursykG\nqzAjIj+qx8avP7ZsDSRtcRv49taVwAxlrpNUOkK/mkX7YiR6/+Fqs7eqtF2Z\njNf0z8DI5uoWYCRiu8NUt1/Fk7PiYN7arP3fZHAMAsS7RR0dwFsbv/2H4mD+\njB/xDmW0qa1eub+dQYXVrUpOOriD+qNMgRUOT0/B+aN0rSLY/W1jaeP+x2Ln\nBJNmcP0Oc+d9ea0RtOoPvvFChR7S6azihNkqrtO17r9iSv7JJ5oE3qzZQafX\nbKJUw3Un3ppjXOB5Vv6lq+Oy5xy0H3U/e69mEwH/G6Bg0vrjLSMl++uMTtsr\nrNarneCFdeNAHR/RdrwAOtdUoW3h82gy2EtUt/Q7kql6nXSJZrS0Ig2/+x5+\nRJFu5BhzhbYzsJAq9qL1Ri44w4ryyctqUBkh4cv/VR8v9Vbn3ec1qgrfmayC\nT6xfcR0wSoBTVxDk5/l9EV1wAxWgL9AljVJ2c/3/gtfP9T9IeXZ/0PquHCic\nctyXNXLbyo2eHu7lW231drXYaYKthMoELZzVBubx7y1ZOyJnI3JlVGkmkdya\nXgVo\r\n=UNp0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5ef792e957e83428d868a18618b8629e32719993", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.1.0", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.1.0", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.1.0", "jest-regex-util": "^27.0.6", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.1.0", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.1.0_1630058384914_0.5890331789425061", "host": "s3://npm-registry-packages"}}, "27.1.1": {"name": "@jest/transform", "version": "27.1.1", "license": "MIT", "_id": "@jest/transform@27.1.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "51a22f5a48d55d796c02757117c02fcfe4da13d7", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.1.1.tgz", "fileCount": 12, "integrity": "sha512-qM19Eu75U6Jc5zosXXVnq900Nl9JDpoGaZ4Mg6wZs7oqbu3heYSMOZS19DlwjlhWdfNRjF4UeAgkrCJCK3fEXg==", "signatures": [{"sig": "MEUCIBbfOt8Xwl/Tlc7MNef69mh96cYlrTy2GcYQxkv+QShpAiEAgLbV23pFx7WDHvSACJoJaX0tzu6+2QAWpaI0rhZmRP8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOIyBCRA9TVsSAnZWagAASn8P/jOqJmjVwjhHGsyCxqh6\n1vldwAb7tgJRZj6B+uTnjn9GVImkJxsm8B23VAxK5EBadqBekv6eEpEM+CNR\ntYQv/yZWMjBh1cLOGIbkx/1BVLft3+eNBUFt4r/zoSncysZGGm7eaUdPB7YY\nW2VOJ8WZ8EBXZrvoqMP6rfmG8wZsOVY7bwXVjgO45eO1CO+5YpzNrvOrcqQM\n3zzuLo+SMAwiJulBz0Zc1YA4FvITdyXeErnh/YdTS6pEBVVJ3+W8bJd7pue/\nPa40+nwOWTXPm54dA4xdsSLqyX6WOh0ZqLhKD7Wj0IP6UyEOks0N+CeYSbkf\n5ReJ9W7W+dO4hv+E4bed0WYcrbqiTKcnJGFH4HB+BfOtTc4TYY8DgPJp6zpg\nk9s1PxiWuaQ9esQy0stXzoWmn6FEqXOcVemsc2hOUrnzE+skxaTfVEUBnsv/\niuoGZhPHvjl7x0q2djvvvDiq9K1778H4UJUoUqREXq8ySAgN7555ZiVf3sWZ\nLt9y4ux1k5AWTfHcNKaSli8UtTrLnMUDc2LU2KS/eDaMOJx1HfSriurojyMm\npU+5kITHdektxhGWcY4/yr+oHGzZor8JYLXox8LD/zFxAYNVb2guipe5QTPD\nf+JGZvazemwK4ftWiMwPeFHRhVrTfJUIFKNnp3GnbaT/zIxGmykKZlHQijHw\nSvID\r\n=tQPU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "111198b62dbfc3a730f7b1693e311608e834fe1d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.1.1", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.1.1", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.1.1", "jest-regex-util": "^27.0.6", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.1.1", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.1.1_1631095936963_0.3067826074316029", "host": "s3://npm-registry-packages"}}, "27.2.0": {"name": "@jest/transform", "version": "27.2.0", "license": "MIT", "_id": "@jest/transform@27.2.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e7e6e49d2591792db2385c33cdbb4379d407068d", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.2.0.tgz", "fileCount": 12, "integrity": "sha512-Q8Q/8xXIZYllk1AF7Ou5sV3egOZsdY/Wlv09CSbcexBRcC1Qt6lVZ7jRFAZtbHsEEzvOCyFEC4PcrwKwyjXtCg==", "signatures": [{"sig": "MEUCIGjGmR5rjldYJXakUWSHwJA5qKPRCyce2Cd08+oJbSr+AiEAxVQx2NyHpGYlvX/NHlRIGuvcxrJPlLHDAeSD5FoQgc8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPwaUCRA9TVsSAnZWagAAoxUP/ju+nG/zn6rJoPCtCxfH\naGwmhazcojR4cNnCzOM/36KybiUo8gbR0SVJO6oF1/qVYBhbFCDp9cFm9S1z\nSosYE69T4WQBJVudE/8QXTINzqVvlASMSLSupcpSNoc4ZVCFQ3N7Tqe4EY84\nCdSJzsYim9F5ojdMI/WFs7DXsZXcrMpopUEYT6Dl+hYMZBCf8cGA3slNs8Ab\nGUUdwjGPHlMpzVbXvj2dkueeCpnDzUbUTC3iq9Ny97XKbOT4SJQ0ruKaknaK\nHEtaw7x2r1HQKrCa4rpCuSmb+nIRHGLpBz2qRItM/lWP/i53zJhZusfMvrwg\n3ZRNe0ywq4GUeohuofWTdyAvI/x/hjSId388j4VVAbR7gMty4mDQyYlFPIn7\nJp8nKY8QOD0MMJdYO5DVeV9ZOJowLDdAYrPYn7oyPMZZYf6fD7XTMg2WUf0J\nraIgmvKl1PSAeoHz3SbHqkVqJfJnERcpIBzQZj7f0hd8q5PiXXnEl6a/Pjt7\n0F3EBt6zWi2BLgKdOAXs4JyXYujmHiH0tCC7+75e8CPSnQRclR8Cujb5SbAm\nyYmMI1G5kLIl1LE1bOtdah5cR0iN1nrEEjd5MdkuniEiCzYfvwC2X2oou+jy\nLMyAkeXlibKZEmONGIiGAE2kq4OiXvAPxlsZc5xiuqRMJPJx19Pwzks9J+Eo\noSCj\r\n=90zT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "b05635c539f8f673dfed5bf05ea727a8d5d7bbe2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.2.0", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.1.1", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.2.0", "jest-regex-util": "^27.0.6", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.2.0", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.2.0_1631520403879_0.6260848186382797", "host": "s3://npm-registry-packages"}}, "27.2.1": {"name": "@jest/transform", "version": "27.2.1", "license": "MIT", "_id": "@jest/transform@27.2.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "743443adb84b3b7419951fc702515ce20ba6285e", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.2.1.tgz", "fileCount": 12, "integrity": "sha512-xmB5vh81KK8DiiCMtI5vI59mP+GggNmc9BiN+fg4mKdQHV369+WuZc1Lq2xWFCOCsRPHt24D9h7Idp4YaMB1Ww==", "signatures": [{"sig": "MEYCIQCZpxVmJWmSCOmqyoTr/TDOuU22OXaMIyxJ6HF/SXcVSgIhAJf87FrzjIJrF8PLKBU2r1HMoEB3JQrWSqszrtV55Jtg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48022}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "50862b410c358107ec893cfdd9bb9a689ad8e622", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.2.0", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.1.1", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.2.0", "jest-regex-util": "^27.0.6", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.2.0", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.2.1_1632144482215_0.9959716258031954", "host": "s3://npm-registry-packages"}}, "27.2.2": {"name": "@jest/transform", "version": "27.2.2", "license": "MIT", "_id": "@jest/transform@27.2.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "89b16b4de84354fb48d15712b3ea34cadc1cb600", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.2.2.tgz", "fileCount": 12, "integrity": "sha512-l4Z/7PpajrOjCiXLWLfMY7fgljY0H8EwW7qdzPXXuv2aQF8kY2+Uyj3O+9Popnaw1V7JCw32L8EeI/thqFDkPA==", "signatures": [{"sig": "MEUCIDkMC+GZIWkMXH4jWEIvEaW0yAl9XkVSEJ+4kMTEAx6TAiEAq7ab4zl9MvkrkQZVmHfDzBXSFXf8dnWE3HOSWojkZEQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48022}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "f54d96fec55518640b900d6994b2c4153316d1ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.2.0", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.1.1", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.2.2", "jest-regex-util": "^27.0.6", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.2.2", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.2.2_1632576910326_0.38057229358650724", "host": "s3://npm-registry-packages"}}, "27.2.3": {"name": "@jest/transform", "version": "27.2.3", "license": "MIT", "_id": "@jest/transform@27.2.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1df37dbfe5bc29c00f227acae11348437a76b77e", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.2.3.tgz", "fileCount": 12, "integrity": "sha512-ZpYsc9vK+OfV/9hMsVOrCH/9rvzBHAp91OOzkLqdWf3FWpDzjxAH+OlLGcS4U8WeWsdpe8/rOMKLwFs9DwL/2A==", "signatures": [{"sig": "MEYCIQCmKUwFaUPr6IU4dNw4PMVVQKE7NFOgWgxRtpoE/OuZBAIhAJvmhXKaIdISIY3FGWoigI3PckWq7BANlr1AnCSmOAtP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48022}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "ae53efe274dee5464d11f1b574d2d825685cd031", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.2.3", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.2.3", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.2.3", "jest-regex-util": "^27.0.6", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.2.3", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.2.3_1632823883868_0.6650021154405652", "host": "s3://npm-registry-packages"}}, "27.2.4": {"name": "@jest/transform", "version": "27.2.4", "license": "MIT", "_id": "@jest/transform@27.2.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2fe5b6836895f7a1b8bdec442c51e83943c62733", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.2.4.tgz", "fileCount": 12, "integrity": "sha512-n5FlX2TH0oQGwyVDKPxdJ5nI2sO7TJBFe3u3KaAtt7TOiV4yL+Y+rSFDl+Ic5MpbiA/eqXmLAQxjnBmWgS2rEA==", "signatures": [{"sig": "MEUCIQDA/dhryB7HKdHyIYDydejujwQclmGUelzMWolBK+pEeAIgGZr+9/pIPTMRPdO1AG3IBixowpNBxKJLYBj08dB5JuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48022}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5886f6c4d681aa9fc9bfc2517efd2b7f6035a4cd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.2.4", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.2.4", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.2.4", "jest-regex-util": "^27.0.6", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.2.4", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.2.4_1632924289927_0.5736346840934474", "host": "s3://npm-registry-packages"}}, "27.2.5": {"name": "@jest/transform", "version": "27.2.5", "license": "MIT", "_id": "@jest/transform@27.2.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "02b08862a56dbedddf0ba3c2eae41e049a250e29", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.2.5.tgz", "fileCount": 12, "integrity": "sha512-29lRtAHHYGALbZOx343v0zKmdOg4Sb0rsA1uSv0818bvwRhs3TyElOmTVXlrw0v1ZTqXJCAH/cmoDXimBhQOJQ==", "signatures": [{"sig": "MEUCIDu3iQuA8pLXKKe8XgI75YuXcC+8hA4FC9npMT6l0jplAiEA9Fl1zJRpp0y7jm6N6XkkH5p4GLFUTqNJx5r49AL1Lak=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48022}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "251b8014e8e3ac8da2fca88b5a1bc401f3b92326", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.2.5", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.2.5", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.2.5", "jest-regex-util": "^27.0.6", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.2.5", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.2.5_1633700363331_0.8484081069964671", "host": "s3://npm-registry-packages"}}, "27.3.0": {"name": "@jest/transform", "version": "27.3.0", "license": "MIT", "_id": "@jest/transform@27.3.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f2a63883eaada30f8141938ec1ad23ba7fdfb97e", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.3.0.tgz", "fileCount": 12, "integrity": "sha512-IKrFhIT/+WIfeNjIRKTwQN7HYCdjKF/mmBqoD660gyGWVw1MzCO9pQuEJK9GXEnFWIuOcMHlm8XfUaDohP/zxA==", "signatures": [{"sig": "MEUCIQDNjLWs1+Rl0jBPUFeQkx/mTNdpN7ByEmLxR0r/fSk27wIgJj5yAN5xRwqAl07RJYzmz/o3JBQ2AIWMPZog1keiKx8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48022}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "14b0c2c1d6f81b64adf8b827649ece80a4448cfc", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.3.0", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.2.5", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.3.0", "jest-regex-util": "^27.0.6", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.3.0", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.3.0_1634495688307_0.14837551254335923", "host": "s3://npm-registry-packages"}}, "27.3.1": {"name": "@jest/transform", "version": "27.3.1", "license": "MIT", "_id": "@jest/transform@27.3.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ff80eafbeabe811e9025e4b6f452126718455220", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.3.1.tgz", "fileCount": 12, "integrity": "sha512-3fSvQ02kuvjOI1C1ssqMVBKJpZf6nwoCiSu00zAKh5nrp3SptNtZy/8s5deayHnqxhjD9CWDJ+yqQwuQ0ZafXQ==", "signatures": [{"sig": "MEYCIQDrHiNJberRMvNUuEcdvabc5pHoPpQjJQyZ/4+gev/ZpgIhAN/a8pWII3zRGb3yOKd624xQFhQchSKkzYfbWxBpNHwN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48022}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f3328f3227aa0668486f819b3353af5b6cc797b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.3.1", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.2.5", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.3.1", "jest-regex-util": "^27.0.6", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.3.1", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.3.1_1634626654624_0.6843584833084178", "host": "s3://npm-registry-packages"}}, "27.4.0": {"name": "@jest/transform", "version": "27.4.0", "license": "MIT", "_id": "@jest/transform@27.4.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "060bf842d3ac162c50c684e8422f3bfce6c824c1", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.4.0.tgz", "fileCount": 14, "integrity": "sha512-/8Cb8kEoCtXN/Co5lvv+jG0zv4Uj3ruIvffYUzxNGRGmM7qqaHtOBZ3WbH0T1Nvjya5utTA4YtwbInZVS6Zt9A==", "signatures": [{"sig": "MEYCIQDVsQbjh7UYOSj6ue+ExEFyqlf+WKAmhFihJYofTdrr/gIhALCLUXmpPNrUwcxT+FzKHf5xguMO+OdHmw7YH2UpM5Ta", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51569, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpNeTCRA9TVsSAnZWagAAf4gP/2Aie8s0QZb9IXtOD9J4\nHvIr74uMhYqcsFv9lzFtHNHlOrKv9ZOIm4Co8AuwE9dDheRdRdextV+5P1dW\n3ZtX2XK25oERi4Fo0jxuhqOeKSN/pt7lUHj0iBGoPTjPY/O1s+6ogRp1C3pw\nYR540T95aOQQwaD4UzcFFgqlUjU+v3wMFmJT1OilH+ppObsKvraMiE9tzipr\nxN2eL1wfCFRE6Q6cgsdVNUa5B87tEM6yo+pqvttXLwKmM+D6zGjhHq0gv6Yy\nAHQzZBGVmf5bq0HS/RyZkf5YQm7FwfXyBx1HHPjmc1vrbR7FSP5G4IWnmG2m\nVrtUy695iny29PSNZdjLt1psssjP5+1+7fjo8BqYmfnlG+knbEmwXG82t/9G\nEvX0+FN95Fx5dFtdEDL0C8EKEzxLxnTAMQ6jL3/CWlgynubIutloOeJ8LAz8\nomvZGpGqCfirKemzFLnzrNh9UUOv4PnID343yACJVAxUF2UI4bJ+OQuxHIDI\nizfOMUskNcphd7wr5N+9N5t2wM4r9sgAKj3AMMRNw5svah95e/Xc42g5/KB3\nAypQ1ZEZ39g+hAjvxXf3S8fMV2oGvG6HgFkzK5muPNtoCRf/SzjgKpHrxTvt\n+rwz6vEIQ2HLKMsKnLDxAjoXTDzq3er00ZMihCdyQ9tTI/xlxI/olxzt0lDO\npCBm\r\n=WsvF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0dc6dde296550370ade2574d6665748fed37f9c9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.4.0", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.4.0", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.4.0", "jest-regex-util": "^27.4.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.4.0", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.4.0_1638193043785_0.4487534700562721", "host": "s3://npm-registry-packages"}}, "27.4.1": {"name": "@jest/transform", "version": "27.4.1", "license": "MIT", "_id": "@jest/transform@27.4.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "17c8a29402cc693f537e1ab9d68c17853f5000de", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.4.1.tgz", "fileCount": 14, "integrity": "sha512-/eoX7PpUnKzZ8UDI/j/1jGWSEcP967MMEpi2XHFcYlFa5X++MvNg+5HSAXxN/6ru07ekYiGTMdu+OqbTbJS/5g==", "signatures": [{"sig": "MEUCIDdk++XgIJua4d2f9ok6tkZsljNz6FsvwL69gWvtHXOhAiEAqI+qZAwjsP8B5/bAyMEx4v78tp0XXWn5rZ+Q3tqA5ic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51569, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpeK6CRA9TVsSAnZWagAAJIcP/1zGWc6u+/QLyH5TdbWN\nvniDR09i+KFn4k7JqVcgKn8zkLDM9PtlO+EB5MEiCDTcXWkJhuj+gUZHMDAB\n17QlUhHer21OzMkMCwLDVK7IfZ4QUr9bVoV/MFdWhNkei6J1kZTZamhzur4x\n7pKcAE9LitC3r0ah9GZwzQTU/jZ6mZP5qkvzNg+axoSnQ3rbNs3SG4xmA55/\n8ZqDWfbcTX1Y3Wgy+M4NGIduYkGtN27rcQn/VGONEmpqKfBZqLZ/kqaMXS32\nttfLbUhU5JMog3cu2x37KMSANyY7FMmSGDziD3u8aIwAQQl3BqUxyy6qexLU\ns568g1ZcEuh0n14WL/6tFhIYJaDesH1Ex4lbRcrTDKPTa7xPHNUF9B/Ut/b2\nko2z8Qp8WTYFmAwANITfOCBr7Yh4xC6zkamKI65sOcntr7Vs8kkDcfMX+skM\npO+9ZrhP+bpy94HMMeL2Xm9OX3AvTfFazYjue2AamHd0aKQ5f9+95cl4VTfw\nWj8fU+zTfSJGYBqoqtI8D/VtyOtS4mr9iMPelwfGjlePz8x95XeFqrlRKiDX\nMJEg0Ff+H18UILIVRkc9LCKrujrNJ0Zyf2eUPKuBW2g6Gko3gtlfNdPnoO1k\n50i23Qip3Sw8ymVt6LfZRZQhjmCMWf/S6lnFIsbDTjdKxeuHKk67LXPtmycX\nWBU+\r\n=gvzK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa4a3982766b107ff604ba54081d9e4378f318a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.4.1", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.4.1", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.4.1", "jest-regex-util": "^27.4.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.4.1", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.4.1_1638261434246_0.9950858905066076", "host": "s3://npm-registry-packages"}}, "27.4.2": {"name": "@jest/transform", "version": "27.4.2", "license": "MIT", "_id": "@jest/transform@27.4.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "459885e96de2e21fc68b8b371e90aa653966dd0d", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.4.2.tgz", "fileCount": 14, "integrity": "sha512-RTKcPZllfcmLfnlxBya7aypofhdz05+E6QITe55Ex0rxyerkgjmmpMlvVn11V0cP719Ps6WcDYCnDzxnnJUwKg==", "signatures": [{"sig": "MEQCIAxeHSUyCztlmS3Eba7FknsCrmiVUEs5e5dTvO1oPz5FAiARrW7kLl58MIpiBvreu0it8NbnWMy9xccGPCLU9SFo6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51569, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhphDPCRA9TVsSAnZWagAAj7EP/jAZJDziXkMj1VpxecPc\nBKh9QaXCYWiSHvHePIfsIgUHp4aSSAzwlNJzIyC2Ah84fceWLuTB5y3JN+3J\n7AM+ryxlefdE66yY215WeFfNmJt3Hdkra+8vLDegrmOlVqTSbm53KYMYCFjc\nIBeca5+hEdn4ZnfYuY+isfZEBI97kLMfFaLUtuMomWdq4zy8nw7UExc7SPih\nQ1ZOz/AY1L6DWtpv00mZcP+CBy7D5oMSHbjxjAJ9Q7F2S7Zn3Kk8H9H4WNdV\nUdG2LMqiXoIApE/GJ6n+I0xjoC4qYb7dMaM1yzgFFcash4aBJyBFuPMJPrK1\n3PUzqi3QaXinSBLnH3weLsSWIbWixMOvm77qNMo58/GkajJjBbanjADujkfR\nuMhZvvIfKY9XMQInZdzMMqAWInQ0H2NjQhCjNANs6KD6YSAmp4GAPfvhAYlu\nz/VXml/ZG+v9E6lzXqi5foWRQd204lwePj0MxictK1+oLKuGCpA5+ajKuVUz\ncXIo9Apnb4SIxoGNJoCyqucuaF+qRX+iC80TpbBdtMEo95u5lOhwD8U7XzmA\nOJwiVHYismKT7fIfruGHISHLkV9G8aNp2s+8AXdCy0QH7NnRWAEA1mIaXBeA\nFswbB4EGqohRUoYhOVKRf5hdkf3kqNOfbzmP4SWAYJWzAgZAP0V7ZKjO3zQw\naiqd\r\n=9HJh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7965591f785e936ada194f9d58f852735b50ab1c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.4.2", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.4.2", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.4.2", "jest-regex-util": "^27.4.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.4.2", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.4.2_1638273231360_0.5012557001432836", "host": "s3://npm-registry-packages"}}, "27.4.4": {"name": "@jest/transform", "version": "27.4.4", "license": "MIT", "_id": "@jest/transform@27.4.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "347e39402730879ba88c6ea6982db0d88640aa78", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.4.4.tgz", "fileCount": 14, "integrity": "sha512-7U/nDSrGsGzL7+X8ScNFV71w8u8knJQWSa9C2xsrrKLMOgb+rWuCG4VAyWke/53BU96GnT+Ka81xCAHA5gk6zA==", "signatures": [{"sig": "MEUCIQDCShcB0Dv4YZNYzMfSHhhP7ZEHo8uZE4wK9mAkSNNy1QIgRdkmIgKD1In9zHQ2Hq6g7lcvce45BKKNghj3taESHDM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51569, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhstrgCRA9TVsSAnZWagAA41wQAIDfGk59CA7ujrJNVs8M\n3gyhPbawYb9OouAmD516Y0DJO633bQ7EJTe5JPdqxGzW6yz+sG0mhv7GmE4w\nRgkXfzdb6y8050uClNfm4Mg6QLIwwEAxcigB4oPzY8S4yrpXG79gptpnwwqq\nwWVI+mSEEyblFZBdM0FVZFGtw+U6j3ayUu/SfC9qAZARaKu0HVn+wPo9s1Qw\nURBXT7Lukfyoqb1VL47In5PEI5gR5rh5bIPckbhA5l2epdXpx0HqL9CHmVIM\n+B7dhWBbVu0KZ4U9RqcRqWQpfvkvmpC8vO/YrZyg2d7hHKEex6wDe8V7EbFm\nTmy9JNZteznn8otR5FhLzHUN/VvFvu38AOJwm1qFI8NuE2GLy3iHppUIBujY\nUb7tCKIujxal0MFt2gyOf9AoTDPaOsrev0MJRMiMiLY7QU8j4mLiuqR9zLcR\nsLX9RpOwS9dzMyiexN2gD9i0kV1yhRMRQXYZhfwL/Cm4voyfhHhxbiNjXBL8\nlTr0Iu53b1IiQ7S6Adco6JvZwZMVGIgyZGxAlFErfEbQZclY/WlN07G4BPUI\nsx3V67sRdFLtivIwYc5cYRfN7UooGNwDieHQeXgXsm/fw8R3/BUtBf+TRX5F\nxDa9ZUDKz2Bt7q+M/MqM8HkxI8whAmGIqq+WC4EwHdQG5w3BP0ZfTYHP7UpS\nHMBa\r\n=5RNQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e2316126b2e4b9b4272e5a0b651c3cb5b0306369", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.4.2", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.4.2", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.4.4", "jest-regex-util": "^27.4.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.4.2", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.4.4_1639111392724_0.22083722557819274", "host": "s3://npm-registry-packages"}}, "27.4.5": {"name": "@jest/transform", "version": "27.4.5", "license": "MIT", "_id": "@jest/transform@27.4.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3dfe2e3680cd4aa27356172bf25617ab5b94f195", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.4.5.tgz", "fileCount": 14, "integrity": "sha512-PuMet2UlZtlGzwc6L+aZmR3I7CEBpqadO03pU40l2RNY2fFJ191b9/ITB44LNOhVtsyykx0OZvj0PCyuLm7Eew==", "signatures": [{"sig": "MEYCIQDaH4jnpOIwr7DdF0DBIHv6WmDVz65hGC6PoaN0Fq8zHAIhAMpjIqNnUgpUi5FCOxwnFsnDqcQEA7uUbpLd922ZtNrT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51569, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht6DLCRA9TVsSAnZWagAAqpIP+weiER87OaqwfjPj4K6h\nsmVs3KGlb+L++iDcQgWmTgKpNM7u3IMMdo8jNVM5HZBZ/T4Xk6pS544gjhrP\nMXafcCRlm3Q+IAiQQhaIZjpYiOiPeMMU5x0cXXrnskEDyzX5DlJS530El0V1\nuVrF2ShODmKYZX+6A/TKr5EF/O10+AUCetRbbPx37JfDbId/jejOulf04GUx\nzvQKhFFZgCTXbH6W3PQzj2s5BHLOjBbeTzW9dAeIN00ied5UeQI0/47+55dy\nyCo4+VasNuDsYdkN4tenk9O4jtvJHq0JhfeERuwI6ttzEW/NxVQ7iWRlP7hc\nVWEPTY3AHMUqjjG/ltUpDe2MQ3qShEl5M4v1YpfYHgO5HPMVokzxnN+TcotL\nKhAiXvnrM+xQSFQWveogbJYv7qrz55y/aK6V0LRaj9YF/DPXzoDHg+Lrd51W\nMGY00fbctLfUHRSJ42xD4YQOhmhepvwDq6j6F6xdGh2tm5OAUku/EW0h9Thl\namyrs9afesw5YcFVFTQEjfDrxSc65oIEkRtRAeuifPRFRIvKiJ3/+lx3nac3\nKw1HaiWWKpkqS6npkrXHo8bidwNWjP/ZqbndDj4hD33shWNa5jwKzJ0zNwgG\nK0Y4ZH7L049N8ccKQepgvDcvj9wXLo9DNexUl20HHvqVvIRc1lDkWyoOYk97\nMk4U\r\n=zDpU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "73f3a5743b9e5b16c9b7a69e2705f07ba7a010ea", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.1", "jest-util": "^27.4.2", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.4.2", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.4.5", "jest-regex-util": "^27.4.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.0.0", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.4.2", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.4.5_1639424203190_0.4219185804948484", "host": "s3://npm-registry-packages"}}, "27.4.6": {"name": "@jest/transform", "version": "27.4.6", "license": "MIT", "_id": "@jest/transform@27.4.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "153621940b1ed500305eacdb31105d415dc30231", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.4.6.tgz", "fileCount": 14, "integrity": "sha512-9MsufmJC8t5JTpWEQJ0OcOOAXaH5ioaIX6uHVBLBMoCZPfKKQF+EqP8kACAvCZ0Y1h2Zr3uOccg8re+Dr5jxyw==", "signatures": [{"sig": "MEYCIQD855+zhuxx7jUOovNEuc1OD2wUHjUERcnbveZxStGa1wIhAIRrRugEpaNSiSc0MYjvzyjGQ5RqMtsmiqfHSI7fhLn6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51569, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1NJLCRA9TVsSAnZWagAA+JMP/jW2ywDqcriwqLxnInHI\ncKcgZhkii0wDP72/K+J8IPNlmXxSU7Pf3Sq8nji2lIrWmLubLNlu+nfpkDuz\nc+IfK1s2gSGINCyhBk2IqKV9bGnEa5w+etFUzHUZXAdupowj8vlKjty2UdKz\noJwcfpMprKCnMJ8Lx47RBkjXbU5ylKjBKSKnVXzP1aGmUpt/j0jyYSo1LcPo\nmKoUca7Agf8CIQIHaZ9PLrVhwqnuUvp8CreY62voy2vyomNz3+TXSdcSkArG\nbJQyH0tE4+rppKwngfZjRhyN0AH+f+FLaLe82759EGAFowMPkCP1dQ04j0OY\nKw/9mnK+g5xgKTYGDh2IQCgVDYncJaRUOR+vhtAgXPg5o2w+QIMY4Zq00e+W\nm1qm+b03fANApgAZQDCCbPsfbmO3D566mY+qcwOo8asME6Q5BUCCC5gJSSJl\n43NmETWWBkOcqcwofgl0ZeXqjnNbecwUh5XL5ypdpHq+7wsQSVHdL7qsw8BA\nG9sdlOqWXGHCN4mNVVJ+asKfe9nzaZm8ohOhYwKMQQHoHDCxF9kxBPxbc7xI\n2h9/X+hcklHju26zbQGhadQtAs9nksiUU4snm/CSlzI7HKMZjZUb2QIrkD1w\nlnrYOHLDXrRljlftyfmxNOqsszkrhrUD0z6HkknNBKv0ZmQkHm6zFZOvZybk\nGvea\r\n=3B38\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "644d2d3e53536b0d67e395c0f35f8555a67beb1e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^27.4.2", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.4.2", "graceful-fs": "^4.2.4", "jest-haste-map": "^27.4.6", "jest-regex-util": "^27.4.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.4.6", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.4.6_1641337419758_0.05367218057480927", "host": "s3://npm-registry-packages"}}, "27.5.0": {"name": "@jest/transform", "version": "27.5.0", "license": "MIT", "_id": "@jest/transform@27.5.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a4941e69ac51e8aa9a255ff4855b564c228c400b", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.5.0.tgz", "fileCount": 14, "integrity": "sha512-yXUy/iO3TH1itxJ9BF7LLjuXt8TtgtjAl0PBQbUaCvRa+L0yYBob6uayW9dFRX/CDQweouLhvmXh44zRiaB+yA==", "signatures": [{"sig": "MEUCIEtA38t6BfdG+i/eDM/4wtVZe7bXTkso9p9lGzx8Q+U4AiEAq3B7ARC3Xqb7pcV3eGABMw++ukRpyNwMJq7OZ83VBAY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51569, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/kqBCRA9TVsSAnZWagAAxisP/1DVtLuBfHppwg1oGXmF\nTAhHfO1jFBz5CMnNs38DwraMre2+J1Dw4ZxJsNin0Mw9rFJSjU6lmi6i59+2\ns2ePwkxhHsuNNkomUVrG+sNtI+ig2mGeueFLWeQGBiqvIuON7qWNxG6NJa8M\nJPv4JrZbg1iSWRSYxyLYLI/MrfPRQHLW11pBrnlLs7pPkTqS0vKWZQde/axw\nWimoYeo9QptVWe7c7oIH6qLo9TWcVy+wwhl3I0j04UhxlsSXm3yLGksQDaUm\nUXABuNErfA3LZ9X2IYjToeqTiWWb9CBTmqMoKx2KT504q1uMArKkWQc8Fplr\ndD3Yk/2aDkYe7iqo6RMxnJGy2tF86ZTbUAdlCwxd+Yi7yzjfjFMZlq39Njnd\nE3O3NtLDWDFqs1pa0C31flY+yIqCb3Smnr00kxLMWSWP0BR4bs1+zKbBokGw\n5+wL3NTCSoEDsIRwZOE7OmeT+/mMzrFZf+PCwsMq7uKC4zgXg71/ghZjkdx1\neEQjzX5rLCwgSuQcYCBGXFqLDGqbZgzo7swCPzcDUHBubUKdsJfujGIj/G6B\ni48qNHf6d955bValpwDQoH7Sq6F6UxicNWjUdlyAb9+VYMFfKr9qycvL39fq\nERRKbP2gFNki8Sg07ZLBuLWBaI7iJu1dg8NhDj7uqK6ZR+TnGW50xQVfmwcl\nsJoy\r\n=xThD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "247cbe6026a590deaf0d23edecc7b2779a4aace9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^27.5.0", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.5.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^27.5.0", "jest-regex-util": "^27.5.0", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.5.0", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "jest-snapshot-serializer-raw": "^1.1.0", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.5.0_1644055168799_0.49424589893584203", "host": "s3://npm-registry-packages"}}, "27.5.1": {"name": "@jest/transform", "version": "27.5.1", "license": "MIT", "_id": "@jest/transform@27.5.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6c3501dcc00c4c08915f292a600ece5ecfe1f409", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-27.5.1.tgz", "fileCount": 14, "integrity": "sha512-ipON6WtYgl/1329g5AIJVbUuEh0wZVbdpGwC99Jw4LwuoBNS95MVphU6zOeD9pDkon+LLbFL7lOQRapbB8SCHw==", "signatures": [{"sig": "MEYCIQDaGCfG47hnD2rYrG/SBWetOQ2Uw/Ss9Rc1XlgkC7srDAIhAOEtNvC/ouFVUPdIY5B6IDjAkjBZrX024qZqCwHd6Wyd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAktnCRA9TVsSAnZWagAAIigP/0ChQqoYr7DHssgrNupD\nZAmF8Qb6lRrGGConaRRrLxCG8BSwKfuutx8CcYsgT2Wj9rd44MlfDbFjr2JJ\n6CYoss8wNlNxhNuRpMP6dgJiNSCydI3+rVXllPSO4tOrgB86DEfzqY9evx3+\n0I93mX28xCgakrcS/ibxYcJcXhnTf6KBEuJJ4AlSSXH1rV13pEpjhFkdWpTL\nP4WpefnfGYJc6uRa7wQHiiO7eynSWeoKU6g2ucUTF7wlME2aMrswRjnVjrHt\n7koSsxNcdrsz45spCcze/nQnVf0ajG5p3IXCMgokQs3eMuepUYPY9hiNYl18\nH5MXr6DHlkrCawQa6OV5XasxgJJxOcqWXSgWyN7B71pA6fBoGpEpJJh7o3fk\ngZT0dBjLzt4F8Owr/5zAW1SZ+ZUwfm74XwMYuLsQgGgx2nQ4FqKTG4pW5IHs\nOR2UECRQp6f+4R1p4jcU0h74YDcSTszH4329Wl7ylYTRGABX6P9+XSFeGWJt\nYpBuNNL4jfMNXrrrmmo8fl7tDZ4FJVaaYtctHEBEe1tE6+RGoN5Z1YVVI2Jc\nZu8dZo/dwVyntUwbgLd7rGXCzi11iD7eJWgG33GVNn6+9qonrmXND+OK4Y3h\n+v6JQSX5Vx4pgLW6FntkGC4IMo+xBMvzWUsy3layIVqfCxTm5ug094ys6vkR\nwskv\r\n=nGxY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^27.5.1", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^27.5.1", "graceful-fs": "^4.2.9", "jest-haste-map": "^27.5.1", "jest-regex-util": "^27.5.1", "write-file-atomic": "^3.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^27.5.1", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_27.5.1_1644317543617_0.763402027982997", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.0": {"name": "@jest/transform", "version": "28.0.0-alpha.0", "license": "MIT", "_id": "@jest/transform@28.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b2adfff11b1703cad9faea10690bceb67d244b6d", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-28.0.0-alpha.0.tgz", "fileCount": 9, "integrity": "sha512-/MTxQhtU6Nbwkc5jpNPcXloKzNpsgbMbgJshVp0MXbpTd2+IrQxD5IOv8R2TJXi120mAZMmMiFUiIl+IRPn8Hg==", "signatures": [{"sig": "MEUCIQDOIJaVO5pUefcLCadKpECki31gDWFY+AFb2AIIZ708VgIgWYsyZJObpNGfd3IzZVf3QZ1yne1935jzPfZxJ+tUPCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49627, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBVbBCRA9TVsSAnZWagAAkzwP/3cJX1G5iXCSFVwfLUZC\n+2E0/OEHxmytEhd8liw2CClgmCwGuapJWcyoDAblj8K5meaUArTYl5P62FKf\n7HXh8TUBfnf2UlGIWIN8u782ny+WtlPUz2XGqCLr4k18JcVW0fIRDRNldWTo\nTFQh7sKIZBK8NGfsVWe52jTaF+Lwj3YFAKl+/SObrrI/AjrAul0jDYcTgJoh\nTZJ2V/AgKwsBeFA1bnY03n7Gh2Hl4ecpgGT7EcAEVoEzV1Z8FLlMuVpH/7a1\n+PT1Z5olG2xdBRH90sdxC+1SiVFqcnF4FBwVkdcpAqYmzm5dfEAKcy1NFUMC\nV2TnBWB8Xlk3xYr4BGn8fILEcIwRh0YJrflpWkCnSIhSylK85ILI0R5tyaBN\n0XH+kdWa0oTbJx979IAmG3fZgUWAp9mjh8AZqzdn1wrbGOe6Hq55HZUzCRhp\nLk15aLuExJh2YpflQ+gthoCPdpz0np64JnQFVRbz/cfLEN1MwqdRrBU8V/tf\nij1XzFvRqugiLTrl1K6xRilIzyYKBhBgcM8ot9EX1XdLar6yj8MJrLtnWlOT\ni3Ci9taE1enyuNftZxTWpDaepi/1fNpUM5Mmt620v+B6vsZMUCWiMZ383yAU\nBW+2V0TDyOptKW8qP+w8Ga078hl7E1fXdEEfJPndsTBGwXxQkZgsegFH2YJb\nyf6R\r\n=bkJt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "89275b08977065d98e42ad71fcf223f4ad169f09", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^28.0.0-alpha.0", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^28.0.0-alpha.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.0.0-alpha.0", "jest-regex-util": "^28.0.0-alpha.0", "write-file-atomic": "^4.0.0", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^28.0.0-alpha.0", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^3.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_28.0.0-alpha.0_1644517057070_0.64060210912225", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.1": {"name": "@jest/transform", "version": "28.0.0-alpha.1", "license": "MIT", "_id": "@jest/transform@28.0.0-alpha.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c30400da2a5702118ba169f93325aedfe0fa5bc1", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-28.0.0-alpha.1.tgz", "fileCount": 9, "integrity": "sha512-XjVIJ/5evYDeedEYuWW3IAJH4ibTGiIrAu69CID1Xo9uhTfxSrnNPp7jlvLB3kc6MaPkOkcjkQg8AfEwwYRP9Q==", "signatures": [{"sig": "MEUCIQDxQ7fTUPHekKbQHiiOZRis8DkJwvqPl/vM6CcmQrLz+gIgTC/btv9zO9GH8VA+m6hUQYgtTsKgFhOX/3exxbfNQDU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49627, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBqsCRA9TVsSAnZWagAAm/8P/A4vivUtNueVWqrzitS8\n2/NGtnZ6tlrle+WeRfJsS/28bgYHYJNfZf6E5BSwtVWgJq9rIFtHZaOZ9L1G\n6mNpkDawh+o1/nl+CUCiMBwSzxEzlj0y2k+8srQqtsUj1uTlh4EjzTzpGNnn\nK4M1BOopH3v6VaVHFELiCn9b2anARrHT7cca2fXH1YPk0fice3C98TgwCF6f\ntI22rjXGMbhKpwI2YTitKrjeD+8Abq/jLSWuhQoPvSfDIYOrfJL4ekkGILv+\nWK8AtYI6TU78TmSUgOpKRfksIaXL26AowiXYUj5+YK4KmQM89fVc2PV+UUQ0\nyQ8JB4d+LuweCZAFHxQef2VaSnhcDSFifoKvYOdAYhkE1Fgy+mzzOlD+i05Q\nsvXQIo8fyFsHI/xN4X96jAAbkYDzI+1DeVkHq/jGzGCK+CMoQadUP676MoLQ\nvyXIdv6YPAdJzFPhOJeto+5qR2xyborKKyQJrali+9Wpr4yCnZfH4SxUZAG6\n1DL+CyA8VdmE1K9+5lpLXZqf8qBvrHoikeuLhkKCFUHK/b2IjMaOiOH1caj5\nB7X5phYkNhpyY0eee7polZTykHMNCBzIgqcFs8B5FVQ74k70MRtMqMYLdsBC\nl8ldWcoOC+UFrxUcrSl+055XT7flh6oyghGVzX3GjMjVzoUF232j7h7HJUOm\nVP0i\r\n=yYtM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d30164dde1847166fa0faec98d20abffd85e6ffd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^28.0.0-alpha.1", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^28.0.0-alpha.1", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.0.0-alpha.1", "jest-regex-util": "^28.0.0-alpha.0", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^28.0.0-alpha.1", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_28.0.0-alpha.1_1644960428022_0.30677836579252915", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.2": {"name": "@jest/transform", "version": "28.0.0-alpha.2", "license": "MIT", "_id": "@jest/transform@28.0.0-alpha.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d6dc11ffe953e8feae084c98cb1511b023ca1110", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-28.0.0-alpha.2.tgz", "fileCount": 9, "integrity": "sha512-S6txlSee++rPf6iQNAOMhpj8jhhK6igpnUwPkJPYwTGat36e4suTiEC/disJLhgKLgpeErk+bmcaPqnACKG2Gg==", "signatures": [{"sig": "MEUCIQCHSC5g6h2pl8WDuSbwDL0AOH5k0Mxzj5lvnRo8he/5EQIgRJHu0IkDcwgbTKO5jKnbKZv6oklthNtrWs+ygLaw8og=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDT55CRA9TVsSAnZWagAAhVgP/21nF1TUCuWVkD61iaJw\nFhNJV5FbFpj9y9yfCiz/+sZJIHmkp0FnUARjGWU93Qhn8SKakXm8wVaUFZEN\n7KO2XJXAzumWOhQYE10jkXa7yM6X7YBq2xX7tGoklRyfinL3bjcQDwfc5pw7\nPYezom9nlDCNdeR65VcI5V2K6/0/9QH3BOocWppyk9C1BieSWYluctkCC3oP\nXOzHvAVnC+jZuZpke6r/GhNTHENZfMGaz+UP8CPQwIBd4I52+1rXQwdMD55A\npfvLGpDpEyZWJ9Yn92M4eFx7WIxIqMR6I/ap6vasdhYCMM1urc4kwmDBX3T2\nwAJmUu7k+xYVY+TLqTa673FAubFm4Ptjkiv8i/DnURs6eRZzS4Kh61Ynwigh\n+DwlZp2JrmLiHypLA3+X4sl1xjH5EuEFKMk/yUt/vmtbD6PVRnEBF7ORHVVr\nWtXMHxh/qkQ6QV8f/A0tEts8gMe5OtkZdpSGnfJh2ngJfWtnLIIRKqtKBe4u\nuKssZdvryYfHHde5AlFbrZq3UUMcZn0z4Bz34ygvYgxKrDnJMbESldxXeNPm\nbSzyVSM8Z22VdXcvls4gs8m9ksyL7+N7NXKHGn3TkuELCcHg2SUNS1mSSq3R\n1xAm1aiE9uXelIryqTifSjw7G1QCvVuN+XutHPE/oPetqz5NmX7+sKzUnZB8\n2Lbh\r\n=yydV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "694d6bfea56f9cb49d0c7309cdbfff032da198c2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^28.0.0-alpha.2", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^28.0.0-alpha.2", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.0.0-alpha.2", "jest-regex-util": "^28.0.0-alpha.0", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^28.0.0-alpha.2", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_28.0.0-alpha.2_1645035129714_0.18541666313763794", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "@jest/transform", "version": "28.0.0-alpha.3", "license": "MIT", "_id": "@jest/transform@28.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "abab0f0a3c513c5970a4eba059ba470bf8b6f595", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-28.0.0-alpha.3.tgz", "fileCount": 9, "integrity": "sha512-BWG39P9pFLDgwWUi0zjClIZaj3CEvu+NHzomaLHAZLuMtZnGbxFMfDXbbuaIV3ImscdHudcL7JZrGqGCcLAwnw==", "signatures": [{"sig": "MEUCIFS9DJhDloanEhkrDGIB9umNZRH6ZTCUWxpo2fT18XOLAiEA03nhiGzi37ua/YmznqIGjT5oSO7qbTCkfEQCgQBEzbc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmziACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodAQ//TWRjXssnOrtOOS9XPzj48mcNirle0SYIonFdBScaZCivoYbM\r\nTCFMy9R9oHH0xra3GUAVx50JdzqwYG38P4z+yPuKPj8IXu8n37ZjMAvo1sI+\r\nDPgP/qKH0386Gwdi0XY4H9AhYxFU+GdsPMMh6Xn4HNsxwT8EjjVTE0S+bmGf\r\nHVwWW9RbGtxOd+uu0RKwCpVqn5fQgM6rVN4KVR8jJOee8MYqOa2w7wb2vgZd\r\nShac94nhW9I3GnuuY9FPA/b8OcbkM4nMRnckk5Z6TEhbzVx4t9eU6d8bLMC9\r\nF+D8WEe204YC6ij9zl2MDvcYEIkJ9wx/JvmnyUT6Mkm/Jc6DY04M75NaJIus\r\nNOu50tFLV5LATCoPQ+aMKuF2QVepZ5wxd/i6+f/zLnGpxiUkFvHAW3WSnpeJ\r\n8lQKezBa/jRF3BEvAW04lk0Vb/vRpgTf4uRGgS7YTXH4sxwO+ylmERh+BrgX\r\nv/eAzIgBe3rjBZ7FN7UkWjV7NKM6N7+CtfsK4Hms3iOyvHrSRUaDv2k6BDfp\r\n2is4Ci84egkEtByJHU8ea/Hisxz08ogqXF/gWnLdxiJD1b+28X7sl6iP8obi\r\neIxkynOdTaQtHE5GqQKBBUCY7uyUK1LbWka56c4c1kEHZ1T13EEVB0X6dCBB\r\njD1uRTprLirZJyXftMt3XNgA8wnYWNXc7Sw=\r\n=lx9u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^28.0.0-alpha.3", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^28.0.0-alpha.3", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.0.0-alpha.3", "jest-regex-util": "^28.0.0-alpha.3", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^28.0.0-alpha.3", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_28.0.0-alpha.3_1645112545861_0.19018085368966253", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.4": {"name": "@jest/transform", "version": "28.0.0-alpha.4", "license": "MIT", "_id": "@jest/transform@28.0.0-alpha.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0f091ca690f865822fbebc4dc28c370a702168c0", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-28.0.0-alpha.4.tgz", "fileCount": 9, "integrity": "sha512-Kk7MnUEwdK5rfA4QfwH3hMZgE5LmEFwMMh2EY39eMqy0a+lHXL08Cal8nC+UZbHqkDxPoyz1m7dK/bVsCjTD+g==", "signatures": [{"sig": "MEUCIGWTZ8ddMsU4rgltJ9eqS+ctuORHycpFonk30HJ93vUFAiEAlv2Y/TKAnjt36R6iZoq/dcxoNtcNodHjH+peixAaSmg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFNOGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLlhAApHFYtX7/wuBc+0sHlNJcqefuqcE+UHiHjAyup46KjePD3F8G\r\nLskWMpwrWxBQQn8hUuH10DEPO3kPzNmZrbvdqOXX5HBuPV9GEn86Vrty/evw\r\nuRIoSd7OdAWg04iYvajDu2u5RZONRz0ujhHVaRBoE17yEQ3+tLxh9lvgmznO\r\nnW0AUCswPJtALyhhpK3Id0a0UI+hjNTwk1KUdJJxcAklFLVobBXfCzg1Cjz1\r\nh5yroFzemHvhBm6JL3JfYEvCVff09VQxvifyqutMLi9M25N2IUEoCodg1t3e\r\nTRR1/dH1s8Syp8w2b7IvDUbcemJQ8/QaAXsuixD4JN1nBW0/d+CxqVqv0VLn\r\nWXJ0SkQdtwt3qCQumpMEe8e0Vg6/h6YUWnz3E6PSFe7t1TfoHlHY3uRBCPKA\r\nS8NZmDubdIOqZ0KK+1ctRPDwqbWlDwcMFWvy8L7QT93Pumk8TtNdU4ZCBPGr\r\nR2utraPbkdDn/jUiV3DBFzaiP7gEwGM7xnktKHEs1Ktc/+9i6as8hcrK0pZl\r\n40bMbpNdVVu3MVlqnMXCQ1uRL2XqFsIaUTw0Pazl61GhxY9rdxE+H5w3CiGR\r\nFGT/2Kpm4iO16C9NOhTXtJkDRYzs0nhGVXzb0YxRRIMN6pqh5cZSWciByvU8\r\nsjLWe+C8qcoyx7cFjae3qcnxtrCi34tgfrk=\r\n=yVwj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c13dab19491ba6b57c2d703e7d7c4b20189e1e17", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^28.0.0-alpha.4", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^28.0.0-alpha.4", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.0.0-alpha.4", "jest-regex-util": "^28.0.0-alpha.3", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^28.0.0-alpha.4", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_28.0.0-alpha.4_1645532038658_0.36036316756564024", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.5": {"name": "@jest/transform", "version": "28.0.0-alpha.5", "license": "MIT", "_id": "@jest/transform@28.0.0-alpha.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "146072fe5fdebbf513ceb77236004d02b7e84d63", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-28.0.0-alpha.5.tgz", "fileCount": 9, "integrity": "sha512-eL8BzIDndWYTbVP4aC6wL0R24oS7YAqDTDZT+NokomME/UWHZO35KrMxmW9S1gpnfVuaZn4PfElsq6JckCWZFA==", "signatures": [{"sig": "MEUCIAbbIWINYzEHy28CbdgYDaORY4IYwPP/FmNPpbUtis2YAiEA+WEWDVI053/47CxAyBwv5hDAXlVaK7E6e6W1ic/faGE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF/EyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoe3g/+OduYfkEF5qpvYsTt7j3KsITZWceIwYiC1pKfDZ9gJIn9AduQ\r\nNEKDFxk52k4Ybe3Xh5PLwXJVTyhtVgDOwtfXr5PedmBO7BekCch4penewWSG\r\nOCK/zpMThQV+we5l5TkbHrGRLosyaFQzZWKnZHiMJi7JxdeFPiVNFhUt85oh\r\nyPZiZFfxePJys/jLNXzo/zNuLvgYCyPVel7DyBnKy7bTWitZFUnC3vetSnM7\r\n68r3d+1d2TyEdkuUkTtNccjcMocvQc2gxN37xvRXcKYN2ktaD5eSfLDPn9cx\r\n6pi2/KCTzRo4mMmW0LCMr9ngjgmXBKDTCh6wrqrCl/oUNMu2DylISYLmqC+V\r\nylbouF+TfPQgVPUX6VOD5GUvTY/9AjwBTIxCKpjG7ivpSxKGDyL0FotD48/o\r\nUv1oADMkIf/TOg2PoerzH1l4VzkMKBHDv8cUWMTet8S8vg390RmpICbufz75\r\naGL+UlrLAhkmhZzzvuMevAunXuMI4fwpOtfEOgaVVkbkW6JsHBkXGSIHBuZf\r\nrpqzUIrFH8nUMeAdvjRgWmiXyNKl+Yxe8ALd8qIz6B0ln/njZ9AgVVjtg1d+\r\nQg1HR2d87GsyJ1anVVohZcMt3aeyvIJlWljVaeE3h10afY6PD8E0zCAna6jX\r\n6wAOaJYwEldf7Gfka4RXX9JLF8E+AWbXfEM=\r\n=K3ph\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "46fb19b2628bd87676c10730ba19592c30b05478", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^28.0.0-alpha.5", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^28.0.0-alpha.5", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.0.0-alpha.5", "jest-regex-util": "^28.0.0-alpha.3", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^28.0.0-alpha.5", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_28.0.0-alpha.5_1645736242047_0.7365011120272595", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.6": {"name": "@jest/transform", "version": "28.0.0-alpha.6", "license": "MIT", "_id": "@jest/transform@28.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "04270284e2b3f453846c0ed4e9af9bb60b5f83cd", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-28.0.0-alpha.6.tgz", "fileCount": 9, "integrity": "sha512-BUzKwrARmc05JtKIdDik8M3Y11JZPp9iVobVDeJWC5TgPhXIQNW8jQA9tPWI4ZJC/yd6P6TP0slP0xetZF6vJA==", "signatures": [{"sig": "MEQCIAUQ8MZRZqnjFCvCTe+RkdAhC0XIPiC7xIgRvXmkgSiaAiBISTBLmXULN31hyBJjCBre3m7DpLEji/szf5pTvLRqAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49466, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHdoaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpA5Q//TsGsXlUPo7SdztS4fl1PDQpeZdfTC9TxP2AQaWD97zylwVhj\r\nAcLuza9BvkLFgsI6wudjkxsP0t9hd9XEMacHTWhLBmSfkaqQ2bEiy2Zha7yc\r\nXf+p8YPi7QzJqLzbxK3OkOMfm77QyeZwKBOCBiKXm0wo4Yf6JRap7u6Qxe+O\r\nA0I91fgIUf76LNXLYnTk8Rk8pBaHAqDuUocqkhTqjX55WVWlF52YvhLy1pAH\r\neUGvY9DeMM6W0fF75xqOXPk3pP6Q60kdRyJiZtHw0ApzWM7mpMsM4EFR4/7b\r\nnWfIPnQ2MSQQbVQbuy8jFPMnOlYTpthvY9c9VW/9LwWAOTTWpN5H0Il3mwLn\r\n8PmrGz7EMFNLnFwHUUj0Fhw/KY+asMWrtt895R3SfEXiuFsY6+0Vuiurrdz8\r\nXq7qR8hdO43PZBrjGEKgJsYr4InIfgwb3IHiuJEqnRNDJZxw6KBCQP54Xaks\r\nbgU0Asqz2LNqE1sXVq2AI7CNIzN4JwVpkNNysbplIM0dWDubF2cjSk/IQehJ\r\nRv7F4EyKZoiI+dR3dNWkPLuoRytKZlLkCZvnA/DAkA8kyhyX5eeQi6yhWdhT\r\ngfyk6aaz/vCSJcSgX6MfLiAsBbA7SwE2OCaIsSNNd1o7tLWXQsLC66iEk6LH\r\nJwbsr8b9I8HZCF94CVf6Q+cUcoqFjGqnZuY=\r\n=DHkn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6284ada4adb7008f5f8673b1a7b1c789d2e508fb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^28.0.0-alpha.6", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^28.0.0-alpha.6", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.0.0-alpha.6", "jest-regex-util": "^28.0.0-alpha.6", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^28.0.0-alpha.6", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_28.0.0-alpha.6_1646123546369_0.37903726811524496", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.7": {"name": "@jest/transform", "version": "28.0.0-alpha.7", "license": "MIT", "_id": "@jest/transform@28.0.0-alpha.7", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0e0bede7062967613994c675855805603352e2fc", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-28.0.0-alpha.7.tgz", "fileCount": 9, "integrity": "sha512-tY6gFsCR03Fq9GlGG2XDQ0ZGb+e3Zjr2iiuUPSKA8tP8zM8rHJiaEzAGyUKC+/FjffNCDqtwiEhlQH9rbT4IkQ==", "signatures": [{"sig": "MEUCIQCe97+SdhIitDQVFRN5WHWmfSXQDOtouUm6WAczKUlG/wIgZn3yd63QJM+FJCpLaHLIrpxExKfEBkBVvsDmNzJmMjs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49466, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJIbDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqIBA/7BwBschIhaRHlPQIngIg9gZYjv6SGrJcVRcekai6mnzPUvyF6\r\n7aylwzYaIZcEGDBqIsAT4WdTLBOsYKbx0sKOR6PPVJaVLlnyM7rklXP4gsDZ\r\nkQWWuxg8KTA2mhDpIVt0/nx2r5JM1eCwZujXMWTpAEy+hhVZ+9yOWTuEZlro\r\nZlK7Urm9Bkg48AmqIm5jc09gSzGOV47v6qmlO1EVujKBwyzLtFjxcB1es1UC\r\n4ddiKvxsXmLiRc+syCBV7QjizJjXiDSVjJMPekJwX5HCmx10y54IP/yZwpXl\r\n6UcN9ej2i7LNXPtEYM0gSvCeOEdYMYOoVWiBia487jiDDRAKgwZvsV0hIpMY\r\nhl7HMqwj4gnuJ86+P6UbQpV5ZC3d83VRhbrDnoLugd18xRA6X1LgFqUtitWH\r\nRgq05vOgqPCC0QEOYw4HDFk0GJ366r37hrRhh6tLuky7cMFoOHu7slL0nkTq\r\nVadCYgZs8l6uVQzdWNsFRLxnwWyJ9mOQVa/r2ESe3xcTGWVmRef+luUbH7+/\r\nY54i9cr6WJ3pnXcXHQNyrnr+Owr0p4qo6WGox2DMiiDte+vUOHfrmPO5I9ZY\r\ncAjo+C9X3D68MfbtV6r3RBEm9t15/kxAsZgfYmX2cj1NbDq9C5/uRiKWjh93\r\n/0wQz7mxhKDajdm1ltjRLWLbOV042E/giBM=\r\n=mrzv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "06f58f8ca70abc9c09d554967935b58ce85c48d6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^28.0.0-alpha.7", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^28.0.0-alpha.7", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.0.0-alpha.7", "jest-regex-util": "^28.0.0-alpha.6", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^28.0.0-alpha.7", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_28.0.0-alpha.7_1646560963804_0.5243593091173568", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.8": {"name": "@jest/transform", "version": "28.0.0-alpha.8", "license": "MIT", "_id": "@jest/transform@28.0.0-alpha.8", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0b2fbb7a216b4bb980677ce8edb966eb684fbbfa", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-28.0.0-alpha.8.tgz", "fileCount": 9, "integrity": "sha512-HL71shrDLRXaYflXqZMzlVMjCVtirndzrhR6wbh/RasInjQniTYFtn3pQgyvz4cvfSW4eFCGoHyEggWpMsFq6Q==", "signatures": [{"sig": "MEYCIQC0gg6+h+RtKs4k9JSQrjmEEDu9a0sDG1KsNxhE46y4BgIhALxR0ODGkMdn5z2VtKaLPO+W8jLOfulYT58NUN7Md1vy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50254, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTFlqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoEWw/9HEfkqqXHqlOioep8zTpP1K7uNar6lzcQVJ+6JSei4Iisbs4Y\r\nwduL1t4gZmSD9kUSB5YZOOE+NRR4grxN8/EBI48BkB9tzyAWpyrnHA/iAUi7\r\ndKcq1hxoa7q2wVf/Tj0q1cu0Io/ua4LZD0DQbJgThgiGRqeGTekSN3Bc3a5u\r\nwb9AWWFfNEmDi44QxsnvY+P/wDpzmv8d9n5ruM2UZUaQwEmxx+HpV2G1ZCY/\r\nqsrWun8OUJxV1r6P64sEh/VyykR0Ubp+RvDBcTOTzzRzLZXTQ1Wl7yyF8wWX\r\nJ1Q9bVhrimJ4iD+R4NczHgXUCiDunEW4Q/VFxvuCn7HQIeNm1c7cFcweMC44\r\nijsx6YbCBxB3wlnmJK7uO/a3tQFXXdUZesyZVDxoj2jSDY5oMAI+LiwRttVL\r\nPbuK1GXWga4d2mz0xRRPFnyv/BjASaK66wAtrkP0efJpSnvDP+G5Hi8jAgTz\r\nnvMDosGz7lje727jSumkNDBRFqT0KAupgFaBgxBw6cmJx4JPywLV+i86R9p5\r\nK2qAjb6Dh575nd+WKTt4H3mxwYSX9asVHllGpnnr+25b/fDCMNyP9YjXY7dN\r\ng7p5+XSOEN+cC0O7EtnNicg2SeiTw6pkXKlTq5qgHE4s8oZvm35FSRuKXMNA\r\nYJXBVgr2iLCuJwsdKtd/QuebPNbIS2Q3bNo=\r\n=5pF2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d915e7df92b220dbe6e124585ba6459838a6c41c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^28.0.0-alpha.8", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.1.0", "@jest/types": "^28.0.0-alpha.8", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.0.0-alpha.8", "jest-regex-util": "^28.0.0-alpha.6", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^28.0.0-alpha.8", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.0", "@types/graceful-fs": "^4.1.2", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_28.0.0-alpha.8_1649170794631_0.9866981676414837", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.9": {"name": "@jest/transform", "version": "28.0.0-alpha.9", "license": "MIT", "_id": "@jest/transform@28.0.0-alpha.9", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "44b9c2e82ebe60e37a5fc5a25014efd58dc96c15", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-28.0.0-alpha.9.tgz", "fileCount": 9, "integrity": "sha512-ZDh278CqJ1iH5stEFDs3D5IQ9MQY/QVWKz1C6NjuIjU7q9g0IEKxoLxDQkxmmX8L+xxAeOT3EuIyyHINxCQh6g==", "signatures": [{"sig": "MEYCIQCn+Ua5jMdT6oU8nn24+PiCN3+XOfjcmyIXaASNe5FYeAIhAL5ZycHJqg4sr8tVqGcoaHOXwN8QcF0PiNdMRbdFzqgQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXpYFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWoQ/+PzwRytH+oxjXhm6FKzbNDxhN7a2GpSFEzoSt6Si6hZ6+JeQw\r\nvpgehtG/rIejk9/yw03oTlqjIb9oDgXaIGct9yU7DsiRFP02HXLsspy1U65n\r\nEfr6T70AC9Wo1B+tbcDxDFk+XRNInSPcoeciU6Kirvk0j6lMp4bUEr6Zdgie\r\n7ML+KcDAsRe+EBZpTNFUlz/jCaw46cNeT5bz3ePZOR8tOPc3fdEKTEiZ/fKH\r\nC3law6g/Q+OULzuJl2B2Zslnc9TP+kF2xc80jeXPKEABy6zBV241XSjs/Y6L\r\niye6Ud/9sG1Qh6VGhaPmN6iwdPhc6QSu8ThqO9pBE4bcGCD1DDwkZtzq/7iH\r\nIwtsR2SCM7UU+6qJp0BrKiQUDIDS16lwqkkGPdnrj/bwgLu3AyweiUadqXUP\r\nzStFyNL0rSljQ6dYHoKULUnlvg3d9kCdKR+6+lWiJxJI9WeIWUO2pZ02ZxhP\r\n2DglRc/XZG+kmH1WIo90fVLn+j/rEfZXtSppHAN64Z4ynyjqLy9GKXlpNcKa\r\n3OMJr+7pM+3rDASkYuby8EIwxkW6FWJ0RcFHda/UGO5CNN7qQ91hb+RhNLzN\r\nj6jsxNyu7R+5AHbvH+smY8vIenLQV2XFPKmC7IjOx7M0/oDbn2xHkLzc7AcC\r\nEyinMWdjcO8kapojRguOb3c8SyNcSlcK8V0=\r\n=B68b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7c63f5981eb20d4b89a4c04f3675e0050d8d7887", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^28.0.0-alpha.9", "micromatch": "^4.0.4", "source-map": "^0.6.1", "@babel/core": "^7.11.6", "@jest/types": "^28.0.0-alpha.9", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.0.0-alpha.9", "jest-regex-util": "^28.0.0-alpha.6", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^28.0.0-alpha.9", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_28.0.0-alpha.9_1650365957255_0.1914962234289217", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.10": {"name": "@jest/transform", "version": "28.0.0-alpha.10", "license": "MIT", "_id": "@jest/transform@28.0.0-alpha.10", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8215623a9af0ddba520b3b12c100c154d08737e8", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-28.0.0-alpha.10.tgz", "fileCount": 9, "integrity": "sha512-UqwyPlH36In10udTqwcW14rrL8272MHOOOJCUiRDSJink1AnZXiTMoSV86HOBmkRocPBJYKalYVM1d9qrPOvug==", "signatures": [{"sig": "MEUCICYuGYy9edsjv74EDvpsXSXsHwCR0y95tQ8J7qIQMSaTAiEAmKkZ9KZotUaofdRVTLVwMLl3ww5D9d1S5hhtjdPDOTE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiX7g7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrvBg//dFkums/xDztcHXz3pvHcwbTeZaGdzmkIFRn9Tnxop85CHVj7\r\nUYDCvOs/no25hRmEFV7lU38GkgPkGvfCRwjQidkN2QJduaoitsJRp3tDPixO\r\n0n2suoJWQcz8SyzuWFkyUkEaC6+wpfX1df5gSQf5KmNPagGNiSvQ/ndZ02s8\r\nCLekTb+phdKaL147zLwOpldK07Dm+gkBVjXZHpulH+XF/Brw5CqfV/CIUUBh\r\nt/mpav/HQcxM4y2FtijHYOHZVgfBi2JSq1U1nTxbRb5UrXy+slv8nP36UYVJ\r\n3WX7oq8XdlcDaoI/NOroJrsSj4QHA17RV6MbEXNCPL9g03qRPOGdgtESNham\r\ne7XigSvbX22xjZNCmtur9+y1GIsYbcAJ6nhxY1x6W38VUN5GsgR7v12J9kPH\r\n8h9cwo8NzBDmA4q7SqTUb72t4llvX68FrK9Hz8tcLO1YB1thu/eu0eGXEtP6\r\npulJXfXIrMsOBX9+pffnNHTJvi0wnsN4LTxkRltluIX1P89jfhhPMbuuNg7g\r\n8gdS+dcpzkl1pFrxlBu9yNgItszS25SFfs02BlTcmDJCID/sCIBWptm5E6JF\r\nU5BnX6VrtPGNW/FnR2Qs2IFtCyTCp2va3K/GGowRKbgvulpGrcSqaIbIlZSW\r\nVAPUEPqJn1Llqu3d1NfW9v1WyVgHLTUNRfM=\r\n=Bb7w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d5eda7f23571081cea24760261d209ff3e01a084", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^28.0.0-alpha.9", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^28.0.0-alpha.9", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.0.0-alpha.9", "jest-regex-util": "^28.0.0-alpha.6", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.4", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^28.0.0-alpha.9", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_28.0.0-alpha.10_1650440251167_0.20906864936964142", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.11": {"name": "@jest/transform", "version": "28.0.0-alpha.11", "license": "MIT", "_id": "@jest/transform@28.0.0-alpha.11", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "aeda6c731526f1c2623c13d49bba13a75c781a28", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-28.0.0-alpha.11.tgz", "fileCount": 9, "integrity": "sha512-pLLnho52Qs7Y+kok6g4REeMV3aeP3q0XAQh/LlnxIz6A7wHm/Ozu8AVwwnzmgKHstl7IKMtKz4/+l0UBKSBX+Q==", "signatures": [{"sig": "MEQCIH4Gf+Ra3pHzDay61bcJDKc6g0yDDfhUtLI2Tbg31+PSAiB/IHGXrZksYqL361fZNLPmYZEdlgZGRphAV9RRpIn3uA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50206, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYAsUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoK7Q/7Bbhq9+yfIDOhAnsq1I3SsyUcymyfUe8bvPFfyYtZ+sUvOttu\r\n0DE6iZAl2dc5ehYvbn3XeivI41+yfwPWh+TwuOyVt8VSrgXxm+SWGslCa/Mu\r\nCQ4QEzwfMlZ6qJOyyrAdMKtKhGLYCubnVs2R6puYAZxpoEfD8Y870kx+ioJf\r\nzZI30MJKbHCAqUYDc6Kw5sCfmMvFTg+dofbdkUTpObbX/wUjYo2HbogzpivR\r\nXEQUINne50X1B4nSkfziR4xFtf8iRN/yXjwRUu0UKVl6WQ5FLtRiag17thKI\r\nFCThABGdqA3Yl5usbM9mDuHIQs18gj0plR44LlqYKseiDx/YjmemuYOthsJh\r\nah6TVrcz+OIOGcFc+buiMLfIzckyDKT4B0ZKnjzpzasE1oe5YlvtqWF+o0Zo\r\nXUJ+fqYc2kz205D+KVt5TskPhRL+c0KOdylhhvIt1T/4ZH+se6cRYpdfYp4M\r\nw+32bY8SL1vxFfCkP0Mt+MpzgMm25K/aEdWUk+/d1+SdKTAQZH0RIs2ND687\r\n+dmcOf/fMNFMQQfgMrM9JOOrVSvoCF5+yMRIwnYX6NSOrY9QPA9UUpeh+6Qa\r\nKhHQ+uuwi5vgp7V/ub5WbrujXG5gmA0qCLdo16my/iI9T2vv+pGPv61cwM1P\r\nlBkTCJlOF5lD74Fr8hDMyBxGlK1OXJWlVaU=\r\n=DRYb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8b4b78759d255746f11e396efe7d06ac93dbd05b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^28.0.0-alpha.9", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^28.0.0-alpha.9", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.0.0-alpha.11", "jest-regex-util": "^28.0.0-alpha.6", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.7", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^28.0.0-alpha.9", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_28.0.0-alpha.11_1650461460546_0.24346301876522514", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "@jest/transform", "version": "28.0.0", "license": "MIT", "_id": "@jest/transform@28.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d41c76a9d8c50cfa3945e2f68fd00c0b44b8c0d1", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-28.0.0.tgz", "fileCount": 9, "integrity": "sha512-Xrc02CJFju0TLb1QZmqHCvbVEvSvR0SlSawMFEiXu/vVthWKfZgsia2UumOEJFc7YFknufShHwf+0OWBpSxNXw==", "signatures": [{"sig": "MEQCIH2htiIGrRjsGrjkiWZzs86nQ8J2VcjPIcuVPqZ2rduTAiBU0qbYf0cvuUvoZo4N+tCUZ4itXy/RHSxZUIpzfiE+sQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8tACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZFw/7BrDdKR4xfQkii1a8HwGUJ1EVtt5UYsVegZT8CbX+d3T2fw92\r\nGAkGaTNfVpajmixJBW0EQMhaoSLXEJVU0Gr6PTrOX39jgADtOZq7mLDLvZqI\r\nx8pDIaGp8e4pBYI6Hn5KFS3axzodHo+Qfez5ORiXqMqb5B5p/9n+0+QAjlLA\r\nA9XMvumeUqZkcuv/rgTN7vHCnqvFjPBjwDJKCWFy75h2lJ3Q16wg2ZrD65Wy\r\nN2AVC+TynChl756DgJLX5imQTKES10vRTr5Kg6xIr6JvwJKjIZz2brBSZ0mN\r\n02JrlY8TuDl7C0lmsrty32xjCJGPlvTw3E9Buzj+ZkV5Z5LQ+UTIPbvcK/74\r\njoKOffLsSn+79C2hTpZKxfpZPoIwFa/CAVjqW3a5kSc6+dsnK45NfcItPYQi\r\npPDFfEM8/DpD/JqEFYb4DXDPu2PXrQ5L5ShQET4m2+0XLjgHYDcwFmgwO7YU\r\nRJ5VPYksbWTtALn+KYLpjVu/u4a3Ho+ndSrE5EMYyadJG/TQ9K9lvevGeKLB\r\nUm5h4DsF9jFWlzDmjnpSoQgpaLIK0K5A54ZhKoF42BO36gqhbIifew2B6b7K\r\njmpunEE7H/I+gwSgowHuz2uxidoHKa+JSzdx0bPM4wKHhnH9Vfgq+AriWSml\r\nu8LOQ0Vs/btVoPj816OyObGYPRjmvlo5FwI=\r\n=aSBO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^28.0.0", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^28.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.0.0", "jest-regex-util": "^28.0.0", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.7", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^28.0.0", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_28.0.0_1650888493402_0.04941094719675054", "host": "s3://npm-registry-packages"}}, "28.0.1": {"name": "@jest/transform", "version": "28.0.1", "license": "MIT", "_id": "@jest/transform@28.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "44fced5a366af7cc5f69745c92014bf45c408d90", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-28.0.1.tgz", "fileCount": 9, "integrity": "sha512-45fxjycts6CTPMeusSICYhMkMgFAs1opvgEBYcMmukucJw/AgVEMsGFqheWyDzlU6GJ+h9cpft/zkTGPJtzRGQ==", "signatures": [{"sig": "MEUCIQDf2pNVR8EUH7CKcj3xq5wMfcxKeZ+cJ3DTSYjFwJysuQIgXXtnwDOqsMwO8UPfA4U6vWkdhsmEiswQTO7JIy3z8+k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZ8NCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpWIw//cO2EuFmUEWegowA7DHGQTOJGZ34C8lwIIDXGHcbPV/4LrT4V\r\nwGJPksYTAtmT00/taW5hdE360aWA39OiXD6chmD45mweb0nMk9FTMGaaTjM+\r\nvWc+19Ijhj18uZX6c0QJMnWbKY43FuDzi9XaDpQCmpJxzgMok3I0vqTNTyJ4\r\nVTSlaYy1ctiuEWX+rMC0gbNyf6RLMcirmpTWcftXtOi2PpILH/JfeDQ8cn8J\r\nBSkm39VpoE7YT7IZkG0MFWUsxP1MMxbN3FQYp5FrnJ0Hvaf2gRV8ydSlyl+R\r\n8Hf3L3mixMSSTIYC2ZhzFIhZchbBlkpV+hib1r70PS4tJDU+smc+KBSd+uEG\r\ngtQLNmAiKt7QlPMiFkWjUIjjhbHISa8dQCfM93OvIhzTsJ3LZaaBUb6WmmXK\r\ntwp5oFYE346CiLYUVVjmPWaZXMru74Svw16Hbkxg3RznNp2uH/1YHA5tedmR\r\nrf0t/qtszN5GIq9Vkbya3nkPGjz2kXeT6X6uX3IVYrZb8Jh269FZf2k3sh2n\r\nU2RfyYyjE/IgySvgUpTHZP+y9RT22ZXnFIQHnZ7PJ1+uDSy5ZJGNLVGvKkOm\r\nDt/LECprE2unF+qERY9Ppj/0rqKtkyI8pOC/PH9DIhO705xT5mAYVg42Lfia\r\nabQ4kDTbo01huAP+xyfvZT5ct2qK1IWsL54=\r\n=2Vh5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0a08639e4299f07becf1020a761adfec83536018", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^28.0.1", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^28.0.1", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.0.1", "jest-regex-util": "^28.0.0", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.7", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^28.0.1", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_28.0.1_1650967361916_0.9619813888387778", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "@jest/transform", "version": "28.0.2", "license": "MIT", "_id": "@jest/transform@28.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2b33d93fdf5827309cbd332bf968fa0dd049e7b2", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-28.0.2.tgz", "fileCount": 9, "integrity": "sha512-PgvB/DEwVY+vJAGTRSFhdmorytx54aXKK1+VQIxVtdFVAe0mJ2fUIRWQuGimveEJWT4ELJImZAIYOgs8z2L0eg==", "signatures": [{"sig": "MEQCICaYwvnoWFcoCR6KkF8NggTFYKW902Ys8wFVZhY5UonVAiAXQMiAR15ME71tzJ9s7QcQQFzgr7WBxw5IPkFvuIdauQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPRFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqj9g/7BXR6f6EL6DkJAiRDskbq3asxlskLTQmaUdNf2ylnWBcP/IKh\r\ntCIjXE2JcvjEYtT20zPX3tAD28nDCFR+YTn9MyrqLvRG/L3K9MGLPjmbOrWb\r\nMXkog0jBnWryvHUEBbKIEG8S7rLwSxfsLXy+U+lvdPnJC9iFJfVqqEZdWKzJ\r\nyG3I+MAgx5XHgxOh74KOiw1xTSgKKNqhvo3YPM6396hSB690CdNnJULrCTBH\r\nWIpuVB5/MHlc9YnvU7Hs9ewmZH+8da8h4o8ilGewxyHj4R8RaeTz/L8RmyID\r\nd/+1C7Tes1aUEGIa/fKz3VqHAwjY63rDQiL/PNfs6RF1SXomiSwzOQMLst7e\r\nfcaslSN0qMGa8gSj4H2oau+qc9pffumJGdzbrTafnlHY4aK4JbXB1JlPNwWl\r\nSuoFZMgyg6mo0jM711T6S0PtN9Wwcrl9jYmjudoDDff+McKDXb+kSZesu3/j\r\n4lIF1VYlTRkhiJIFgMYDNcuwhqm82LSZS+Gt0RFtgeNUy8QttohOCN0Z50I9\r\n8iyHKrf8QUM9+A4b3VRHK4HybVkyyk6fx/XLtQKdx6FiArc/IP/2mbQTPOGV\r\nQZBexHFGvY8mTrMINvtxc6CWTtMkSFGB51m700deh4sINcqPUOASTU1qWBz8\r\n3yCxLN557ssvWHlKY2kx5ykKpcftzJkT1Z0=\r\n=6qLB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^28.0.2", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^28.0.2", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.0.2", "jest-regex-util": "^28.0.2", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.7", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^28.0.2", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_28.0.2_1651045445342_0.2924812671637973", "host": "s3://npm-registry-packages"}}, "28.0.3": {"name": "@jest/transform", "version": "28.0.3", "license": "MIT", "_id": "@jest/transform@28.0.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "591fb5ebc1d84db5c5f21e1225c7406c35f5eb1e", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-28.0.3.tgz", "fileCount": 9, "integrity": "sha512-+Y0ikI7SwoW/YbK8t9oKwC70h4X2Gd0OVuz5tctRvSV/EDQU00AAkoqevXgPSSFimUmp/sp7Yl8s/1bExDqOIg==", "signatures": [{"sig": "MEYCIQCSQzAsWhKXjPhsSx2shm5hL2DHYtNWO48CRfTKYNfpaAIhALVax1wOixERlhYSELxcm2+zVGkheu64k3ir45YwtDSv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50851, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8GIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr2bhAAlbl2bSjz6tmchnJYK75ldnjBKXfY+Swj54pdENtre/aliT+h\r\noeEuEd3TNkyBgl1g/kJ6n3kaC+xm293NuJWDbt4zk2Qff+hPZWmJ96wNUCV2\r\nCRHjaOOzxpzL/G+lRh0gqDYmahOX2JaSObSIioca5VcLj7fhpEjcJXnUxCCG\r\nsdPjlU96s97HYSMa1RpQp08cLceoTWX0pK76vGawpTqzccjDJ/COqK2+FEMs\r\n9fGwCUjncmoAumex6Q5H5ISHBtUrL7U+04ynW3/7yq5myHpbFPhkM7V+IZzw\r\nbdsKal5U+QodOc9prRkPua+/K/w+rebIe7T5Nni426TlbULjNPdwIWpgjeR8\r\nEThO8MnYtwybHvrFOzWyficzKZ2C63CQArUgbuwA07DEUPCapNUeicGeASki\r\nYgVXtqypV0Ng8+GdX9ivWyJVybfUXHjEtKBUqs6xp9tdvMoi5eOcZkP+F2Ji\r\nvF0V4OPQ7k3ozRr0og7COeZZ6J6MVYRNbYHlqEogfn1K99Ik5qu8L4LxR6hh\r\n3ayiHw5aKkVzNY8jetMVRHIGejDKqhcQIUTQYKKDfo/0L+oy5cItJHWCBX7U\r\nLjnVjRAlehP+uv+iEn/l2HkGR0y3eSbvGw0Fvv+ajoxMEdkHjnl3/4jHssJp\r\nKr7Eqt4zG82sp3b3UwDkS/kOKGre0/urkp4=\r\n=N/Bo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "1bea778422dc049b0fb3d7da809ffec39be549ff", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^28.0.2", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^28.0.2", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.0.2", "jest-regex-util": "^28.0.2", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.7", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^28.0.2", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_28.0.3_1651229064676_0.8285805482869439", "host": "s3://npm-registry-packages"}}, "28.1.0": {"name": "@jest/transform", "version": "28.1.0", "license": "MIT", "_id": "@jest/transform@28.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "224a3c9ba4cc98e2ff996c0a89a2d59db15c74ce", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-28.1.0.tgz", "fileCount": 9, "integrity": "sha512-omy2xe5WxlAfqmsTjTPxw+iXRTRnf+NtX0ToG+4S0tABeb4KsKmPUHq5UBuwunHg3tJRwgEQhEp0M/8oiatLEA==", "signatures": [{"sig": "MEYCIQChdwF0M1aiQ9MheJZZemKJnvaHqGHmc7kSqi++6LO3UQIhAOIg1U5V2LfEy29zl9Ho7P4eRSNtdTChYa76/6ssJq/R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidP0ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoo2xAAgI+fLiHJFXKJN4ekzgrjIpNvaPr645sf8pLfdQ0pIxseXw8l\r\n8jblZ5ScZ+W3ndMNZUR8bV7CioxEtVJnaG8bCdrlCiJmScA5XT0jZ4iuVeTb\r\nRwdIQQ4T8CVsEz4rDiyaFOj8+KiCXyoTnT8i9rn5c1CnNkPMn5Pi/5FKYk0i\r\nzYnyleNQ7agSsIsjCVf0RtStiC3xDNg/C3oI7RHi7SK0Q1R4XFqginCcHwz7\r\nudHuFx7dnJ13G4c+AqetWjSBTV4LIzpiHy6NrRRQcqvh61sb963yFD98CKZs\r\nEcrtS9ssuB/h0pG620T+Bk3jBVj/xVsjSyh9HlzZS7ckOzEhcYztdScktvM7\r\nBVurjq9cTBm3B91PDSZRzuQsSeEPkpxR59lRiecDzLm+v4Yk2gd+AzyslhJo\r\nN+jkBoLubUbgNclier1n53/EFYrljddwJK3H+viz3jHjKDWUGM++GB4punlh\r\n898ERGJi0aGHwTH0/nr9anxONmJ5/JYRjGWD4srW4gYN75mrpnkP4HBorACs\r\nxe2NPw+1lulb1NQHfKhDhf6Ur7Nn10Qc+oHp6m3AjlwTYZ2eD6JAmFA4NS+C\r\nCaXOPs1bPkoe1pgp4M6iR/lQwMgxbYXCOmVCivrX0qv2+1bkgOLq6VxYCr1a\r\nD38ICJeKVLJU3Mx5gJxrF/09UgAp4LJfE9g=\r\n=8BPm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f5db241312f46528389e55c38221e6b6968622cf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^28.1.0", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^28.1.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.1.0", "jest-regex-util": "^28.0.2", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.7", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^28.1.0", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_28.1.0_1651834137009_0.5807959795810222", "host": "s3://npm-registry-packages"}}, "28.1.1": {"name": "@jest/transform", "version": "28.1.1", "license": "MIT", "_id": "@jest/transform@28.1.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "83541f2a3f612077c8501f49cc4e205d4e4a6b27", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-28.1.1.tgz", "fileCount": 9, "integrity": "sha512-PkfaTUuvjUarl1EDr5ZQcCA++oXkFCP9QFUkG0yVKVmNObjhrqDy0kbMpMebfHWm3CCDHjYNem9eUSH8suVNHQ==", "signatures": [{"sig": "MEUCIAUOTrNK55G8yNnqUAu2q7Bvqih/OoAZ62lQHIgUZIMXAiEA1JiOZYFcafGwRJ0X6/MTPrPJ4Yl0OLlPy9bEryioNos=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinuujACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjkQ//ZMIjL1wqEYU9ES5VPump2hzngWv/SWuEa95rwFTngOsJnzw6\r\nv9vA8wY2jcvw6H5feHLOPPG9ckvANdHXCoxgSA0Pk4OvuIPFrsRGJdA92866\r\nfWFOBT3iY2x0tTz8SzSM+isNT02IMwQcHBt+YkxGGdnTyIf/ZaVXOowlzzj2\r\nfh8mROim82kYjEhbTnMn9IDWbxmA7oUALRbeUXHueXLK8r56DUa8XCGOr8it\r\nhkFa7uRsXKEHNNpkYgsne1dqZm2TYuD27lZJ5UlcXuKE8/I5Ix3lNXxxARLX\r\ndXWtE3BJbe0IVUe3myAplSW3fPSty5MyWzk3wmYedlAnzXYJ96LIqM0sEbLe\r\nQOWmJNmLbssrS0DAmyzV1929Zecy9ZrcnkMnSKhcbhnHgqIN32lQbHjkaVsr\r\n7Cr12NIJ5cD4Tm0LEr5ScPg5pbFjWVMqzbYOd7erMBOEB5sRFCR4rYaTlAmE\r\nlURdoLlFn5czYJbmoyp4NcyaNLdIgCUhuLbuCtOJYsJCFZkqVxBdgdV7a9qv\r\nI9knpgfXA+tG4UpDBKo3wQA9tz2Z6/Syl4LJMJ4EeC6lN0sPlG6cR49YFuvl\r\nh4bNAcdnt3ymlYmLVHrpOoE2PiXsdYQfYfqPZd1IvrnnxHDJ2QZiCQtAFpbO\r\nQ/Qs9j2/NiXgHy7mpEmv7jvLvK67pP4DjN4=\r\n=xxzh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "eb954f8874960920ac50a8f976bb333fbb06ada9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^28.1.1", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^28.1.1", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.1.1", "jest-regex-util": "^28.0.2", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.7", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^28.1.1", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_28.1.1_1654582179049_0.5442215922244371", "host": "s3://npm-registry-packages"}}, "28.1.2": {"name": "@jest/transform", "version": "28.1.2", "license": "MIT", "_id": "@jest/transform@28.1.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b367962c53fd53821269bde050ce373e111327c1", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-28.1.2.tgz", "fileCount": 9, "integrity": "sha512-3o+lKF6iweLeJFHBlMJysdaPbpoMmtbHEFsjzSv37HIq/wWt5ijTeO2Yf7MO5yyczCopD507cNwNLeX8Y/CuIg==", "signatures": [{"sig": "MEUCICpYmjNObEkDZR/t0Pf6h/GINFn/vZgMFCQYLnfKStd/AiEA+ECNe4Snxug8fFjZXOEOJgcB+wTidRKejrr6V+V43zM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivCqVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrc/g/8CF1ifHGMEmPD8huquc0t2BkBOgmJpbwWkCJDdOuJoPjF51Ms\r\ng9KRpp6/fq/fxxCwbQ4l4qZ/qBZigHc7eCJji6y7L5BBvJypmr820prKjycR\r\nxteKVRZ8kK1dAMQnTkcRcEiAgi3RlCqxIe6mEZyVUH1nOIP/FKc/SnTcuzKT\r\nWQvErmdyxZpnbDHnhGkx8LyMwFh2T2soJQRW8teJl3dBNG497lke2nOQ7HNr\r\nl7OjYQETderuDBLG1HLtPTG4XP8Itj5QS/2X3FqSVjObQkQjcKrucptMKCvk\r\nr5nOImz1mlnvwK+Qgs558YxTQJtwIw5zlKkT3V+CA0pTHF9gKy6BgHHX/mTJ\r\nWMyrY5MqlATciFTWlZ1jmF4Jbs5hsMsxGstCue6c3hsNKaBeS56LyE3et2VE\r\nJhrwm2UcnmN6pt2YaHUYEJLvSTin28j5uYV7genZOdT/wT01pjG0KIyfePqt\r\nnb3IUrb6B50eXGBPR5qog+66O1RbsUmZW6fbd9jDsDJLYw8MJvbeQwb89JeU\r\n8g2MyA3Wu58zPyqkmbJfY7EglnVNPN/esR5fom20ueIzd5EVTvGrOmo0PAm7\r\nXPWsze8Nlin7t1E2c3yrdW5o3l4XENfqQXsXUzo7yRheMkIT3bnUmegniOCO\r\nmvitNpQEflQ3pfPTbjm5jiwgSTpG2OV9FaQ=\r\n=gwfX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "789965efec4253fc54ceb3539711b3a3a6604d94", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^28.1.1", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^28.1.1", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.1.1", "jest-regex-util": "^28.0.2", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.13", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^28.1.1", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_28.1.2_1656498836894_0.7913299348163312", "host": "s3://npm-registry-packages"}}, "28.1.3": {"name": "@jest/transform", "version": "28.1.3", "license": "MIT", "_id": "@jest/transform@28.1.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "59d8098e50ab07950e0f2fc0fc7ec462371281b0", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-28.1.3.tgz", "fileCount": 9, "integrity": "sha512-u5dT5di+oFI6hfcLOHGTAfmUxFRrjK+vnaP0kkVow9Md/M7V/MxqQMOz/VV25UZO8pzeA9PjfTpOu6BDuwSPQA==", "signatures": [{"sig": "MEYCIQCuCDX+VAlpdLmkp9+tnCEfwqc6S4JRWycGG4sMkrODiQIhAMx3YdV/VrrCgcFcr4rT12CImG4LEy8ZK5UfJ03/qE0Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiztLPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqG1g//UpHxfbM+RpyoltWg5BCZaVpGDnnx8rHVO6JRRYB7FCigIx4Z\r\nbLF5qyNI+t/aBMjO/TUS2+ySnrRjF4j2alNH4YkelI7klpXNSBVCTdA7Hd5l\r\nWUgPn8AZ2O0mhQ/nYx1IqAyFQKEc6Qpbkepd1jM3s8KdmoszP9pOD6Yx4mOM\r\nHTz/W49lg3kn2QRuooxUIgArrsnHljsGjoaJ3mBRqF+flTXR2NftDCviDdPb\r\nNiKpc4WwRDfPrRBeTG009z+pWXGN6I30SmXmfd584o9N8VnC6YBQACqTrzcb\r\nwowrnEr4kvVYPRKLDJMnsosOyNoycTdCKkq7X5UFzSlMlFGZU2TXuzeOnUqn\r\nCqgWPnAM2ArquEoCZQGHaDWo0d86cGHv2RrCtSAA1/Jbx06O5YE/ULxM08k6\r\nCShbdVFMBDbVoRG4VLooKaqjiIsTxsv1GRlc5dZlsnornLSn9btHhRYwZcm2\r\nGmeoCgDVvhdse9D1tQB5pF3C58/5b6Z5l/Jug2eoKbz69xg9+eodtrHjuVwI\r\nKboXwtajmSl+fFurxUMhxnjX2UwP7BxD6qRGevGwf2fHdM52NYxpyvG4UIEH\r\nA2lwjLt8AEiAuHHiVi0z48jLgrfZEYUSOFXaS4nCCio0oeOsP/qMoEiG+qVU\r\ng20oMG4hoaKdkd1A6kkljOxYhxfwX4PWBQw=\r\n=nZdp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "2cce069800dab3fc8ca7c469b32d2e2b2f7e2bb1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^28.1.3", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^28.1.3", "graceful-fs": "^4.2.9", "jest-haste-map": "^28.1.3", "jest-regex-util": "^28.0.2", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.13", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^28.1.3", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_28.1.3_1657721551275_0.37846027907400615", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "@jest/transform", "version": "29.0.0-alpha.0", "license": "MIT", "_id": "@jest/transform@29.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "96a127b88e6f44f74554d6909c8cce481f9b6065", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.0.0-alpha.0.tgz", "fileCount": 9, "integrity": "sha512-4+Npr2RZ/tE954NZK1cojGM8wIb9K9/cQxvGK0nMpZBWeHjuuRidBTZ7jNiJFhvWBZcZ72iJHzfXVs5uI02KDw==", "signatures": [{"sig": "MEQCIGIn/y6jYuws/Zf23g5hFCycAVReYM+o1MUm1TRS4jv3AiBkYlXOPXY4USUjAzbiAC5W0U9b0LDk3wrvnKn3M8mLbw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50732, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrbmQ//SRC3YZ35cPwJwVXDOzJ80yWgNRbe9xgx2QAFVlF53fdTZbM+\r\nFU3yq1t2cJ8cPd0iaewATAtDL0j5vQGKfstnUtIlUKY04R9il63ccHZmWyZU\r\nxbz8tYvAIEq8lFDerBT3Id3X+JdNnFyuWc1XbuywNeFTIhHx0jWr1k/T5brN\r\nb3Lss6VWMrP8DsCEQpWbX7ooLYtq2l4qBF8gyNVFhonTitMwpGwRIjgLJLCS\r\n7DZeQ3Sno06knv7JEOrXag5OT9+KKePk1XptQlsTDLpPlyseVCR2rt/wl4Xw\r\neGB94s39AknBBJdssIWiRtgRET+Q+SXW9npqNlvOMalsy1LfWEaOqTV/8j3/\r\nn0y5uIg8C8mPu1JZYMDjKS2GhULLjphLcLTti/QqS3Sh2nWIrVLprACwKNnk\r\nE8WstxaqwUPhc/Yab264klej8tTxIrx9zqSqb3+NH6yfIAkKIKAxjezJ/3j8\r\n/j1fnEXr6Jk2ob9zG6xEq7v6pLsT/L4E1cbEY4S3rluOLuHJNTyfqUoDYzn5\r\nceTkR5v8SVzdolq+3Et75PeRCo5i5sQXWp0kbPvlwzIdn+ACnsyaM9gX02KU\r\n1+UObuH0T9vE7dpF7J8C2qGsAJT7CDfY8nRzh19E52TdCrd0PpxkxBjhJCpX\r\nA+ym4fGvpB5BVGjXh/bw6OLxZoDdblrYVZI=\r\n=3Otr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.0.0-alpha.0", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.0.0-alpha.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.0.0-alpha.0", "jest-regex-util": "^29.0.0-alpha.0", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.13", "fast-json-stable-stringify": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.0.0-alpha.0", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1", "@types/fast-json-stable-stringify": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.0.0-alpha.0_1658095630638_0.04030928288074098", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.1": {"name": "@jest/transform", "version": "29.0.0-alpha.1", "license": "MIT", "_id": "@jest/transform@29.0.0-alpha.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "11ab69d26c1adcc88a48084398390edee94be5be", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.0.0-alpha.1.tgz", "fileCount": 9, "integrity": "sha512-DQ4xjjMBXDoJFlTWrcrk/AmywupaNYrycePJGgWf7+COB7qefj+v27EUlfe+cvE8QAeV4eq8Vhu9Q8y3H25ttg==", "signatures": [{"sig": "MEQCIE0xWRA0ckXTStsdNo2608cE8JrHld7o7h4zoKjBtsK/AiA4p+vlI3X86wxeD3qlkP6+gxazDM7IMz/A0rKrDit+tA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50681, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi64IDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqocQ/+P9JkTUoHa0/4GetI/Rjt5fs1DD/HM4eCjgivSmybAAH7fAh+\r\nKEvJGOxQkYfGl77bwf/AHmv1NRCG/xcoCXxjG5telNgu4oryF/X00lVKo3p0\r\nBypm8Vg5OTZI3r8PsYPM0I5mp0bTKv4/cesqhBsvCBm05J9FNlz4V2C6Tqyw\r\n3qedJg/nI2Ezfq9J3hcXygFj3OJtzakVuRwFyzqdSXgjEArVyvzMRI0qHrZH\r\nHMJGIrm60qU2ySjeajBTMmlPsapBzHDqyxPeZtincnkEqHYauccX2//tHM7I\r\nylCUpDQapiHwHqxBQrOj4y8Jq//4QIph458D86eoFQFfdCazcepbTsnQHO01\r\nGYOarzvWnJURlUfKQ6JZmajprTsrBHBzlHqaE29EGGL7D1B5NrSbxJgligN2\r\nrvLLmk9wgHq0SXBJ+hIeEDEtvDqF37bU+OYdS7557ivtU4O1wEpd+NT+RRGD\r\n48WtbY1g/dElNwnBhse1pYmRipM1ojSDmoOJ3fusWzmba0wwTHH8t3AMcRVW\r\nkfxlpKdd7cb+5zGPx20CipwJWf89aJ6AKaMNy+lgtO49BSICMKboVfD0D1uJ\r\n/tlWgS7JJLIt8PE80GCVZu4vsZlJVmY8lLL1PB2EsxXwzWfBDLBrIdKSmdIl\r\noWY8lS4JO4P3dg6lHiemKCnf9Jj6CoEBvX4=\r\n=6wK/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "10f1e7f52d9f876e6fb7f20c1903fdcddd8db8b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.0.0-alpha.0", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.0.0-alpha.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.0.0-alpha.1", "jest-regex-util": "^29.0.0-alpha.0", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.14", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.0.0-alpha.1", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.0.0-alpha.1_1659601411649_0.5174505654047552", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "@jest/transform", "version": "29.0.0-alpha.3", "license": "MIT", "_id": "@jest/transform@29.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d7fdab300fe04a7ac18c7c93c8bc54599dc368d8", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.0.0-alpha.3.tgz", "fileCount": 9, "integrity": "sha512-AJysiPkFCjaEEtAxCRpBxtre6OoazwRbUmNwsQvqnOcpHDqvVt+XGlQaiC2MJuGiZIFq1XgDAq/xVQaokKWDEw==", "signatures": [{"sig": "MEQCIBz9prSL2QaqK542ZrkSE/llVlDGRPtdLxorun3sRciSAiA1QYDO4KYRaFBET85e/IJcmrnuPL/cJ+HqCB1oL+3B1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50681, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78EWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq56RAAiAXO2Gg1tguii66Qy17T+4HbiOS/54WVxUZsELCnuWhmPVpi\r\nmNBJk2y8gcvZ3zjdagnBi+i0aR0oqNmaasBMXPqSAcAFEnc72KsfFIWmOv9p\r\nbDvsRjszcmLflG6e9s4P4tTJjjPWeh3RX1koQ/I+stUzHkuVYJNh9vV2ANC5\r\nxyr16mNPOCDqf3JqzRfen7tG4zxKjAorZtJ+gPYT65qjwGrTfNhUBr0sGUdp\r\nB3wpuQmc5ZbUzv7QPZM9qoxQoaZ7TfZoeLui0zk3myv/SftAcxZbIJP88FwK\r\nrsuVLoqycSuQVcTkFoJXqkhqSsFFBSWmCrX04wW1HcaQ5dzAeBra67gb97BS\r\nSWYjr6Ha0wLPjQzoKadA8w30pTFRq4c5myCGQ9/rKZWFERVTXoDstTIbECiu\r\nbTpflbcw0LM2Ux+NMZz6op80Fx3tr4ZnhfN7G4Sn5cM+cypG4MUjv+3DnGXv\r\nX8UpI6D0ivBHGzSRHWR+KrDvO3F+KZGjQ/ush1AGyCFYbS32Gf/gbainlisu\r\nraakqaGV+NnrcxOiKmD3om67sklhopmJRAlCk+NTnPMxjIbHk08Lt5fPJtWO\r\n+X58r8LTwQFhG4rfmSm2FrMK4kBeF6fkdA1CiStuhzWrSVuVNupB7+49+ih9\r\nnQu5pVrxY6QLefLaPuVV3usNMqLclaVT/Bo=\r\n=SZ7Z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.0.0-alpha.3", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.0.0-alpha.3", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.0.0-alpha.3", "jest-regex-util": "^29.0.0-alpha.3", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.14", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.0.0-alpha.3", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.0.0-alpha.3_1659879701894_0.21232236582418684", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.4": {"name": "@jest/transform", "version": "29.0.0-alpha.4", "license": "MIT", "_id": "@jest/transform@29.0.0-alpha.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "64e3c338877bf953014707168db7cb1d591574d0", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.0.0-alpha.4.tgz", "fileCount": 9, "integrity": "sha512-0SuVirO5844n59fe8d9P9bZj6tLgXiDRCMKkIesvSEseCLzxu4+slwlXtv8JRH89klIrCHmuIcTKi2nGEMBXEw==", "signatures": [{"sig": "MEYCIQDXOcPXkGuf8Ob2nVQh6e0b1mX9vhWdJiPSsNUpHC7pTQIhANU5Gazp8EVGxB2LXtZ0LATX+B5tn7YIJZwELrb0IwhX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50681, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8QokACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8Iw//cew+evYmKTLlhMi8W8HazKtEkUoyzVYQ9StIyJmkxcEJ/ptJ\r\nNNbjdBQyk91PcIzLow6taHo7jYiYk1dhvPFvYz7AnmI210zIvwuAhS0D3yl7\r\nqngFxTZRP+gK8PtI9Uo592HhJSIybyd/BfkSQdgXPB+GXpOwxNzvj/BTIgkI\r\nQojRYyjSF6wy4j54mrU960HWwW2HuTEFmFVCvA30Xel0QaDseWmGsgXEDYJ9\r\nZWqSDs2UQVMeZdCLIukBt7mKbSLJW9pU6giEFrl9AtC0zIlTuwne5VaRu2/N\r\niYtjd3wHprfkFJmoY5BXSjUSLQOR+53sl1CzZYcN2wD4i7YlrFj/vdZaF3wC\r\nYykbnj9oSDwUQGgn4ntXLWXqTDs7Y0jkw9GxD8RlzGJP+nem+bhOmriMBgna\r\nSqdeOZI9LkNnNd7XSzzWM1xdqn1n82M/qN/XmZDr0MuM2/PYvt7oxN/JUdr0\r\nhLkllFEtp6PCRHjWxDa+wSr2rKPqIzvvUBGigtGPmQJpmQxwA0rWPDibKP0e\r\niDIvjDsVrYYd4V5EejdZFcAyMS9N5TyNcarQu85qtupMJ9zvMS+vIPLs8kQ1\r\nTbmufHW6RC6Tt1hB50PIzp34YmjX/3y4eHzx7Cl2LS692jipgGw8RPYFWaMi\r\nTvB+2QxU2ZZoAMTrGZg/CFX83zy0Qjfr+50=\r\n=w+Cl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "98a833bd4bc0bdcfcee5d4f04c2833400c4e2933", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.0.0-alpha.4", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.0.0-alpha.4", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.0.0-alpha.4", "jest-regex-util": "^29.0.0-alpha.3", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.14", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.0.0-alpha.4", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.0.0-alpha.4_1659963940757_0.7912747897026522", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.5": {"name": "@jest/transform", "version": "29.0.0-alpha.5", "license": "MIT", "_id": "@jest/transform@29.0.0-alpha.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "61274696d596e71533ee104cb8ddca05c05a11b2", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.0.0-alpha.5.tgz", "fileCount": 9, "integrity": "sha512-NEi/qLWfjjKrXWMFXRpWk1/UdQDQg2b0ePwIakBrsVozru/kzUSXfDYaPCU6QeH6Punadtp7d9NytngIb77feQ==", "signatures": [{"sig": "MEQCIH3oMvrQ3pHPVPveLhj5GCKdbuL/KHh1Pu+Zm5XVXQpUAiBYCZBcZSJJiZxrm5fnoc3qGFL51mkw7sO/rWq1ueaXeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50681, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9QbsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1shAAk/0Ap/cJfqahC/37wEP0kHeHy5EtTt84cBopj4zunCzkNpAV\r\n+W1HGXCsz92znOm7Wjiodtxwh4iPuLM40SZCHvqhyxCoswOJxnQGnMQcC9+6\r\nf5An67J1Rl666RTwYB9wbHrw6BEgWu3gHkHK7iQgTEoStEl3H6epcOj8+a2z\r\njAG/D19P3xRKpSV4n7xgymcVUF6CbgL19LKZwRH8bRRj9SKOlbcpoRC1dXyK\r\ne5TCbynKe4htss08SKUgqkIWEGdqkWCz/TeFRx+8AKobT26yGYFdgVmx2XvB\r\n77cmjcGaEWBwE36Ow2Q8M4haHe1Cx+0yb4WT++3GxsGe5JpZDstWJpACzUVD\r\nuD1FyAC4Wk8SrrVKqyqDsGw7l0eBVLO5uarBQFs8LMknVkVIDEyhcWi8IJ7S\r\nPw8KLGYnnf40/EauSquvXItmsMNaz8yny4om60jB7mOT3AgS3Np5DQ6wlP0+\r\nw01j1mTgumR7M8N85mGHso3wcPjOmIXTw+oJ5aQYbCd5nSaBQM43qkvSq0Se\r\nFz0LmCep90q5Am+YkgWD3CHqGwu4q6xYi11EJFRu1C77nlmTimC9oNDH30OH\r\nUa1qNkHTIHDpXVFfCp0tiEZ+Ih/5dW2acko16dB+rhXqgYWTZctC36B7ieqd\r\nGOXmEOts2iDBvMGO5LWk9hYcJTuqVNUSoTY=\r\n=GqAU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "63e506b5d1558a9132a8fa65151407b0a40be3a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.0.0-alpha.4", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.0.0-alpha.4", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.0.0-alpha.5", "jest-regex-util": "^29.0.0-alpha.3", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.14", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.0.0-alpha.4", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.0.0-alpha.5_1660225260072_0.04786894169442313", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.6": {"name": "@jest/transform", "version": "29.0.0-alpha.6", "license": "MIT", "_id": "@jest/transform@29.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "611f4f5cd7a017055346932d53f82291e4093e0b", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.0.0-alpha.6.tgz", "fileCount": 14, "integrity": "sha512-MbjxkN7AuBrOXYpGUraL0eZJUMgHATqi/1HDXJQxVRzQHrxXMMf1QBJRYBSfn5+X6v53xLiGAlSrOzsJhWUUbg==", "signatures": [{"sig": "MEUCIQDsvhlS3n3v0XxIIDT6FTVCKJlTomG+XcYV2Zh1yB1VVAIgHJfcPRAhwQg+8Fhq8Oex/qDxN2P80lTnB5IuAFhg4KI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59513, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/5blACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfJg//aEgoD1+Kvg17BmvS3FjeLZ94NgHON2X/6E8p4QjsTwFBszMG\r\nwZn4cU5ULSxUJFYW3uLk3T9GpbM1phidX7gAuejKJrVPUORS7otX5GKGZmSH\r\n4Mabsf5sLumuxyJUgTIcJK0YXum5Vvq9gTUJ47xHYrav3qHgbdJeAa8IAXOU\r\nDTOLTWrdZ3Rwa6/F25uuPjH8k8D+n1SAe3ofBqlR67PJQhaTGR5EZmRssmhv\r\nBISlMW4LtV6Lk3/CjyFaFEBjbRy375pSMiPN97KM3BvkYUUqPvlaXTHXNMaM\r\nQs8ViCIDZo2gEHTqZdFhqVVSQFf/ncAV2zcqQOJu6/IMgHSMgH+lJi1rYOHt\r\nqRUDcqzm4XdPIBlG+rWL8HrVicH0D0tyUaFYBfIxOy/NpE58avrKxzG9BTOn\r\nLPHFW9t64SmO05BHQv5xncHNfAt3HzgMgh2+cbepJpNdk+41gRuoWSK8z4AF\r\nSANiMT/Xq422VT3mD6guBDJLDRZYn0Vmk294rhyoo6rXZ4JVvEmvW2EIGyx4\r\ne5wBy9W8WuEIZefoAgGwJRhn5TpjVpvyAKpHOsqDCGlz6WHC3iTQjeHSbU1G\r\nkfoOT06jLOMxNjjf7KIbU0z+Vf1twhAdtJY7Ypq7x615ZET1tnzXV1hGDQTC\r\nQOv8mPEpT9qCfSqXk1dsyinBMGeQV7YRZ/0=\r\n=124v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4def94b073cad300e99de378ba900e6ba9b7032f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.0.0-alpha.6", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.0.0-alpha.6", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.0.0-alpha.6", "jest-regex-util": "^29.0.0-alpha.3", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.15", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.0.0-alpha.6", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.0.0-alpha.6_1660917477343_0.1970194259151321", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "@jest/transform", "version": "29.0.0", "license": "MIT", "_id": "@jest/transform@29.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a19c6af7724786ac286dde2037d077372ce8c76e", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.0.0.tgz", "fileCount": 14, "integrity": "sha512-hwyBt8UR5o8GGaphmRqNQwVCctiOR8ncugCp/RlInEZvQ+ysKkS5TFfe5RgeQ0KtKdWByQqn5yA574LLOp3OWw==", "signatures": [{"sig": "MEUCIG3prPdnc3zb5f6MY1sSnpaZrP1pncIZ2IQvmskRIYFVAiEAmzKYDtRAlpx67hE2Ms6ls+egBW869ipQu4/4HmzQlL8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2weACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJXhAAlElqpsGoUhR/b1RtKI+QFmzdtA8EiXvxFXPWzMRQaZ/SxA8R\r\n0TQxHGAOYtoZtow4mcjsZYtpcpJhFQ1qy0DQQ6lDvYT+CRd5wxQLvN3mckNs\r\nevg/tw20/t8q1ghL6mC6nbBFUqoyvcG4nY4KFsDXZ4tUNBopzfSti1w+P71b\r\njkz0z3FdOxF1msF4g48pzuEc59rTEPcA/xqUIJeLuU/m6gMPsWvO25NZ4MaQ\r\nm1HvBec71BKe+Hhily8RxX/5BWOjiFgp5qLY81+VcrBSAGNTt5tuljP2dTTd\r\n+5OQZgLe+ITZf2pUp/YAdzg32XXFwF/vDs9G9cZrd+MLAI6sZ97KQNox1cYI\r\nA/D2hW1Z/GvUXMlmb6CVXPsZw+RMD0SSVleAia01T4Xng61zRlbr88j3NPqD\r\nEz3Qxc+hB0nf4L7PJUrQI4C17KyjnJzdGFoKKxUkY0hmIrPeB7ZMYlSoZb1V\r\n23esXiGyoUVy+C9yNqPz8gyC8cvaRyux5XBPkZL2vdrDm30IYKdJ45uTL0/4\r\nm8IeVf8AEupM6EAC2jKLGpLUZzmMIbhc5aMxyylvqtmS8pF9+30otXZkwGma\r\nYA1bq1epZqri389h3wgTTZj3ie/539vr+Gsxwpyv6ilnTpZsmPI+MVGZCCgc\r\nWMklp52rnScosYgelhvQcd2IP2qh53scyjA=\r\n=6sQB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.0.0", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.0.0", "jest-regex-util": "^29.0.0", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.15", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.0.0", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.0.0_1661430813987_0.338850403591874", "host": "s3://npm-registry-packages"}}, "29.0.1": {"name": "@jest/transform", "version": "29.0.1", "license": "MIT", "_id": "@jest/transform@29.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fdaa5d9e135c9bd7addbe65bedd1f15ad028cc7e", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.0.1.tgz", "fileCount": 9, "integrity": "sha512-6UxXtqrPScFdDhoip8ys60dQAIYppQinyR87n9nlasR/ZnFfJohKToqzM29KK4gb9gHRv5oDFChdqZKE0SIhsg==", "signatures": [{"sig": "MEQCIBPc4byp7Z5EwM6PWMMkPCLEHYxE0SqsrMuz32G96NVcAiAGaGeUHC9inXV0MlzOyZ3HfoEhlg+Xb9846OFd+y5w/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50329, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCMv3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrVwBAAiHLbtSDCC8cJY5xO2zYf8bLj518PuNgeRJq8ZG/hrKs5e3jI\r\nCUmMiJOV/EsgQi9TvrXhg7Kw6qrhkno9q4DfafT4LhLiuRLuvqRMrnrYhQpV\r\n/ah1teU8ddCe2yTsvuPjTmTsrwhQ1zpa16xRA9WlNZdRg471c1ZtOSHBt/wX\r\neTr4SV6YVGhJ4YuZb4GbI7g6ETCdZqgRcSF8TulVfQyZ1cPzTKZTGU/NWIIZ\r\nQCXPwyjiAJUzjJjQhoVg1Wn0BsVpBcyyO05Eoj2+e6ew0IuGenquMJZv/EWc\r\nZsYXQoZDjuLcDob8qQ9MNVvNk7pRl5vQ0QwPcIGdQ72zeuFciffQB/S8J1+4\r\nxT6ss/n/gylHIMPvOm5D7GKYXsFUkLSfhFKJ/rejYXcEP4C9kxT7KsgHldKl\r\nKA1J1jPyNveQuhWAFdaw6QhYFkEyoEC6PpVWZ3KpaAF0VMRNFVgZ8JyylT8j\r\nFOcalnO6Di3BPNd6xoINFAItV4igZkM7BeDcKpVa4HdxqOr3aa8TORzRP4aF\r\netpMUR83WTSIUX1ywkUlNpHEqIVrON4jUlV9ieOQFzOBNDHBiXsLtjmQEUN0\r\nOohVbxn+MFrYnMhyQF8FBhU3LKgtrMCHZBw/+WxzuO4qSngG03MC52pMMUMI\r\nhxlJx86gndWGuBaWE20GA7Gs69mqEd7lN78=\r\n=Oi9p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "b959a3d3bdf324ed1c7358f76ab238a8b0b0cf93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.0.1", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.0.1", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.0.1", "jest-regex-util": "^29.0.0", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.15", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.0.1", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.0.1_1661520887450_0.4838264496118245", "host": "s3://npm-registry-packages"}}, "29.0.2": {"name": "@jest/transform", "version": "29.0.2", "license": "MIT", "_id": "@jest/transform@29.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "eef90ebd939b68bf2c2508d9e914377871869146", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.0.2.tgz", "fileCount": 9, "integrity": "sha512-lajVQx2AnsR+Pa17q2zR7eikz2PkPs1+g/qPbZkqQATeS/s6eT55H+yHcsLfuI/0YQ/4VSBepSu3bOX+44q0aA==", "signatures": [{"sig": "MEYCIQDU5lB6qvZZi36Htm0ahWkGKDzSOf1PK6arVJU0bY5JOAIhANF3Q+PF5+a5j09hyhsewcMgI1ydmDdo+ciz5/ZNM/9g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50696, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEzD5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpqcA//WyHVkZNq5vZSr2i6xgSgQcw3PsZXj5HdOEG+Lkgd5uZfuVVi\r\nXI0B/PLNTAJglvkL2yDz+h/PD/+WegX+WotGwE31+gu5oB0xnKtlvxed1AIZ\r\nigoJFKQ8dFh+gKtB7Jf3R+2YHtW/xEqeX3eDnJcNyw1CVdeZfzCMLgye2Qox\r\nd1p3pqG02UJWfGfI0TgVrxqzHZViOJswMneKn5ieULIdcL9E0V9CTjOVbeBJ\r\nr2HlDDrWn2yjmVU9JiB5gOhwY972nSZDcrJxvp7rMm44bw+RaFONTdZB652W\r\nIk/wbcHkhRblq0p4kNzijTFUil0jnvDxKLN2mhSOcOh2OIITb53IF5VsCWBr\r\nnYg/mLzk+9XKMrxqN8rqQ2M9837JhZTbJ2daw8l2PO6/6u3i6xQW1DTOahI5\r\ns6Tkc4QocG0jkjqhDxrPPLs7oup8MgAU3lwF8CHwFmMOIJGyL8IQjexk8hfC\r\nrQP3NolH05CS8TBJMOF9qhYKo5LJHOPJRwCkBRidSXo4Wo8AW0vMtr6RNwsn\r\nxYgRT2SoXnXcrX29uitAFxqfB5ZN728rZKn5wii6AI2z/V6/PMcLBeqddWvI\r\nYW1UI/CDrGhILwOWjo+ys6gQPT4ZzpJgj5pLNr/U46MhrysoHOdrYqbib4N6\r\n7YgVGinbOelql2IFpePVUTTcz5mKj22T2HU=\r\n=p9Va\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "616fcf56bb8481d29ba29cc34be32a92b1cf85e5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.0.2", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.0.2", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.0.2", "jest-regex-util": "^29.0.0", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.15", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.0.2", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.0.2_1662202105293_0.160180061482033", "host": "s3://npm-registry-packages"}}, "29.0.3": {"name": "@jest/transform", "version": "29.0.3", "license": "MIT", "_id": "@jest/transform@29.0.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9eb1fed2072a0354f190569807d1250572fb0970", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.0.3.tgz", "fileCount": 9, "integrity": "sha512-C5ihFTRYaGDbi/xbRQRdbo5ddGtI4VSpmL6AIcZxdhwLbXMa7PcXxxqyI91vGOFHnn5aVM3WYnYKCHEqmLVGzg==", "signatures": [{"sig": "MEYCIQDNbZFcqKHnCFOOEnd71TgznGpiA9zcOC121eqDeEtnNAIhALrLNKSVC8hpmXx/rCsKBkaS4gFo9Y712+Eja8oYySMa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50696, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjHKIuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJpRAAigTIzX40b8IeE39qVLXmQhRIPPWi6XpaMJGxDcYFI7p5oLGg\r\n9J+++GbYZQN4HvUGCUK+fUYk5PVnlfU4TCUWSIsSApBrfprws2Mi+4kukVmW\r\nry+V/8KnMhAAV13XssaHoNzPW260U1CE68lUUYOnAfciLv0V6l+UmYgqBcRy\r\nA8QTmyA6fKF+t2LiHNqSQ2zqDLiHUN/n6TtnmFMabo/WoghXJ0rG2PRajBSA\r\nphuTEMepBWqe05YAsoUGaPnHbIRnw+7wByNbPxUWrD9dimn3uZkuyUXR7Wgm\r\nRuDau1otEcb9IjYxeBUJLXIXkpeSvh9h/yNRmbzCO9zG87nnHYM3w+hn17mB\r\nGLxIY0vfewhvCt0NAP1U87cPxDoftdpJniJBobVV8VO+/RtaR7BEs39qNQX8\r\ngERV10H01CfnNooAfFkm8kcbjcPWKIBafaeC6VaZJsGzZliAzRFSllDpvo8X\r\np1yW8lwJWf5thuMafoc0Vm5k3oPy9T+Z6ClvQpGcEcV4Pj+yrosJsQBQkzn9\r\nQxbzFIsMb7ETlO34JBN4OfsY2MFIKKdZUImG8ZU779p3a4Y0Idq05vOdh7qw\r\nmMW1hMiPLTMo3o6NY0zEfITDZNzNqeoRL/DhQRVR3lvPc7rkn7j2UNhYbq6j\r\nK/tfzL0/AQZAK74917dsDRKqSSDlyiyRlgg=\r\n=kLDN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "77f865da39af5b3e1c114dc347e49257eb3dcfd1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.0.3", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.0.3", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.0.3", "jest-regex-util": "^29.0.0", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.15", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.0.3", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.0.3_1662820910630_0.9978578168237049", "host": "s3://npm-registry-packages"}}, "29.1.0": {"name": "@jest/transform", "version": "29.1.0", "license": "MIT", "_id": "@jest/transform@29.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1b3e597681362f6164e786e6bc2826c34ecbf74f", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.1.0.tgz", "fileCount": 9, "integrity": "sha512-NI1zd62KgM0lW6rWMIZDx52dfTIDd+cnLQNahH0YhH7TVmQVigumJ6jszuhAzvKHGm55P2Fozcglb5sGMfFp3Q==", "signatures": [{"sig": "MEYCIQC6+c/6tGsRU/yYdpqdF21pppjjFi/MHRKNJMwQXv7+oQIhAIPhoFrXy2pWhGhIe7hxEG9pHCYQF1dCtULvy4zxX5BL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50873, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM/nKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqtHw//ZlJUD4iR6aSiyHFfqLTfncrbxo3S4kQTPg21en2gLAeIFLk8\r\nwXTkcQ+hm6oS9rN8iCmokrBQ8RJZqEB3j3lLaAj2ROMzEWfHk/DTBDiXZ0ho\r\nAPdW5KuPbeBE0/Jcr1Xitv7rXilILKbW+8KeLaTQ19ho+xNT91DckU2rNbCe\r\np/Lp9+VfiLf/hwaxAs5Ot2rvNe94MHHh3CghrPOT8/wk3nM5ZvRkJ5dc8K0T\r\n9dRgHjvFbAS+HhYQ0q3oU8gp8q19lJ8WkiAWwShausvvt+4La4WQ0r845QKo\r\nAlAWLXsr9gQKL2iZS2N8MpnGEQKuyjipfw/sgO7Sq1YseNY8G6FYZZtgvTPy\r\nDZ+kOvBcrWVAqskS6kdxAt3PKjhkRVuaemXR2G75LA1FR/b+Bw8XorO4bTp/\r\nhRq7b1Ql9KTvcuH8dRZz1LsTs9N4TrEXl4Z1euSuaXbwhrN1DRLA/ypwhOxr\r\nwfPc33hXsWsZVateglF39EaSAB3qLPSnk6yj5WOVT0H6oyn1skeYlAb5Q8F4\r\nuC/hGPRq8UJ1bKToD2cOxuNfazAm8rBHU6blgYGO7kyZtoJ81dfUaFpcYA+f\r\nwdSYqUVZ3alytxKV1Ysas2odcfR/Hw2yk7qQp9t4/z3WwvuZ1Mj+eURo110B\r\nLWSCbTSdDA5NnqhoaEPIOu/dro6qsM/sBzs=\r\n=RMvV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "51f10300daf90db003a1749ceaed1084c4f74811", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.1.0", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.1.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.1.0", "jest-regex-util": "^29.0.0", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.15", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.1.0", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.1.0_1664350666679_0.23209549346106928", "host": "s3://npm-registry-packages"}}, "29.1.2": {"name": "@jest/transform", "version": "29.1.2", "license": "MIT", "_id": "@jest/transform@29.1.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "20f814696e04f090421f6d505c14bbfe0157062a", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.1.2.tgz", "fileCount": 9, "integrity": "sha512-2uaUuVHTitmkx1tHF+eBjb4p7UuzBG7SXIaA/hNIkaMP6K+gXYGxP38ZcrofzqN0HeZ7A90oqsOa97WU7WZkSw==", "signatures": [{"sig": "MEQCIARBmz533Rj4TylRlIlNm9WyZpNlA9YNha8R+OxPUbZ0AiBAFhXncRXm/keL/fUZ/dx22oiC1ijgvCzXK5AhxQeO2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50873, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNplNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLjA//WO/0DW0KUaaDgM9HaHjM3gUixFvesvoPMj3zuhNbiO5CyDTX\r\nGTtQkTIhVAwr4azNTrYFtwNQCRTNV3Z24wwLX1BUSu437bL/JW5od9zmvcHK\r\nAwRXnWKnYNL3UtFYJsH0wVDAEOYOMheLU6Amfac5eK3yBdEBR7HWN0X28hNW\r\nN+Q8G9dLk8iCClfzijYaO3qXrvtdFIJ2JF/SD2M6yIFd1TXEM561J1KOnMqG\r\nUwa8HWpO1GTl3ViQBd3jSaalVMvy8l0Sx2aj3eoTwrU7ONxdvxjxcCCOPqzR\r\nRpe0BwoueMYp65ZECvsaeSWxpHlLg12Fzrl54kWfm7z/aPgh40nk9/kxy0Ps\r\nS/vViiQWsL4Qbct0qwkrc4dMyBLHggPLdjei3Ua2Y1p5CqJgzb9s8dLwjsr5\r\ng8RUuyKYVx/ACeD//AQ28Sx3uFvU+YYSUETGhW0dEDJCxtdn/fIBcrH0rPAh\r\nYdwYaijFmResivFvq7jPLF/Pfkl2UZk+qeqJ2Ib+xrmP31TpROhngqFpL9FF\r\n3H4irNTQWhaSyoWUiXiHH9cgzYOEZ2cQCzHjwZCIMVX6EJTjtjD0F3M73cKw\r\nqUwJ9FcTRWL3uv1doG4J4MH2mM76D8dHtF2HhKVYASfnao9YPBLIpfOYTn1u\r\nZsTbkF0O7VMGyvO1Seurq0kHJmmB5Rc6PoA=\r\n=W4bJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "3c31dd619e8c022cde53f40fa12ea2a67f4752ce", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.1.2", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.1.2", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.1.2", "jest-regex-util": "^29.0.0", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.15", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.1.2", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.1.2_1664522573118_0.1442477039675234", "host": "s3://npm-registry-packages"}}, "29.2.0": {"name": "@jest/transform", "version": "29.2.0", "license": "MIT", "_id": "@jest/transform@29.2.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1c55ca549f64810351df999265a29f8ead51be15", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.2.0.tgz", "fileCount": 9, "integrity": "sha512-NXMujGHy+B4DAj4dGnVPD0SIXlR2Z/N8Gp9h3mF66kcIRult1WWqY3/CEIrJcKviNWaFPYhZjCG2L3fteWzcUw==", "signatures": [{"sig": "MEUCIALnBMQdclRdUJnyOEugja6m1K0ytGsI1UKgNe78vWrpAiEA0SJRjMDji1P/6NEV63mWENb42CtwgEuUopCfLjyccHo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51215, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSShZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbYBAAgpToRfu97uUa7W22yc/PskBCRdvwwdswKAp2xUkfDNTYG+es\r\nHSKJ6dkdqYfi6OYFmBrZcHf4WGUBeb97JJmm/oY+yGeiZcZhAp3U1ZpVGcqE\r\nTwSgD50F2zV4xKywWgAO26zb50Coqw1SW9f02ulQ88hkROs7+O4NsTmqihf7\r\nrS3mpwoAam1KWfqYmX8VaPfED5elT089CIzcFGeZWm8qQCueAg90wmCW0289\r\n2JKWOrCUrpSYms+qg4aLJ5wSQ20ExRPSEt7uWDb+S0tgCPLEEwMjWA4fV8/U\r\nXvSlKchoyh4BZnnjPKqFq1p4aziqUEcKEQGZY3I5SvPfhLgeK93dD4vAIKaF\r\nRZQrbrV6s3jo941OQdD6f0SNVMbobCCZD7gza15vzpzqFpfP/uoar7yr8Beq\r\nEiowAVEqiRqR74RN5faUwVyI5BmZXFv9i1g+wtJq/KapBfPQR10fuG5pmQxs\r\n/0Q4Wm0gKGodMSPM2873jLAgmjqwGlzUTSCXVPgQdO2oZ7eYt4m9MSFALAz0\r\nqFyT9Wh+1aOirxzh539ys7n+gv/QYiEN4yLJWQ8mRr8wx5FKW6ghbotzlY4n\r\nJz3wQih/ifjJVn/7NnruRSR+at7kpUSKFRxIJJX05yVzpTqV7mPSiB3OuVFF\r\nS3W80F7hZ6YkP6PVE6B8qosU8CBr/R1wVP0=\r\n=lwTS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ee5b37a4f4433afcfffb0356cea47739d8092287", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.2.0", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.2.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.2.0", "jest-regex-util": "^29.2.0", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.15", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.2.0", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.2.0_1665738841432_0.7119325123107978", "host": "s3://npm-registry-packages"}}, "29.2.1": {"name": "@jest/transform", "version": "29.2.1", "license": "MIT", "_id": "@jest/transform@29.2.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f3d8154edd19cdbcaf1d6646bd8f4ff7812318a2", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.2.1.tgz", "fileCount": 9, "integrity": "sha512-xup+iEuaIRSQabQaeqxaQyN0vg1Dctrp9oTObQsNf3sZEowTIa5cANYuoyi8Tqhg4GCqEVLTf18KW7ii0UeFVA==", "signatures": [{"sig": "MEQCIAjvDrCucLv3D1DDE2Jz39kdsqYVT6W90KmHZa35/mFCAiB9SRXOhl4x2pdzH2augT/km61/hv7OfToKQrP1qaLxJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51207, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTs2RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrm3xAAlHv7u4bR0sB+PPnlvnIUo2GYALkx8OS0AaGKpk4ZXLxn8FUG\r\nycbjnPlVHfQAvePIMbuf0kcgVep75vqw6fqlbEWGnZWRzz0N/X+GqyfWTCNC\r\ngddpeqdn2SgCekXCMzEQc/X2cbERslQV9XdSh1pm1FdaFXZosst6GkXZ+2j2\r\n7OWlMq7a7izNJ567n4ZZU1D5MDiL168ribnYNDO8xej0jV2Hqu9jXrpQEF5p\r\nuvzMhlBSeUMPwlndKrUTd9ECAFM87+BXsD+x0O89TmDLUBP51hTwisHwWCGO\r\nemh+KiqdPfEdp13DomvHVbf1UEBe1zXOl91SUrkmF5hkl8HJY23WitAr55Nb\r\ngABgCa1LCnBL5aWRs1wJxEooVHqomVCT9/6FpPUnkZ6HCElAeDjGgmFEvnKQ\r\ni770eMXGPtv9RESX/J9Zs+54SLlL0SuOqFMi2yRCQnfN1VFyOcjil6m4+bNn\r\nnn1+wcn70UQ7Exw3Qut+/zUbI+ZPBLLT+euGdk7D7V0QHStnZ0pUWzC+/aXd\r\nu1/tFzjrB9OgrdhlRkpetlnWbAqrFSWOuIvro6Ib9zQhBVMX6mhOsRLabHCK\r\nI/bhwzk6g9pFDfGj6I7ISyIVMZHpgtsgNR5gYz5ZGRFsNJ4Dx+lyrsx8CCTI\r\n2RGT/4o+E2S1jnFrCiKd2RWHRKjb2JR2QLs=\r\n=c2tr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4551c0fdd4d25b7206824957c7bcc6baf61e63bf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.2.1", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.2.1", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.2.1", "jest-regex-util": "^29.2.0", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.15", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.2.1", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.2.1_1666108817458_0.8220640636035046", "host": "s3://npm-registry-packages"}}, "29.2.2": {"name": "@jest/transform", "version": "29.2.2", "license": "MIT", "_id": "@jest/transform@29.2.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "dfc03fc092b31ffea0c55917728e75bfcf8b5de6", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.2.2.tgz", "fileCount": 9, "integrity": "sha512-aPe6rrletyuEIt2axxgdtxljmzH8O/nrov4byy6pDw9S8inIrTV+2PnjyP/oFHMSynzGxJ2s6OHowBNMXp/Jzg==", "signatures": [{"sig": "MEYCIQD/PAaiYC3Wx8d1qlVNNrxsfnLbnudNQZHf59MiHYsfUwIhALgvIhvilzHEPF2jgxWn7wO0rTUMXt5zYhstddeqr8FH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51212, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjVvRmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfSQ/+LK9TvRq2PZx1/Odl2Y38OJrbWR8bFAf86J02xsOmlgvrd4GE\r\nXb0P+fcDPMJJuaM+k8gHjMRC7ADW4IhDemCg+xapRhmmLAvTq8o3jzrzt5k3\r\nr2cnGjOz55T7ICWUZNZYaUP8/cT5aOrBwFb+WaW8679RdmZlE+5ZdMTm+5NP\r\nfIiT96fuCWINcOrVpRad31eJUyWrua/HzcaO9QMGp3X9mXF4jNOGeDyFFdCd\r\nLxzO4MwTRgcbEt3MiG7vg4T7kJ89DqRxkTrl/GABHn5dkNAvtCqaYbiSHPzx\r\nuACdImIRx8mRzawcstQYIDx50CNXjj+2zfktQ+CbbyJ193GBDiMpxFa1VygY\r\n7VY55GKBiw6SMwJckEI9jnO4nS/bc6QXdegI1+iMTM+BsndkEsOB5NkTNBWW\r\nD5TZKzmB9PllMreOTDjegdsFguq/aH4YBj6kkgng9RnXH/f1Q8Pas1BB7OVe\r\n4vtsajkBOJwF/fvrfeOMiUbBN49pQwTompgNDH+9b6I4HjBog3Pynv7ezbWK\r\nvIEHxP/kC0p0xUVjRd7lzFvdTt94KEpsho1x/TO1f7wN5gs9ZglYDzPKxozb\r\nHJjbHPHen9iMIjciIfKiFqQtyILpnqRjBOHZubDCRS3QWU6RCU4Rdf6YITUi\r\n0/jNht1hoZY8foMV99GFGHH2lMxZC6L9w2c=\r\n=l3Hm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0a8edbe0ac434394a16cc173a03ff54a9cc50e41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.2.1", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.2.1", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.2.1", "jest-regex-util": "^29.2.0", "write-file-atomic": "^4.0.1", "convert-source-map": "^1.4.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.15", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.2.1", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.2.2_1666643046114_0.38558565541279477", "host": "s3://npm-registry-packages"}}, "29.3.0": {"name": "@jest/transform", "version": "29.3.0", "license": "MIT", "_id": "@jest/transform@29.3.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7f71c9596d5bad1613a3a5eb26729dd84fc71a5a", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.3.0.tgz", "fileCount": 9, "integrity": "sha512-4T8h61ItCakAlJkdYa7XVWP3r39QldlCeOSNmRpiJisi5PrrlzwZdpJDIH13ZZjh+MlSPQ2cq8YbUs3TuH+tRA==", "signatures": [{"sig": "MEQCIEQrKeZ/Q831vMGxE0CO1PbjpnF8pkhUONOCNVvc4p4AAiAL3JMHRUQDBlSGU59BMzc0vM7/NgYYlAWTkmv7JGhy7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51212, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaUamACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpk3Q//UJVuhybdTFlfRHOu8MpP/XH7P1FqqowH7pmPl+BUYa5Tpa92\r\nJQuN6KgS64ZE/GJeWy4C3fVf6Cru698IdVwcDfTi5Vdb6WJ30a5t7TOi/tN7\r\n/7P44fJoNiC8GcFlbde3bIe+rwUe+bgHhYSFa8YDulBa0hahFPH5n84g/Gr9\r\nrYQjIpG1+Mk3BKBBCShilu1cBTsk/QaxNSaT5ArOIo7p2QUvbFhvmt8KXejy\r\nSgBWqwzct4sSqv3DROmbfu4juY5PWT9z4o8xoFZa4AMbB/2oq/jy36EPomF5\r\nSBBYQGOH8DK4fYv3kV6nGqTbAa1PSXDTtJSXhI0MMpyRND8kFQrl3/Lesl1d\r\nYdKNwzg6kZPy2N+KmIxi39UvyWMEwrpplYDq9zEijT/nBen4Wfe1kN9Ddvbh\r\nBzKp5m8x4JStII/CmYJZx6XO65bqNFfSk7xmeBaf74hd/Rv3q4shIEyKwf64\r\nx0nrKoFL7npddg1dH09EX+SeiBvOT01kAn95WpeaEYqkPfOhM90yzRHr74DE\r\nNuyvQwV5E0XfcrFM86mtPvFR8cu3E3PIqQjAymFBCfuBN7eGfseXX64rA45u\r\nfP4EDrCHQeau6jNQPxJlWlpHTZKOGLfo8hG4kOA5IB+e5R63A6nygFZW84gC\r\nKzcPYLuD6ng5dImMm8yMHSk7qQLUIZEETZU=\r\n=msol\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "84b8de987b33e2da20dc833aeb65f23d72a673cd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.2.1", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.2.1", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.3.0", "jest-regex-util": "^29.2.0", "write-file-atomic": "^4.0.1", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.15", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.2.1", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.3.0_1667843749828_0.44147724959806856", "host": "s3://npm-registry-packages"}}, "29.3.1": {"name": "@jest/transform", "version": "29.3.1", "license": "MIT", "_id": "@jest/transform@29.3.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1e6bd3da4af50b5c82a539b7b1f3770568d6e36d", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.3.1.tgz", "fileCount": 9, "integrity": "sha512-8wmCFBTVGYqFNLWfcOWoVuMuKYPUBTnTMDkdvFtAYELwDOl9RGwOsvQWGPFxDJ8AWY9xM/8xCXdqmPK3+Q5Lug==", "signatures": [{"sig": "MEQCIHmAgf0Mevs+wozsauC0ZzG7NDqIp3td+TYEx226F/XoAiBWS0A1HT5duy0VmX9MrLigT8ZgHSdKMSJ3wCeufpzBgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51253, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjat6bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbDQ/+NeGwx2Y7wUJkBAiivkmaqQ29cYdOxo8FA0QUWVrDEwgAFJr1\r\n2h214TCc1EVWRYr1xGJPwF29sfmVKIhTPVuS+D41cxKLMSrBZGUPDwU0x+kK\r\nzOcJOk6wxZSNzhU5YrU1cWiVv/PR7CPTKxTc4on9NgMND26ZXico9pZ6b1qW\r\nlShQVnRRyQOIkmakyMSyIXaV6MXwZzQxADtJuOyYfwbEgj3p1D98Z19Vd9mD\r\nF9MeYPmP3jn1mqARJdKqgRIGK2fW5W+wNbQM+/YtlBA34IUSHmeo5x/v3ZCL\r\n5FctUAzftQ8YF443nbMjvJPTsHco88QuAUSVbLQU9mrzzuexgkRUBblCWaGr\r\nC+38vKrFcXYgly1V6V+1+IA56qomhaM6gnOOnMvRFGPYNFUVhxroufQN+/2U\r\nXPJv+FDhR9IWCQOYBz6eCiAFK0e5Kp3d4rZ1nXEFxNqPiHdftk3U7RfemdAZ\r\nMyQTr14FEnh32ZsvVnhAzHRR2hQ78fB8pXhWOuh8kztD2JDPaBXoTfdeWwZk\r\nFPn38HMrTmJN9hgA8leAMpPjyVkc0Dwe76DGEX0UOl/2gRFBgTe2DQw7g+jZ\r\nWjQbGDraFvQc9INkyAJIEe1xL1XYl5HL0Fl91EwcLWZxNTim21zVmHxZpPiB\r\nMfJ+1DmUTmY18nO9oUF5jnCaTP/Dp16yO0g=\r\n=q+2t\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "05deb8393c4ad71e19be2567b704dfd3a2ab5fc9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.3.1", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.3.1", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.3.1", "jest-regex-util": "^29.2.0", "write-file-atomic": "^4.0.1", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.15", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.3.1", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^1.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.3.1_1667948186964_0.8769417079173325", "host": "s3://npm-registry-packages"}}, "29.4.0": {"name": "@jest/transform", "version": "29.4.0", "license": "MIT", "_id": "@jest/transform@29.4.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "73ddd9bf8599d285af09e7e3fef730c17a2fd965", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.4.0.tgz", "fileCount": 10, "integrity": "sha512-hDjw3jz4GnvbyLMgcFpC9/34QcUhVIzJkBqz7o+3AhgfhGRzGuQppuLf5r/q7lDAAyJ6jzL+SFG7JGsScHOcLQ==", "signatures": [{"sig": "MEUCIF2e3L5y85Y76XVdnbOAGPRNJ6MmijQP6Eepb0DAVRrvAiEAhNzUfSLkf2Hr2NOPN+O196DtDuAx+VF5Ml+mmVsfxxY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59200, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjz7k+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmorpg/+N/9so0NtwEsar7AaMIepuvZKeMmzy6uoP4Snhve7/QFnAAys\r\nU1XAe9FGwi/fg0a/7IEgOXCDGBgjkrkDTGjcYCNGNmsYPFh3BkJLGhCHZdPY\r\nn377sz/SNnsPKaeEi4iaeP/Tr9Yc4xcdFimk0listtqMPICvXhAcvHficNxO\r\nk8Bl9BDjMmxBy9+dQd6VWRJYl3aKCYcdIZGbHjFMVgDKZoOArXcDxosFcsMy\r\nyTDwP7E1KuABEkVBqOZaFquUTXTmXyDJMG/51bjuv/ZDhsJMJkYWnWbqqmVu\r\n1kZ+OfkWTwrSgb9wMp/z5n9i74NnpKyHoWZASjqIb3W3bUsL0DAv3ua1+umE\r\nOqUFZ4sqTf2/drSEUAvTj/q96Rv10OU9wncarxmZHlt350a7mVghuAxbtNDB\r\nHrPKMQ1OloGGe3ofnPO+C1OvWPLew5nmf8HPFKBKZZsA9ScOVVGhxLyTRhM5\r\nBUNHvrpnJJUYt7FJx757gz8loScQHu3JWkayB76n7WdwFAQ6ZHEHkB2qhqbk\r\n5AXk3hu1CYyZ4zRKG6tcKw7Nvyt4+l48VMUMwuFPnLSLyI2SV9m9R4fjU4E2\r\nJ4hBadws9TVdAQ/loT9frcqE0zvwO8d/I0Y7qOc9CRDe3itarg9haF0pdCPi\r\n7fVwwIIFJyYlmSLOfJcGquk9uwmhLlYY+k4=\r\n=kWCM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4bc0e8acaf990e6618a7bed1dca67760c20bb12a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.4.0", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.4.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.4.0", "jest-regex-util": "^29.2.0", "write-file-atomic": "^5.0.0", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.15", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.4.0", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.4.0_1674557758021_0.9857662417506927", "host": "s3://npm-registry-packages"}}, "29.4.1": {"name": "@jest/transform", "version": "29.4.1", "license": "MIT", "_id": "@jest/transform@29.4.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e4f517841bb795c7dcdee1ba896275e2c2d26d4a", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.4.1.tgz", "fileCount": 10, "integrity": "sha512-5w6YJrVAtiAgr0phzKjYd83UPbCXsBRTeYI4BXokv9Er9CcrH9hfXL/crCvP2d2nGOcovPUnlYiLPFLZrkG5Hg==", "signatures": [{"sig": "MEYCIQDByKsbH2gI1o3nZ5m57irPsZ28X26h6jQvbnhABvxsOgIhANDLgfriLjoWfqcaLAzG1V/4B6jN6B6yAgQ2D2oSAT+6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59200, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0pd5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqi2A//ZdXua7/9Xle7tzsEML9Sy9MkBNECpQ5ZQlqHB4Og00JKUh0R\r\nnaE+vvNScCseKhXBZUGawwsblxQmMGTwQDU1RzD2y5EMm2sPZqt+JOwPdQur\r\ndgYfyZAjnrBRH45+5DzH27bmPtpAFUJScd9ytv7e0Js0+fhsf4xSGi2EzfOt\r\n30ONYiJQtI3rlP4JQXYUyPVtwG7CrK5LJ4nUsurusXfnPUQQocAPw2bZ1kpU\r\n+Xg/8MDL+byEr0kSXn9j6nxNkbzO9YiA4HlVS1UnlgEvkskwwMCjTR0EmAhx\r\nSTnqvPRY6w5bNY4tG5WnhT6WpUxKxX2yu9iUaPu9BDzkv77TBj99dfZT2gYr\r\nrPMQeTGRHXyD3psq1rRkUyZwQqjKbN5MiMjJBzLBXbOH+ShgE/PR0lXnMUol\r\nqxcTud9OlGiR05MFhljp2+/P9++aEdJ/jgcek5cZ/VAua/gFNT7lKzpKRMfs\r\nLayF7tOKqLLat/oeNxl/BAIGMg+c459vJA29Spb98ihdF/Q9G7I1vaNzxKs9\r\nrjMj5k1Tym9xpsvwgXxkh5QUJ2pyMVK96tUheRhf8U6Py6IuozJswjaHsZRg\r\ndBDJM4jo6T7/Zb/kC+t10xyiCenoL6zy0l6+Pstb2o0W4BsF7jaFmqSeCKF3\r\nugHWQjoJ5yBSW+F3D1/vu8cHj3wNLYlAtaM=\r\n=u6qy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bc84c8a15649aaaefdd624dc83824518c17467ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.4.1", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.4.1", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.4.1", "jest-regex-util": "^29.2.0", "write-file-atomic": "^5.0.0", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.15", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.4.1", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.4.1_1674745721037_0.20468327827890742", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "@jest/transform", "version": "29.4.2", "license": "MIT", "_id": "@jest/transform@29.4.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b24b72dbab4c8675433a80e222d6a8ef4656fb81", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.4.2.tgz", "fileCount": 9, "integrity": "sha512-kf1v5iTJHn7p9RbOsBuc/lcwyPtJaZJt5885C98omWz79NIeD3PfoiiaPSu7JyCyFzNOIzKhmMhQLUhlTL9BvQ==", "signatures": [{"sig": "MEUCIQC+pkQ1GK6QqRUEaFQ3IncQImn54y7WFi/WN4Ddjm9DbAIgegEGu36WziUyPTZ+Do+/bASFWqCgmeXj6s/ibEM/HZU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lYDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmpA//bAatgshvDRQCTXci8JErlBCe4hOdS1RAugUhPfDerAW0qSGZ\r\n77CTL83YN5eELiIk9u6EcHmLR7tYbXwX9MAo5Dt4ryIBPceyfY2y0fAL5yMg\r\ncYDR+qHkHs2vdWf4cszo/LQwJoqwvIK/h5BQcbJOxqw6fHhWUhzFHhuI4fj+\r\nwNnNe+T50MPmEhwQCXe2oauXeJOWPmhcSpnD2UQbITDVhLmoCfAoucorcWKx\r\nX90jRc9l7E08vXDaCtm83NMNwucvg9SKBeI3Wtaivc3wro0YPkhfDIlISb3v\r\nR2UvEBVdTTSDajdvDo3nlbwm+B8kzizpo0hZI4GJQfcOK/NwmH4snxAP8IEj\r\nFSTvFE6P1N01gXdIVuU2gaf7l+5JGODVuxugZagq/vETDLKa/QaRg0ECZa1x\r\nd5NtBxi1sFHs3tkXNGb9/SXCsHkQtQ4G9eV24wth7YnOUQN9LrB23avCXdSL\r\nno2n0Y967O4LMxLhh879nV6Ilqio81PF6LbcoKb8BgOgbmBPu0YTdbEYLd/l\r\nYIGGz2FFcBg7hAXkya41dtsOIQJAi/UoxqyhS7MTB96vIe7KVJ78IrlM/a21\r\nSXCrRa6msxeYYq3zVkF15oQFgrmlrU1rOoPTsehhMuvohMsFfjHwIUvK3EDp\r\nBTGFRdLAeonpyBN6cvhVwGxvRDeIYQUNvv0=\r\n=Rl0s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.4.2", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.4.2", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.4.2", "jest-regex-util": "^29.4.2", "write-file-atomic": "^4.0.2", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.15", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.4.2", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.4.2_1675777538909_0.16480494872199136", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "@jest/transform", "version": "29.4.3", "license": "MIT", "_id": "@jest/transform@29.4.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f7d17eac9cb5bb2e1222ea199c7c7e0835e0c037", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.4.3.tgz", "fileCount": 9, "integrity": "sha512-8u0+fBGWolDshsFgPQJESkDa72da/EVwvL+II0trN2DR66wMwiQ9/CihaGfHdlLGFzbBZwMykFtxuwFdZqlKwg==", "signatures": [{"sig": "MEUCIQCF9jIUuM/c1bMDK3/g1G0Z8xNC0CAHlpdy/b8SvNR12QIga6tO28k2Uqu0SFKd3eVWpY32gHip0Yn6XHGGEJkaU+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52145, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MipACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpX/g/+OjYKEbbQJ5Eo0ccHyexWOtLzp4VhbocpPzqMBzV3z1WZdB5Z\r\nNA8t/W5HORIwi2eaEYW7aIY/ZecTjzgD2tf3DdzbyrZOUF53PzunS4WBIfVw\r\niZZRUCs1AA4Odiz2FWX4jgDaNtiJ0eP5EkQXO7HmjoSgUH85g45C2QP1YXaw\r\nJN22UTpA3TRCHQ703ci2aMQKwbyQ1LrcZCdrSBw5rc8ChIvei6bZm9dlf<PERSON><PERSON>\r\nW4JXqmmsABIyopfUrocCGTaLTzfBb3mIkzruKQc7wmjNDmEDCSK6S/ym9M0g\r\n0yqxr4+nZCpa8IMa70uC+I5MU1v9Nn3GJt3M5mwHBAsl4bkhKAXuf7EbFjc6\r\nZ+Wb619WfGw6an1kdC9HzVIAN/f/nC5HR6OWmcVNbTvkET7GrqhEgkfTEzmu\r\nNe6uwi/d0RP7/Web36tkWWfSFJRaNMAlar+5zV0xcwlZn1rzxbkA9HDgpdaD\r\nA2zDHbX00j/xo1jaDJ//S4fA86DjYil+ydUhZ9B0LcVKSKEvneDXQ5RGo0nn\r\n6wd3BMB9amFM+xEiUVx3lZs2qDu1873OGxKwhApswyXqUay6UbXlGWHARRZS\r\n9aolyRDVYBRYmA/EnruIS83lWgI/BJozeksVCYnrWdjfQueuEE2yidyooLvF\r\nb3ngtwegYJuRnta6umWoeGra8MCIBqIE/sI=\r\n=7yGl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.4.3", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.4.3", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.4.3", "jest-regex-util": "^29.4.3", "write-file-atomic": "^4.0.2", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.15", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.4.3", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.4.3_1676462249617_0.48951972321119874", "host": "s3://npm-registry-packages"}}, "29.5.0": {"name": "@jest/transform", "version": "29.5.0", "license": "MIT", "_id": "@jest/transform@29.5.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cf9c872d0965f0cbd32f1458aa44a2b1988b00f9", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.5.0.tgz", "fileCount": 9, "integrity": "sha512-8vbeZWqLJOvHaDfeMuoHITGKSz5qWc9u04lnWrQE3VyuSw604PzQM824ZeX9XSjUCeDiE3GuxZe5UKa8J61NQw==", "signatures": [{"sig": "MEUCIEqYT5RNmyszUHBDkAAjMSSHhCtpEUYJdyIOEwjfBZEaAiEA4zu6XMext25sJFKIiM/FJBf4dx8ovYJHoDVs6R9L7GE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBeuyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmorVw//eeuTse/IPKh9exvorICUfnOH088jjQFVrR42uHdmpg005e0N\r\nY9u6uAMwYVUDStrxvEz8PfljoRY1K4nAly7hE7FiThmsPKNkQuxWpGvIi9fs\r\nzvc3ygHXGuEgI4ovbGY4JTDTXT4XYpF/QUTdtI9AKJ4/58LpB3GHQuny9ueW\r\nmRnarUjHEHL0ytJSjWV1npuPmnU0EhEzlsP/iCcnj/fODxV0yFI1HhFaTH/T\r\nG8gyKA5devr3qoBAoOJo7OQor/3CBhVip3uFx/u73+JAk7xVjKtE5ieHTgki\r\nJt+cj2FjnngGat1Th/3Id/eIFV+yashpegLrhvu85MM3PYcVfYdQCmsneQBE\r\nSMW1SRXe+GpPtCuQiWasb9HRGJ5Nk6q4uM4dr3gKv1l1d6GSvmEJmiR/VT+Z\r\nl1vthwno/RyVI6vylDm2+01QK98Hw8b/1a0Y9bMWnkCOjQXAHRgvOSUI6qUN\r\nzDgQKsXPniFgyelJThdYuAYN5LUzEktZpSAjP5qtB0sWw8mqrtq5XwNZLHJP\r\nYwR1GbbIAYMhVGDM7yx29sTjrLR5k90oTwAjzwh8yITG0hB71n2MMybPDekG\r\nidreRC9rJwxnfl0sWjeJpHeY5SBSHXVPOOrgtmEwkYtcAGcmelY9fo2HjmvS\r\nc+WBIzXQ0e06U2uBpkfedltEZ2o/1wdlXEs=\r\n=IlOX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "39f3beda6b396665bebffab94e8d7c45be30454c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.13.0/node@v18.14.2+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.5.0", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.5.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.5.0", "jest-regex-util": "^29.4.3", "write-file-atomic": "^4.0.2", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.15", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.5.0", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.5.0_1678109618039_0.6761746760320602", "host": "s3://npm-registry-packages"}}, "29.6.0": {"name": "@jest/transform", "version": "29.6.0", "license": "MIT", "_id": "@jest/transform@29.6.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "dcbb37e35412310073e633816fd7dbc11773596d", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.6.0.tgz", "fileCount": 9, "integrity": "sha512-bhP/KxPo3e322FJ0nKAcb6WVK76ZYyQd1lWygJzoSqP8SYMSLdxHqP4wnPTI4WvbB8PKPDV30y5y7Tya4RHOBA==", "signatures": [{"sig": "MEQCIEhWSCEPX51HnZbbbeDOODmieg5pJeoCyQ2ryKG/PNxOAiAJHiL2NbFqlvWMG5/FQyzkjS44l6CNz7a0tcB9zcKhrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52409}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c1e5b8a38ef54bb138409f89831942ebf6a7a67e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.6.0", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.6.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.6.0", "jest-regex-util": "^29.4.3", "write-file-atomic": "^4.0.2", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.18", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.6.0", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.6.0_1688484351381_0.7339974962897864", "host": "s3://npm-registry-packages"}}, "29.6.1": {"name": "@jest/transform", "version": "29.6.1", "license": "MIT", "_id": "@jest/transform@29.6.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "acb5606019a197cb99beda3c05404b851f441c92", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.6.1.tgz", "fileCount": 9, "integrity": "sha512-URnTneIU3ZjRSaf906cvf6Hpox3hIeJXRnz3VDSw5/X93gR8ycdfSIEy19FlVx8NFmpN7fe3Gb1xF+NjXaQLWg==", "signatures": [{"sig": "MEUCIQCRjz+vjyZU4KIbHykUZdBKQ5UHnt6ewIpFesEx9V0h5gIgXL2vRFIfyzgCfUMRai2zPy6Jvsu12uXOsBO3wCUARpc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52409}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "1f019afdcdfc54a6664908bb45f343db4e3d0848", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.6.1", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.6.1", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.6.1", "jest-regex-util": "^29.4.3", "write-file-atomic": "^4.0.2", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.18", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^0.7.0", "@jest/test-utils": "^29.6.1", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.6.1_1688653112069_0.99420395587776", "host": "s3://npm-registry-packages"}}, "29.6.2": {"name": "@jest/transform", "version": "29.6.2", "license": "MIT", "_id": "@jest/transform@29.6.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "522901ebbb211af08835bc3bcdf765ab778094e3", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.6.2.tgz", "fileCount": 9, "integrity": "sha512-ZqCqEISr58Ce3U+buNFJYUktLJZOggfyvR+bZMaiV1e8B1SIvJbwZMrYz3gx/KAPn9EXmOmN+uB08yLCjWkQQg==", "signatures": [{"sig": "MEYCIQDUY5jfeaEToe2wxm3iU7dbUWi2G+zI24gHcBwsspUq1QIhAOuBuUM9RpvmZ6QjdcJxfx7GoBPykJl9udUd+VwPESYs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52409}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0fd5b1c37555f485c56a6ad2d6b010a72204f9f6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.6.2", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.6.1", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.6.2", "jest-regex-util": "^29.4.3", "write-file-atomic": "^4.0.2", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.18", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^1.0.0", "@jest/test-utils": "^29.6.2", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.6.2_1690449696302_0.14177814113826792", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "@jest/transform", "version": "29.6.3", "license": "MIT", "_id": "@jest/transform@29.6.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "e8e376f56fffe827b529bf03a9881e58d152c14b", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.6.3.tgz", "fileCount": 9, "integrity": "sha512-dPIc3DsvMZ/S8ut4L2ViCj265mKO0owB0wfzBv2oGzL9pQ+iRvJewHqLBmsGb7XFb5UotWIEtvY5A/lnylaIoQ==", "signatures": [{"sig": "MEYCIQDW61rNfHw4hGUaH9rbvvb6+QTi4lf6FIxYL1mLGXGFrAIhAIEHq37SRkWd09nktwU3PbTOh+zzl1iG4KXo6StQnPBc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52384}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.6.3", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.6.3", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.6.3", "jest-regex-util": "^29.6.3", "write-file-atomic": "^4.0.2", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.18", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^1.0.0", "@jest/test-utils": "^29.6.3", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.6.3_1692621562759_0.7700782005967663", "host": "s3://npm-registry-packages"}}, "29.6.4": {"name": "@jest/transform", "version": "29.6.4", "license": "MIT", "_id": "@jest/transform@29.6.4", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "a6bc799ef597c5d85b2e65a11fd96b6b239bab5a", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.6.4.tgz", "fileCount": 9, "integrity": "sha512-8thgRSiXUqtr/pPGY/OsyHuMjGyhVnWrFAwoxmIemlBuiMyU1WFs0tXoNxzcr4A4uErs/ABre76SGmrr5ab/AA==", "signatures": [{"sig": "MEQCIC7RA3eo2SrJl7lbvrRCwRJpZwLCvBp/xsejAnkei+/dAiBUSFPjFNpGOOc1jEcDyv8MO+Ay5xrlnOS4Z3kXyrCS1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52384}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "55cd6a0aaf6f9178199dfa7af7a00fcaa7c421fd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.13.0/node@v20.5.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.5.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.6.3", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.6.3", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.6.4", "jest-regex-util": "^29.6.3", "write-file-atomic": "^4.0.2", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.18", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^1.0.0", "@jest/test-utils": "^29.6.4", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.6.4_1692875444591_0.6434153403561573", "host": "s3://npm-registry-packages"}}, "29.7.0": {"name": "@jest/transform", "version": "29.7.0", "license": "MIT", "_id": "@jest/transform@29.7.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "df2dd9c346c7d7768b8a06639994640c642e284c", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-29.7.0.tgz", "fileCount": 9, "integrity": "sha512-ok/BTPFzFKVMwO5eOHRrvnBVHdRy9IrsrW1GpMaQ9MCnilNLXQKmAX8s1YXDFaai9xJpac2ySzV0YeRRECr2Vw==", "signatures": [{"sig": "MEUCIQCIlKUoZH9ODG58VdNzxt+vO2tdW+dnuHH1sDT+wastBwIgaWyfxlIaS5+zeW7fpXracSZTaCGn4MIKT7gJ74XHteU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52384}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "^29.7.0", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "^29.6.3", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-regex-util": "^29.6.3", "write-file-atomic": "^4.0.2", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.18", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^1.0.0", "@jest/test-utils": "^29.7.0", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_29.7.0_1694501029060_0.1194333736181512", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "@jest/transform", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "@jest/transform@30.0.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "e6966e496dfc28ddba1d7d17d386e8b7e8ef21f7", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-30.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-8QI5MOyxJ81uvVqTMt8DLQHv5NNUSvvutQODNWitsHJVWO6Kyod9tSVO55Ly/Z/QAL8he6D+LNwPt8vQc4J3eA==", "signatures": [{"sig": "MEQCIGfaLwuO3MB8M0kDqLmL9xci44DdQ1nsqJLYh/BAr/mCAiAE+GhyjT/ZCOXqTIK0tWp/rVmZRkVLvsE8oeaZTjQlkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53192}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "30.0.0-alpha.1", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "30.0.0-alpha.1", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-alpha.1", "jest-regex-util": "30.0.0-alpha.1", "write-file-atomic": "^4.0.2", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.18", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^1.0.0", "@jest/test-utils": "30.0.0-alpha.1", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_30.0.0-alpha.1_1698672797808_0.6148584426825188", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "@jest/transform", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "@jest/transform@30.0.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "6a32d9040fd8edc77a13381727ee23dbc09550e6", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-30.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-emrmGUS461TMgqr+UXzYDfASJwx6Rqbhw1B/2U7rfD+j57Z3nWCu4c80FabGdQUkKZcaJAap6VFxl5qYAPoW5g==", "signatures": [{"sig": "MEUCIQCHGP79K03KL4j/yrkcJmcslZbeik7Ph/qzzEOkFA8CjwIgKWGwLa6/GJZCAVKhvMCTb2O9ku38KAjtLjLYa2kjlYY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53158}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "30.0.0-alpha.2", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "30.0.0-alpha.2", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-alpha.2", "jest-regex-util": "30.0.0-alpha.2", "write-file-atomic": "^5.0.0", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.18", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^1.0.0", "@jest/test-utils": "30.0.0-alpha.2", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_30.0.0-alpha.2_1700126916926_0.5664248725659544", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "@jest/transform", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "@jest/transform@30.0.0-alpha.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "3eaafce664c1b07903582c60edf1c3f257cf33f5", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-30.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-oTjlUPmqCdxqQqKYgBdedHOraGxYe0h83a4gt/zqmDKU+0/rmwI8Cy1bW62ZslP9faUqyv+mU11grA60WJlvlQ==", "signatures": [{"sig": "MEUCIQD9IejyG44W+bOYacGpY1Pk1WpwG7bQIqTCN/o1v6raDgIgSAwCHFZ23bJhTTKq560dhtPaeUWc5pysfg4PvUSeHjg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53197}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "30.0.0-alpha.3", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "30.0.0-alpha.3", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-alpha.3", "jest-regex-util": "30.0.0-alpha.3", "write-file-atomic": "^5.0.0", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.18", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^1.0.0", "@jest/test-utils": "30.0.0-alpha.3", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_30.0.0-alpha.3_1708427358190_0.20476301892102655", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "@jest/transform", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "@jest/transform@30.0.0-alpha.4", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "67f5b8b6b528519782699fcb1fab968d69512d42", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-30.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-wipt89BUw5hEuBzSvOq+4Og0UV5ipDgdHOIxfyLRs6flFvh8afvvaubvHh6He5alOnImVcwrq4zUMO1vJd+OIA==", "signatures": [{"sig": "MEUCIQC2IAWYVhoN0akLXXcq9Lu/J1W/uhahsLT+OkPzc6vFDgIgLXmncdcrwC4+5eAMb9aLKyfLsMI/5HTWyxX9gXGJgKE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53253}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "30.0.0-alpha.4", "micromatch": "^4.0.4", "@babel/core": "^7.11.6", "@jest/types": "30.0.0-alpha.4", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-alpha.4", "jest-regex-util": "30.0.0-alpha.4", "write-file-atomic": "^5.0.0", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.18", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^1.0.0", "@jest/test-utils": "30.0.0-alpha.4", "@types/micromatch": "^4.0.1", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_30.0.0-alpha.4_1715550214517_0.2837711228362578", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "@jest/transform", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "@jest/transform@30.0.0-alpha.5", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "9b3d35350178735c5b2baa50f5300e76a615c3a0", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-30.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-ng0RZBbSVWzkcLscV/orutlgz6WnVA1yUNGruZ6p8QLow628Gndpmd4JrOGRm66v8DFhwtPUkxzucTJ+3VNpkA==", "signatures": [{"sig": "MEYCIQCjQN3Sai5DsgE4Hz9ZnwixwZgKQdrPpljBDrQ0RoMfGAIhAJy1fTeFQn6vUNy1PXh4OLdIPkf6xHwWLANhHWDSJR4u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53253}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "30.0.0-alpha.5", "micromatch": "^4.0.7", "@babel/core": "^7.11.6", "@jest/types": "30.0.0-alpha.5", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-alpha.5", "jest-regex-util": "30.0.0-alpha.5", "write-file-atomic": "^5.0.0", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^6.1.1", "@jridgewell/trace-mapping": "^0.3.18", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^1.0.0", "@jest/test-utils": "30.0.0-alpha.5", "@types/micromatch": "^4.0.7", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_30.0.0-alpha.5_1717073055256_0.13319862771773217", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "@jest/transform", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "@jest/transform@30.0.0-alpha.6", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "dist": {"shasum": "26c4b33082bf465d5f64e9ac39df0002fd763818", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-30.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-4L8BZm38BJASswsqruc4c3F0AExYLvp0xq8067e7fIyg4hfwa4zUA+N2idf+eTTjDWevVVdIBfELzJ8b7nvO4Q==", "signatures": [{"sig": "MEQCICjGK0owfanzjmCyln+2id6Ce9UTIkUfEut9hBASSauWAiAPXJvNeFEEWDtKPPduJk7/9GJ50XqGYEThElJ9ws5O1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53199}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "30.0.0-alpha.6", "micromatch": "^4.0.7", "@babel/core": "^7.11.6", "@jest/types": "30.0.0-alpha.6", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-alpha.6", "jest-regex-util": "30.0.0-alpha.6", "write-file-atomic": "^5.0.0", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^7.0.0", "@jridgewell/trace-mapping": "^0.3.18", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^1.0.0", "@jest/test-utils": "30.0.0-alpha.6", "@types/micromatch": "^4.0.7", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_30.0.0-alpha.6_1723102990686_0.46269159769956736", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "@jest/transform", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "@jest/transform@30.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "ca7eb97bebd7fcd2ab20910e990c47f3020f13ea", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-30.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-uv/gA0QdtnSHJLoQngSzJGXCjTDykJpQFUUuuYN/G9GVPM/aORyNb3ybkkozgjHSV9eC1hJCoQRuraEDJa1pSg==", "signatures": [{"sig": "MEQCIAYY1swOc6dZHkuembztN0UrNYS1gFXLDK7j8F4PpFmpAiBfLMbwGRLhMPxRblolIyXBDzr9AhH6hxbExCW9+lCYhA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53201}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "30.0.0-alpha.7", "micromatch": "^4.0.8", "@babel/core": "^7.11.6", "@jest/types": "30.0.0-alpha.7", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-alpha.7", "jest-regex-util": "30.0.0-alpha.7", "write-file-atomic": "^5.0.0", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^7.0.0", "@jridgewell/trace-mapping": "^0.3.18", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^1.0.0", "@jest/test-utils": "30.0.0-alpha.7", "@types/micromatch": "^4.0.7", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_30.0.0-alpha.7_1738225719138_0.6614923166694266", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.2": {"name": "@jest/transform", "version": "30.0.0-beta.2", "license": "MIT", "_id": "@jest/transform@30.0.0-beta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "1e7f9392e077122eca129f037b5dccb13cda7b1c", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-30.0.0-beta.2.tgz", "fileCount": 5, "integrity": "sha512-6det9//h6w/vSJOrCChbASdsuIjz0WJGHt4ltjvH8bgXkJ4szZm51gPtIKxlZoU8RgFoBtnWGRtFTMx6wqQxww==", "signatures": [{"sig": "MEQCIDJZ+ElwGnB0kLZL614gIeMAe7OF1zvQ6HohqqGRZt/XAiAY/CEVXx7+UX1AO5zuma0JoL7lg2vAYsBG7gg1Y9xkjQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 52940}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "53a5635ac9a43099033f6103e179b13a5465e017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "30.0.0-beta.1", "micromatch": "^4.0.8", "@babel/core": "^7.11.6", "@jest/types": "30.0.0-beta.1", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-beta.2", "jest-regex-util": "30.0.0-beta.1", "write-file-atomic": "^5.0.0", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^7.0.0", "@jridgewell/trace-mapping": "^0.3.18", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^1.0.0", "@jest/test-utils": "30.0.0-beta.2", "@types/micromatch": "^4.0.7", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_30.0.0-beta.2_1748309003105_0.25216000900550406", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.3": {"name": "@jest/transform", "version": "30.0.0-beta.3", "license": "MIT", "_id": "@jest/transform@30.0.0-beta.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "e0a213bf03910faf4c741d31eed1570b41e93974", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-30.0.0-beta.3.tgz", "fileCount": 5, "integrity": "sha512-2gixxaYdRh3MQaRsEenHejw0qBIW72DfwG1q9HPLXpnLkm5TKZlTOvOS33S00PGEoa4UG1Iq9tNHh7fxOJAGwQ==", "signatures": [{"sig": "MEYCIQC3xRPwV4SuEc0zGxSHaA/Wd8oH79D7m3H53LX7/bEy0QIhALRkd8zDbVBBZkQ3VqcME7YC73pZ6tSfW9pjBvjw1r7x", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 52940}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "30.0.0-beta.3", "micromatch": "^4.0.8", "@babel/core": "^7.11.6", "@jest/types": "30.0.0-beta.3", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-beta.3", "jest-regex-util": "30.0.0-beta.3", "write-file-atomic": "^5.0.0", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^7.0.0", "@jridgewell/trace-mapping": "^0.3.18", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^1.0.0", "@jest/test-utils": "30.0.0-beta.3", "@types/micromatch": "^4.0.7", "@types/babel__core": "^7.1.14", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_30.0.0-beta.3_1748309271996_0.13410330122750747", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.4": {"name": "@jest/transform", "version": "30.0.0-beta.4", "license": "MIT", "_id": "@jest/transform@30.0.0-beta.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "0a30fe41bd0a4d95d20ef8d1ae838eb7df7621dd", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-30.0.0-beta.4.tgz", "fileCount": 5, "integrity": "sha512-yTsU3patg0Hw3rT+ZjGnNrOCQtsYebdVzl8joxSZnA3a13fVD/VUQcXaVNLhOP56a8ycVqdHy/MbfZaVw4Qy0w==", "signatures": [{"sig": "MEYCIQDO7VpdWNHrwcY8r+kFLOoU9weM9QSp/I3VInLXUYXPUAIhANSx0Mm5NeW5hmdZcimpp5BvaVoCT9jrr+FZVS/pSDtT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 52940}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "69f0c890c804e6e6b0822adb592cd00372a7c297", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "30.0.0-beta.3", "micromatch": "^4.0.8", "@babel/core": "^7.27.1", "@jest/types": "30.0.0-beta.3", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-beta.4", "jest-regex-util": "30.0.0-beta.3", "write-file-atomic": "^5.0.1", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^7.0.0", "@jridgewell/trace-mapping": "^0.3.18", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^1.0.0", "@jest/test-utils": "30.0.0-beta.4", "@types/micromatch": "^4.0.7", "@types/babel__core": "^7.20.5", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_30.0.0-beta.4_1748329469775_0.7106918702836353", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.5": {"name": "@jest/transform", "version": "30.0.0-beta.5", "license": "MIT", "_id": "@jest/transform@30.0.0-beta.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "cfa00d2047f7509ee53c9dc18e1dd081dcb3a692", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-30.0.0-beta.5.tgz", "fileCount": 5, "integrity": "sha512-3uXm9GLqmYlLqd/81RsRWCYtUVS+CexdioqzxXXoY4IDA7xPmbtXCtbiZr8r6NG1wwfr5/7w/76n2ptWw7sIEQ==", "signatures": [{"sig": "MEQCIHVOwlnr5Em89GPTpK+wPrvZ7CEmeb+9pIGDWm9QbqtQAiA16JBQLNC9U8Y4VQtc00QZglg+m+4KUvLhri/4I7LWMg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 52940}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f2171bb4c6836d74ad2b32a48151d9e0fdfa20a2", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "30.0.0-beta.3", "micromatch": "^4.0.8", "@babel/core": "^7.27.1", "@jest/types": "30.0.0-beta.3", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-beta.5", "jest-regex-util": "30.0.0-beta.3", "write-file-atomic": "^5.0.1", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^7.0.0", "@jridgewell/trace-mapping": "^0.3.18", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^1.0.0", "@jest/test-utils": "30.0.0-beta.5", "@types/micromatch": "^4.0.7", "@types/babel__core": "^7.20.5", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_30.0.0-beta.5_1748478613145_0.10533449282445395", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.6": {"name": "@jest/transform", "version": "30.0.0-beta.6", "license": "MIT", "_id": "@jest/transform@30.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "dcca0f4ecdb9f8c704e15fd0b289ed175734abfb", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-30.0.0-beta.6.tgz", "fileCount": 5, "integrity": "sha512-aU4rmYk4vg2eIs59zHrzlwphDoiF3RNqnkPtOZbNFarL3MEQL1QDSiIZHB0Ppeb3/JZI9QWc1glhTgSM8GPZvA==", "signatures": [{"sig": "MEYCIQCSCd3xoWVVqXM1C6WyK4OaXXuILyg0J4XyeBaTSLk/QAIhALuraYQedPDd0PyQeqUIkTMsZHsfYu8t0IGLoNEjIaBr", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 52962}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4f964497dc21c06ce4d54f1349e299a9f6773d52", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/3.12.3/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.0.0", "slash": "^3.0.0", "pirates": "^4.0.4", "jest-util": "30.0.0-beta.6", "micromatch": "^4.0.8", "@babel/core": "^7.27.1", "@jest/types": "30.0.0-beta.6", "graceful-fs": "^4.2.9", "jest-haste-map": "30.0.0-beta.6", "jest-regex-util": "30.0.0-beta.6", "write-file-atomic": "^5.0.1", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^7.0.0", "@jridgewell/trace-mapping": "^0.3.18", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^1.0.0", "@jest/test-utils": "30.0.0-beta.6", "@types/micromatch": "^4.0.7", "@types/babel__core": "^7.20.5", "@types/graceful-fs": "^4.1.3", "@types/write-file-atomic": "^4.0.0", "@types/convert-source-map": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/transform_30.0.0-beta.6_1748994654511_0.05424125298771676", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.7": {"name": "@jest/transform", "version": "30.0.0-beta.7", "license": "MIT", "_id": "@jest/transform@30.0.0-beta.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "63b8efdd1d95c5593f7df1b7ed4ea7328795dcd2", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-30.0.0-beta.7.tgz", "fileCount": 5, "integrity": "sha512-cyaDVrR/p+sYr65LFyrgUG4vA0Pl8jouHtYR/FWcgxC6DBHyJSOcRB+3qODsa/BXcqhasnMvlMleuPH4hIYMGA==", "signatures": [{"sig": "MEUCIQD1fjJVS01HN0nJTokeGK3IlOCrpaLNIqJo7nxXZ1EsrgIgI2v+MGTkW8dSq6t4voFGFWZbTfZUXrbLH0YeTDXGo6I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 52964}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "48de6a91368727d853d491df16e7d00c1f323676", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.1.2", "slash": "^3.0.0", "pirates": "^4.0.7", "jest-util": "30.0.0-beta.7", "micromatch": "^4.0.8", "@babel/core": "^7.27.4", "@jest/types": "30.0.0-beta.7", "graceful-fs": "^4.2.11", "jest-haste-map": "30.0.0-beta.7", "jest-regex-util": "30.0.0-beta.6", "write-file-atomic": "^5.0.1", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^7.0.0", "@jridgewell/trace-mapping": "^0.3.25", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^1.6.0", "@jest/test-utils": "30.0.0-beta.7", "@types/micromatch": "^4.0.9", "@types/babel__core": "^7.20.5", "@types/graceful-fs": "^4.1.9", "@types/write-file-atomic": "^4.0.3", "@types/convert-source-map": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/transform_30.0.0-beta.7_1749008147741_0.6368852727626404", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.8": {"name": "@jest/transform", "version": "30.0.0-beta.8", "license": "MIT", "_id": "@jest/transform@30.0.0-beta.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "741a82e83f7bed5e4626f6a6b3bda21a34375985", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-30.0.0-beta.8.tgz", "fileCount": 5, "integrity": "sha512-GCllIR++dglYR0vXvMjzaaIG5alhcmLjVdtbI8nwJJ1D7w9n9Ru1Vzr+J3/x3aPD00sdElD8apSKobCejvXl9g==", "signatures": [{"sig": "MEUCIQCgOmFBuzrlzJ4fDs/IeNi36zvQ/1otuSPHKUUVoIXvNQIgKHTOUN5nl3S/CtQ3flLHC6gnp6M13zsjnNRIYV7I5Jc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 52964}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ac334c0cdf04ead9999f0964567d81672d116d42", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.1.2", "slash": "^3.0.0", "pirates": "^4.0.7", "jest-util": "30.0.0-beta.8", "micromatch": "^4.0.8", "@babel/core": "^7.27.4", "@jest/types": "30.0.0-beta.8", "graceful-fs": "^4.2.11", "jest-haste-map": "30.0.0-beta.8", "jest-regex-util": "30.0.0-beta.6", "write-file-atomic": "^5.0.1", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^7.0.0", "@jridgewell/trace-mapping": "^0.3.25", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^1.6.0", "@jest/test-utils": "30.0.0-beta.8", "@types/micromatch": "^4.0.9", "@types/babel__core": "^7.20.5", "@types/graceful-fs": "^4.1.9", "@types/write-file-atomic": "^4.0.3", "@types/convert-source-map": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/transform_30.0.0-beta.8_1749023595780_0.8217289309294422", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-rc.1": {"name": "@jest/transform", "version": "30.0.0-rc.1", "license": "MIT", "_id": "@jest/transform@30.0.0-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "da7ad9cd07d3cf898251a4508f8e87c6b9d6d1e4", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-30.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-/szQ+Qk/DLJ3njTUq6Cus9ZOtYYmCL7DsEVEaXWhG4vmb2dWs21OZMJ4O7XZR+4i0vg1FWV/IUBGUDDAVAlkPw==", "signatures": [{"sig": "MEQCIDMuWLLMG1SLuIO/1DjoewB3Vle9/LxhJ2nAvSHlVB7vAiB1eB9nt2uVbwYVw8zvXKLiNppFjN25d8CPClvmP0XyWw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 52956}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ce14203d9156f830a8e24a6e3e8205f670a72a40", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.1.2", "slash": "^3.0.0", "pirates": "^4.0.7", "jest-util": "30.0.0-rc.1", "micromatch": "^4.0.8", "@babel/core": "^7.27.4", "@jest/types": "30.0.0-beta.8", "graceful-fs": "^4.2.11", "jest-haste-map": "30.0.0-rc.1", "jest-regex-util": "30.0.0-beta.6", "write-file-atomic": "^5.0.1", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^7.0.0", "@jridgewell/trace-mapping": "^0.3.25", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^1.6.0", "@jest/test-utils": "30.0.0-rc.1", "@types/micromatch": "^4.0.9", "@types/babel__core": "^7.20.5", "@types/graceful-fs": "^4.1.9", "@types/write-file-atomic": "^4.0.3", "@types/convert-source-map": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/transform_30.0.0-rc.1_1749430971811_0.005519519980621501", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0": {"name": "@jest/transform", "version": "30.0.0", "license": "MIT", "_id": "@jest/transform@30.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "62702f0d0030c361255b6d84c16fed9b91a1c331", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-30.0.0.tgz", "fileCount": 5, "integrity": "sha512-8xhpsCGYJsUjqpJOgLyMkeOSSlhqggFZEWAnZquBsvATtueoEs7CkMRxOUmJliF3E5x+mXmZ7gEEsHank029Og==", "signatures": [{"sig": "MEQCIHYwVyPl2V5MkzaTjcWopJIVjjgXrjajkvNo5XnL+8K7AiBexjiutPQu6LgMKfT65YpzDHqAoLd5X0jcp/gz7KPNGw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 52920}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.1.2", "slash": "^3.0.0", "pirates": "^4.0.7", "jest-util": "30.0.0", "micromatch": "^4.0.8", "@babel/core": "^7.27.4", "@jest/types": "30.0.0", "graceful-fs": "^4.2.11", "jest-haste-map": "30.0.0", "jest-regex-util": "30.0.0", "write-file-atomic": "^5.0.1", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^7.0.0", "@jridgewell/trace-mapping": "^0.3.25", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^1.6.0", "@jest/test-utils": "30.0.0", "@types/micromatch": "^4.0.9", "@types/babel__core": "^7.20.5", "@types/graceful-fs": "^4.1.9", "@types/write-file-atomic": "^4.0.3", "@types/convert-source-map": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/transform_30.0.0_1749521757153_0.7086169106521676", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.1": {"name": "@jest/transform", "version": "30.0.1", "license": "MIT", "_id": "@jest/transform@30.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "c6d4da0c26f03d7ede148216cc61933849aaae7f", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-30.0.1.tgz", "fileCount": 5, "integrity": "sha512-BXZJPGD56+bwIq8EM0X6VqtM+/W4NCMBOxTe4MtfpPVyoZ+rIs6thzdem853vav2jQzpXDsyKir3DRQS5mS9Rw==", "signatures": [{"sig": "MEQCIBhJGlGDBoU1fdPVZ06V9l9wffR5WKkmPBp1H8M7hTaQAiB3up0blCfTrdjJ4xnc4AndPRKYSphN4RAOInK89J628g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 52915}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-transform"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"chalk": "^4.1.2", "slash": "^3.0.0", "pirates": "^4.0.7", "jest-util": "30.0.1", "micromatch": "^4.0.8", "@babel/core": "^7.27.4", "@jest/types": "30.0.1", "graceful-fs": "^4.2.11", "jest-haste-map": "30.0.1", "jest-regex-util": "30.0.1", "write-file-atomic": "^5.0.1", "convert-source-map": "^2.0.0", "babel-plugin-istanbul": "^7.0.0", "@jridgewell/trace-mapping": "^0.3.25", "fast-json-stable-stringify": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"dedent": "^1.6.0", "@jest/test-utils": "30.0.1", "@types/micromatch": "^4.0.9", "@types/babel__core": "^7.20.5", "@types/graceful-fs": "^4.1.9", "@types/write-file-atomic": "^4.0.3", "@types/convert-source-map": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/transform_30.0.1_1750285894832_0.6248396162991141", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.2": {"name": "@jest/transform", "version": "30.0.2", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-transform"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@babel/core": "^7.27.4", "@jest/types": "30.0.1", "@jridgewell/trace-mapping": "^0.3.25", "babel-plugin-istanbul": "^7.0.0", "chalk": "^4.1.2", "convert-source-map": "^2.0.0", "fast-json-stable-stringify": "^2.1.0", "graceful-fs": "^4.2.11", "jest-haste-map": "30.0.2", "jest-regex-util": "30.0.1", "jest-util": "30.0.2", "micromatch": "^4.0.8", "pirates": "^4.0.7", "slash": "^3.0.0", "write-file-atomic": "^5.0.1"}, "devDependencies": {"@jest/test-utils": "30.0.2", "@types/babel__core": "^7.20.5", "@types/convert-source-map": "^2.0.3", "@types/graceful-fs": "^4.1.9", "@types/micromatch": "^4.0.9", "@types/write-file-atomic": "^4.0.3", "dedent": "^1.6.0"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "393acbfac31f64bb38dff23c89224797caded83c", "_nodeVersion": "24.2.0", "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "_id": "@jest/transform@30.0.2", "dist": {"integrity": "sha512-kJIuhLMTxRF7sc0gPzPtCDib/V9KwW3I2U25b+lYCYMVqHHSrcZopS8J8H+znx9yixuFv+Iozl8raLt/4MoxrA==", "shasum": "62ba84fcc2389ab751e7ec923958c9b1163d90c3", "tarball": "https://registry.npmjs.org/@jest/transform/-/transform-30.0.2.tgz", "fileCount": 5, "unpackedSize": 52915, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCICGbjNyGYBvlSDn9xLZj0MvvCIXfSTZ9uyg7U3xrRH2vAiAEAZbUsXnqP1uZ8CaCv6KY2bmzccSADGfc0N56sU0CCg=="}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>", "actor": {"name": "cpojer", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/transform_30.0.2_1750329984889_0.21020176331907403"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-03-05T15:04:16.549Z", "modified": "2025-06-19T10:46:25.320Z", "24.2.0-alpha.0": "2019-03-05T15:04:16.879Z", "24.3.0": "2019-03-07T13:00:13.716Z", "24.3.1": "2019-03-07T23:12:34.749Z", "24.4.0": "2019-03-11T14:58:04.479Z", "24.5.0": "2019-03-12T16:37:13.794Z", "24.6.0": "2019-04-01T22:26:56.512Z", "24.7.0": "2019-04-03T03:55:49.679Z", "24.7.1": "2019-04-04T01:19:06.326Z", "24.8.0": "2019-05-05T02:02:47.922Z", "24.9.0": "2019-08-16T06:22:46.745Z", "25.0.0": "2019-08-22T03:24:25.356Z", "25.1.0": "2020-01-22T01:00:04.726Z", "25.2.0-alpha.86": "2020-03-25T17:16:33.605Z", "25.2.0": "2020-03-25T17:58:25.802Z", "25.2.1-alpha.1": "2020-03-26T07:54:29.978Z", "25.2.1-alpha.2": "2020-03-26T08:10:36.369Z", "25.2.1": "2020-03-26T09:01:22.671Z", "25.2.3": "2020-03-26T20:24:58.315Z", "25.2.4": "2020-03-29T19:38:33.064Z", "25.2.6": "2020-04-02T10:29:30.376Z", "25.3.0": "2020-04-08T13:21:25.133Z", "25.4.0": "2020-04-19T21:50:34.653Z", "25.5.0": "2020-04-28T19:45:35.956Z", "25.5.1": "2020-04-29T10:55:11.036Z", "26.0.0-alpha.0": "2020-05-02T12:13:16.100Z", "26.0.0-alpha.1": "2020-05-03T18:48:13.673Z", "26.0.0-alpha.2": "2020-05-04T16:05:36.254Z", "26.0.0": "2020-05-04T17:53:19.726Z", "26.0.1-alpha.0": "2020-05-04T22:16:04.069Z", "26.0.1": "2020-05-05T10:40:59.204Z", "26.1.0": "2020-06-23T15:15:24.703Z", "26.2.0": "2020-07-30T10:11:54.772Z", "26.2.1": "2020-07-30T11:35:38.728Z", "26.2.2": "2020-07-31T10:53:56.120Z", "26.3.0": "2020-08-10T11:31:59.898Z", "26.5.0": "2020-10-05T09:28:28.270Z", "26.5.2": "2020-10-06T10:52:56.594Z", "26.6.0": "2020-10-19T11:58:50.925Z", "26.6.1": "2020-10-23T09:06:48.056Z", "26.6.2": "2020-11-02T12:51:36.859Z", "27.0.0-next.0": "2020-12-05T17:25:29.085Z", "27.0.0-next.1": "2020-12-07T12:43:29.930Z", "27.0.0-next.2": "2020-12-07T14:35:52.210Z", "27.0.0-next.3": "2021-02-18T22:10:02.998Z", "27.0.0-next.4": "2021-03-08T13:45:19.876Z", "27.0.0-next.5": "2021-03-15T13:03:28.673Z", "27.0.0-next.6": "2021-03-25T19:40:05.063Z", "27.0.0-next.7": "2021-04-02T13:48:01.912Z", "27.0.0-next.8": "2021-04-12T22:42:36.798Z", "27.0.0-next.9": "2021-05-04T06:25:12.569Z", "27.0.0-next.10": "2021-05-20T14:11:27.019Z", "27.0.0-next.11": "2021-05-20T22:28:53.781Z", "27.0.0": "2021-05-25T08:15:13.996Z", "27.0.1": "2021-05-25T10:06:37.384Z", "27.0.2": "2021-05-29T12:07:27.481Z", "27.0.5": "2021-06-22T11:10:46.146Z", "27.0.6": "2021-06-28T17:05:48.548Z", "27.1.0": "2021-08-27T09:59:45.250Z", "27.1.1": "2021-09-08T10:12:17.122Z", "27.2.0": "2021-09-13T08:06:44.029Z", "27.2.1": "2021-09-20T13:28:02.382Z", "27.2.2": "2021-09-25T13:35:10.495Z", "27.2.3": "2021-09-28T10:11:24.060Z", "27.2.4": "2021-09-29T14:04:50.077Z", "27.2.5": "2021-10-08T13:39:23.490Z", "27.3.0": "2021-10-17T18:34:48.533Z", "27.3.1": "2021-10-19T06:57:34.746Z", "27.4.0": "2021-11-29T13:37:23.933Z", "27.4.1": "2021-11-30T08:37:14.433Z", "27.4.2": "2021-11-30T11:53:51.558Z", "27.4.4": "2021-12-10T04:43:12.912Z", "27.4.5": "2021-12-13T19:36:43.346Z", "27.4.6": "2022-01-04T23:03:39.909Z", "27.5.0": "2022-02-05T09:59:29.033Z", "27.5.1": "2022-02-08T10:52:23.775Z", "28.0.0-alpha.0": "2022-02-10T18:17:37.411Z", "28.0.0-alpha.1": "2022-02-15T21:27:08.251Z", "28.0.0-alpha.2": "2022-02-16T18:12:09.869Z", "28.0.0-alpha.3": "2022-02-17T15:42:26.002Z", "28.0.0-alpha.4": "2022-02-22T12:13:58.810Z", "28.0.0-alpha.5": "2022-02-24T20:57:22.198Z", "28.0.0-alpha.6": "2022-03-01T08:32:26.494Z", "28.0.0-alpha.7": "2022-03-06T10:02:43.970Z", "28.0.0-alpha.8": "2022-04-05T14:59:54.784Z", "28.0.0-alpha.9": "2022-04-19T10:59:17.368Z", "28.0.0-alpha.10": "2022-04-20T07:37:31.343Z", "28.0.0-alpha.11": "2022-04-20T13:31:00.755Z", "28.0.0": "2022-04-25T12:08:13.536Z", "28.0.1": "2022-04-26T10:02:42.123Z", "28.0.2": "2022-04-27T07:44:05.529Z", "28.0.3": "2022-04-29T10:44:24.892Z", "28.1.0": "2022-05-06T10:48:57.105Z", "28.1.1": "2022-06-07T06:09:39.348Z", "28.1.2": "2022-06-29T10:33:57.084Z", "28.1.3": "2022-07-13T14:12:31.566Z", "29.0.0-alpha.0": "2022-07-17T22:07:10.837Z", "29.0.0-alpha.1": "2022-08-04T08:23:31.801Z", "29.0.0-alpha.3": "2022-08-07T13:41:42.088Z", "29.0.0-alpha.4": "2022-08-08T13:05:40.925Z", "29.0.0-alpha.5": "2022-08-11T13:41:00.206Z", "29.0.0-alpha.6": "2022-08-19T13:57:57.518Z", "29.0.0": "2022-08-25T12:33:34.234Z", "29.0.1": "2022-08-26T13:34:47.634Z", "29.0.2": "2022-09-03T10:48:25.454Z", "29.0.3": "2022-09-10T14:41:50.842Z", "29.1.0": "2022-09-28T07:37:46.908Z", "29.1.2": "2022-09-30T07:22:53.244Z", "29.2.0": "2022-10-14T09:14:01.691Z", "29.2.1": "2022-10-18T16:00:17.651Z", "29.2.2": "2022-10-24T20:24:06.280Z", "29.3.0": "2022-11-07T17:55:49.992Z", "29.3.1": "2022-11-08T22:56:27.097Z", "29.4.0": "2023-01-24T10:55:58.192Z", "29.4.1": "2023-01-26T15:08:41.190Z", "29.4.2": "2023-02-07T13:45:39.091Z", "29.4.3": "2023-02-15T11:57:29.843Z", "29.5.0": "2023-03-06T13:33:38.186Z", "29.6.0": "2023-07-04T15:25:51.575Z", "29.6.1": "2023-07-06T14:18:32.226Z", "29.6.2": "2023-07-27T09:21:36.522Z", "29.6.3": "2023-08-21T12:39:23.020Z", "29.6.4": "2023-08-24T11:10:44.741Z", "29.7.0": "2023-09-12T06:43:49.325Z", "30.0.0-alpha.1": "2023-10-30T13:33:18.061Z", "30.0.0-alpha.2": "2023-11-16T09:28:37.200Z", "30.0.0-alpha.3": "2024-02-20T11:09:18.360Z", "30.0.0-alpha.4": "2024-05-12T21:43:34.696Z", "30.0.0-alpha.5": "2024-05-30T12:44:15.393Z", "30.0.0-alpha.6": "2024-08-08T07:43:10.840Z", "30.0.0-alpha.7": "2025-01-30T08:28:39.423Z", "30.0.0-beta.2": "2025-05-27T01:23:23.335Z", "30.0.0-beta.3": "2025-05-27T01:27:52.169Z", "30.0.0-beta.4": "2025-05-27T07:04:29.959Z", "30.0.0-beta.5": "2025-05-29T00:30:13.315Z", "30.0.0-beta.6": "2025-06-03T23:50:54.697Z", "30.0.0-beta.7": "2025-06-04T03:35:47.911Z", "30.0.0-beta.8": "2025-06-04T07:53:15.993Z", "30.0.0-rc.1": "2025-06-09T01:02:52.003Z", "30.0.0": "2025-06-10T02:15:57.370Z", "30.0.1": "2025-06-18T22:31:34.984Z", "30.0.2": "2025-06-19T10:46:25.044Z"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-transform"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}