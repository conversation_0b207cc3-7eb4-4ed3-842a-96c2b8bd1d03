{"_id": "@babel/code-frame", "_rev": "132-3b2bb70efc767ac8641c18d053328688", "name": "@babel/code-frame", "dist-tags": {"esm": "7.21.4-esm.4", "next": "8.0.0-beta.0", "latest": "7.27.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/code-frame", "version": "7.0.0-beta.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "5d0267cbbe79c474ddb125308307107e2d73f691", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.4.tgz", "integrity": "sha512-31sstUlOkWSomLaqAxh1HnCGQLqSK+Wl7vHPfN79QvBHMqHMg5xywPFx+ENZVWISoKqcV+LZtwYBW4jNR89aTA==", "signatures": [{"sig": "MEQCIBAP2o1tm0qP10EwkxpHhCeCriP53dTbjYpl3gEUNMN1AiAhZAvq4Bu+WG72BwdA9SF7wpj1mDbJhwr7tNmJrlgo5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "5.5.1", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"chalk": "^2.0.0", "esutils": "^2.0.2", "js-tokens": "^3.0.0"}, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame-7.0.0-beta.4.tgz_1509388445131_0.9286881070584059", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/code-frame", "version": "7.0.0-beta.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "f719d8988503c7c286b40edd66bda0e093f9e47f", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.5.tgz", "integrity": "sha512-ihHHVMQSyaiMkwouM+xP//SR1xSqGOQkcVsXlu7IFUx1mE1BmFVwb9Q/tDrZbMb+h48lTGIiD02/g7y0iSuP8A==", "signatures": [{"sig": "MEUCIQDs7ncw1UqhcO5AFW4CTxv63K/eA5H+u9/SlQyCW6F0OQIgZYcxfND7HZTJjPPYz3m+RkaNuA8jfDOkJG0ZGDvY1Hs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "5.5.1", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"chalk": "^2.0.0", "esutils": "^2.0.2", "js-tokens": "^3.0.0"}, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame-7.0.0-beta.5.tgz_1509396952920_0.2039596908725798", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/code-frame", "version": "7.0.0-beta.31", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "473d021ecc573a2cce1c07d5b509d5215f46ba35", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.31.tgz", "integrity": "sha512-yd7CkUughvHQoEahQqcMdrZw6o/6PwUxiRkfZuVDVHCDe77mysD/suoNyk5mK6phTnRW1kyIbPHyCJgxw++LXg==", "signatures": [{"sig": "MEQCIEf31ojIuh7/TI2uUcXB1lxBhn3hyKc1aQRbOCYZZY9dAiBVCsGr9/ENWXDoJxgEkmP2NohA/L2kr3wqIrk58uZmlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "5.5.1", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"chalk": "^2.0.0", "esutils": "^2.0.2", "js-tokens": "^3.0.0"}, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame-7.0.0-beta.31.tgz_1509739386570_0.3593930322676897", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/code-frame", "version": "7.0.0-beta.32", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "04f231b8ec70370df830d9926ce0f5add074ec4c", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.32.tgz", "integrity": "sha512-EVq4T1a2GviKiQ75OfxNrGPPhJyXzg9jjORuuwhloZbFdrhT4FHa73sv9OFWBwX7rl2b6bxBVmfxrBQYWYz9tA==", "signatures": [{"sig": "MEQCIAjLhULkTeJiMdP7BqaE5Sa0LyDKMEMZ+NyEnAI7Q4InAiBiN2bSNdfDGVS5Vi3pWQMrSojYoZkiVFU7rghrSWahuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "5.5.1", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"chalk": "^2.0.0", "esutils": "^2.0.2", "js-tokens": "^3.0.0"}, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame-7.0.0-beta.32.tgz_1510493576052_0.5319963879883289", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/code-frame", "version": "7.0.0-beta.33", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "8932bc4fcd94f0cb861f5eac46bcd05444e53b23", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.33.tgz", "integrity": "sha512-q4jupg6G3lSO4luAE3fpxzTtf8Z5hfDKMEKo1Qx6PaOx9PaxxY9sElN3gfQtLwPaLnGbhIDuKcSBWPFLiHBXLw==", "signatures": [{"sig": "MEUCIQDdNBmo04GoejfL9QduOUAmlN+LMZ1H2L2sm3eLz2+P7wIgN8DvBegW0oggVEKyDEcVtDD00qj01nD/4+ptft9wGv4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "5.5.1", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"chalk": "^2.0.0", "esutils": "^2.0.2", "js-tokens": "^3.0.0"}, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame-7.0.0-beta.33.tgz_1512137822798_0.09434121893718839", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/code-frame", "version": "7.0.0-beta.34", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e8e81e37ee65a35cedc8a22a11a51d792bdc651c", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.34.tgz", "integrity": "sha512-CAEMoiuya1pvissuqLntIRkVFmFA54mvovKvdHmsdWETut9qJ+bASY8fT7yxSwHCwhAHj2Ed6Als87BSaS7nhg==", "signatures": [{"sig": "MEQCIBCUWQEwEQFuLIkMoOXjKmGapZ+9An/ePl2BK8e9TM6DAiALWCus2KCHvKqySNbPt9ODELv2p06cHTJGmyqradZNSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "5.5.1", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"chalk": "^2.0.0", "esutils": "^2.0.2", "js-tokens": "^3.0.0"}, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame-7.0.0-beta.34.tgz_1512225536239_0.720601559849456", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/code-frame", "version": "7.0.0-beta.35", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "04eeb6dca7efef8f65776a4c214157303b85ad51", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.35.tgz", "integrity": "sha512-l0SE8cl9DUIY4hYAFAKTLX3F2Yr14Qri7uTsuI7iegB5E4KyQy4XY72L3VOxmj6kwR/RDQURoKYr2NzyETGo7A==", "signatures": [{"sig": "MEYCIQCXwp9lMStGi9mIHla3liQ1t4gOwdoF/FRNtivX4cQungIhAMj1Kz5vg8fajdIRjoKbZqSUAm4fuKtQhaV7ROsAAASO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "5.5.1", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"chalk": "^2.0.0", "esutils": "^2.0.2", "js-tokens": "^3.0.0"}, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame-7.0.0-beta.35.tgz_1513288049768_0.0350339999422431", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/code-frame", "version": "7.0.0-beta.36", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "2349d7ec04b3a06945ae173280ef8579b63728e4", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.36.tgz", "integrity": "sha512-sW77BFwJ48YvQp3Gzz5xtAUiXuYOL2aMJKDwiaY3OcvdqBFurtYfOpSa4QrNyDxmOGRFSYzUpabU2m9QrlWE7w==", "signatures": [{"sig": "MEUCIQDxPJ43wxorw062PhEVwSD/0GvI3TcRgJKYyYYmG0gLrAIgKphTTCoDq6o3RBfAy8YzyKBBzw4N6mgw202rXSHQgyE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "5.5.1", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"chalk": "^2.0.0", "esutils": "^2.0.2", "js-tokens": "^3.0.0"}, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame-7.0.0-beta.36.tgz_1514228652104_0.12947122124023736", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/code-frame", "version": "7.0.0-beta.37", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "2da1dd3b1b57bfdea777ddc378df7cd12fe40171", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.37.tgz", "integrity": "sha512-LIpcKm+2otOOvOvhCbD6wkNYi8aUwHk73uWR+hxBdW2EFht5D0QX89n4me8nyeNGWr5zC3Pvmjq+9MvUof+jkg==", "signatures": [{"sig": "MEQCIBdGXR8GN0iTMDBvgqNMM9Uiy1jmzAIU/UA2F5hA0OnRAiAOT8RQ83kGAgmyE3/R0InsLZkEW1fGF2r81lCqFcVGUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "5.5.1", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"chalk": "^2.0.0", "esutils": "^2.0.2", "js-tokens": "^3.0.0"}, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame-7.0.0-beta.37.tgz_1515427336882_0.7782960846088827", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/code-frame", "version": "7.0.0-beta.38", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "c0af5930617e55e050336838e3a3670983b0b2b2", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.38.tgz", "integrity": "sha512-JNHofQND7Iiuy3f6RXSillN1uBe87DAp+1ktsBfSxfL3xWeGFyJC9jH5zu2zs7eqVGp2qXWvJZFiJIwOYnaCQw==", "signatures": [{"sig": "MEUCIDx/+ks598DbjhFwbD5qTOOALtnlEpEppJilHGsLoUiTAiEAiodiFwPwQxNsmjQay8ipdntzittVDlkd8rtkggq4SDk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "5.5.1", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"chalk": "^2.0.0", "esutils": "^2.0.2", "js-tokens": "^3.0.0"}, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame-7.0.0-beta.38.tgz_1516206695142_0.581886108033359", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/code-frame", "version": "7.0.0-beta.39", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "91c90bb65207fc5a55128cb54956ded39e850457", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.39.tgz", "integrity": "sha512-PConL+YIK9BgNUWWC2q4fbltj1g475TofpNVNivSypcAAKElfpSS1cv7MrpLYRG8TzZvwcVu9M30hLA/WAp1HQ==", "signatures": [{"sig": "MEYCIQDKehQ+rAu+hi0EvCsO1jBHJ4tXVYXLDw4lASi5rb+WnAIhAJnd8WfVo+sp/9F8tCoM6PUA4nFQ8+BaIXArbMAnM6sH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"chalk": "^2.0.0", "esutils": "^2.0.2", "js-tokens": "^3.0.0"}, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame-7.0.0-beta.39.tgz_1517344042108_0.45995172555558383", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/code-frame", "version": "7.0.0-beta.40", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "37e2b0cf7c56026b4b21d3927cadf81adec32ac6", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-eVXQSbu/RimU6OKcK2/gDJVTFcxXJI4sHbIqw2mhwMZeQ2as/8AhS9DGkEDoHMBBNJZ5B0US63lF56x+KDcxiA==", "signatures": [{"sig": "MEUCIQDIjUtZjitrCvxLO2QXopBRqzc4cDiU89arbgQlbO4XIAIgLhK/ykSgVp4gbpqxW2Xv1/KYWNGtedfUFXRNRQDabqA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8737}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"@babel/highlight": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-beta.40_1518453713791_0.6272798336555065", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/code-frame", "version": "7.0.0-beta.41", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "68845c10a895050ab643e869100bbcf294b64e09", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-omQT0n9EW38xfMCY7cCW/PAT55igUX9c9cMq6QN4EjxCcelcrocwMJ6H4JP4BGrQ+OHdJAQPM9/Eaa2Yce4Aug==", "signatures": [{"sig": "MEUCIAzYps/cRnv7RTfKe8jK0CQCsYYxVltLZgSJSeLj6fbFAiEAtYakve1+db47LC/8LtGhah4S03lNr+9p4sehOrwb5yk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8783}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/highlight": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-beta.41_1521044742088_0.6899831164127788", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/code-frame", "version": "7.0.0-beta.42", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "a9c83233fa7cd06b39dc77adbb908616ff4f1962", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-L8i94FLSyaLQpRfDo/qqSm8Ndb44zMtXParXo0MebJICG1zoCCL4+GkzUOlB4BNTRSXXQdb3feam/qw7bKPipQ==", "signatures": [{"sig": "MEQCIDJIQZ49V1oVEUtTZSGU0/ryjJguqtibh1kK+8WGI6S2AiA72rPFcdDCVwpW+qBi65A9M4bxP1CCsFIVueKzxOgoAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8783}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/highlight": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-beta.42_1521147016473_0.312479768463541", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/code-frame", "version": "7.0.0-beta.43", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "57281887da181b4f9a3e72151f54f3237bf011eb", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-6Ch<PERSON>OwmetdKGH9JSppRlZuS/CLKDByTfOs5zxQMCShz3Rc3sNwPkxkdgkY4J/H8rEIxdvbh6/m6JpKGUwPvPZA==", "signatures": [{"sig": "MEUCIQC4QGDejyZtFH+5mm0WGrji3AlsKAQtCPlZQIzsK+ZMwAIgf4uPTTxXJdR9OrasU4lu3NJcz3k2NdWlgEW28dGlGeU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8559}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/highlight": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-beta.43_1522687686632_0.6634529426015716", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/code-frame", "version": "7.0.0-beta.44", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "2a02643368de80916162be70865c97774f3adbd9", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-cuAuTTIQ9RqcFRJ/Y8PvTh+paepNcaGxwQwjIDRWPXmzzyAeCO4KqS9ikMvq0MCbRk6GlYKwfzStrcP3/jSL8g==", "signatures": [{"sig": "MEUCIQDj0IfDWlqCcs/Y9O5ql0rLhf8nAJAnoY51btp4UWyJ2AIgbyGrFufTklxATXmBOTJgPC0CMNmqdTj/disp1Y0G2SI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8927}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/highlight": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-beta.44_1522707589102_0.9830233251030578", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/code-frame", "version": "7.0.0-beta.45", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "69f53a95d0b323ce0169622f1cc3d68671bf86c4", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-zS0DxlIcLM6lA7I02INRLjIK8xOOlt8hYVTlDOns+fWZ5KZ1wnl54AnrHG1byHDU6MnZgi5X3lCBl0ZyDT6+pA==", "signatures": [{"sig": "MEQCIDHtdxZoJOEc82zkQy4u9r3DWE0N91Et/41qqgzUTl4eAiACjgrIluDskPB0Gz0Ak4TY0wiT4WDTnUMVDjJDTVyGlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8927, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T0fCRA9TVsSAnZWagAA2cAP/12DdlTE0sTH6liuwci+\nb8iilZ3T1pJlH3D9sSyDP9BmrCpb9HC8z6JJlUs30B5aKZrfwvhHKH+yijwP\nEVR4T4F0KEe2TeToFF0vPUK0d9Cgyf37SQzXyFixEanEJNgLAOO8N2m0Sz76\nnqZcVfQnzexm9QIQWsWvGzsok7R6AGdLg946mpvi29DtjPeIxfBJZqtBp5ou\nWaL+7r1ndxiTTIhrh1AE2jIWhIvOkZUU2AO2BliYWfzj1+SnXld11tPQswRB\nEgq+in5KLsNFrR8/K8jsU3/TOruLa7GEgDYO5NxmxG1sp6OiIPLj80yPd5pw\nB79rt5TpQ9C6ip5zmWStpUY9OepJ3/UQCmj1ezfMJt7mePoQMMKVuk0Y6qBP\nt8Mict3W6xYKbn9Qc3MKuNEie8KvE8b8IVCA2vow680ZoiwIPT3Th66BRAMp\nCd6Zig0sIE2RtzCR+DnfxfOwe3XxRBaJwbP2cmibTOWiVDsFSjmUQJVaHWKW\nu6w907dLq5Mz45v+gKLnbwfEXaSysZW32X+51N5+C66uPRbV7JoBrCh30Xp6\n14gOYb4OBt5eWPzGoCWMF5nsr5QMU2uOmtzp2kxFXftGRX10geCIuHZa8sgO\n6EAc/GWaDH9SqEGl071M88F6OsUNrpeYr3hW2KgDH13kJ9RmKMz+UNa9SrK4\nHeHq\r\n=d/4C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/highlight": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-beta.45_1524448543053_0.25793894670032924", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/code-frame", "version": "7.0.0-beta.46", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e0d002100805daab1461c0fcb32a07e304f3a4f4", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-7BKRkmYaPZm3Yff5HGZJKCz7RqZ5jUjknsXT6Gz5YKG23J3uq9hAj0epncCB0rlqmnZ8Q+UUpQB2tCR5mT37vw==", "signatures": [{"sig": "MEUCIBA2wuBjZxrjBbrPSAW2GaQTA84pUek+B9NJPKNWtL9OAiEAwfmVD3WHEDsPxbSpiH6OAJcnGjrYQ1uZB84V9CyGFgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8927, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WFQCRA9TVsSAnZWagAAXoUP/jir9ehBKkLq+E2iKqdg\n5cKIPA5/qZ/izhd4JUffuV9HHICYs6c/7PjpbhK0gB0ckIhWa3b6B+63c62t\ngDzgg3766Ep1S7lXDn3HM+NIUM5HP2XDAu4Hzecz3bTeAGgbowJ8fZGyayqm\nLNAYUjkVl8NE4Q5SRP5SYQaUGk3mQ0l9+70SBwoCOAunWqB2gXxY3fiVhDxQ\nmFRUWOQeCQ2riKO7PjwqsCGW14laLwbovepsfTCXGSB9Cl/d9jovLQG/5J1U\nTQze6HPMGxOebw8EmVn9trlrnfkON37ZLVPNznUlFlLp01Ebsps40bw3OHli\nVG0uf82/Idj3bK95E783XofoeDbpd2yX+3JDy+nMnb38nu9FaQarA/BKVbpk\n9H3fFhAXNFWcTa+QFQCbbqkFd+024Uyz6ragt7fEU+nNffI1Wtijvi6nPVH3\nkWyGe8hZ0Z31qF0IPfR8gtTHNDTz2dIsTPMRQbacmPj2rfoeaRJsvUe3D75T\n5KfZwiQLPEqZjJjxkb4OSIaF7Wb3ZYbm6APzNiA6CSYo5DxRXFKf/PbpRQBd\nK0HcsIYbbgS+STnuhqWuCG5tWFpb+S2gBLAjwFrmMOp4uax3asqxOTwUv7RI\nl34FdgnhSFUN7xjdv2B2WS5vKIyElVQRwdfMJ5RY6s3R/sEFhQtpkgK06Ecc\nQeOl\r\n=WlcJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/highlight": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-beta.46_1524457808340_0.7717767131144242", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/code-frame", "version": "7.0.0-beta.47", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "d18c2f4c4ba8d093a2bcfab5616593bfe2441a27", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-W7IeG4MoVf4oUvWfHUx9VG9if3E0xSUDf1urrnNYtC2ow1dz2ptvQ6YsJfyVXDuPTFXz66jkHhzMW7a5Eld7TA==", "signatures": [{"sig": "MEUCIAKEDdXyBYL/yZsboGaZTf5xI1P66C1IjIhg+jygrxmoAiEA239wp41UfcGuzakSwhormM+gyCtzKHNbTbJL06BDJWc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8856, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iTGCRA9TVsSAnZWagAAVVUP/268NZ5kqJmDunAaDRKV\nv9pilxRbdzvzWcrgzWLyunhf/ajN6vt795RG7VN/YuufvAxXPxx2crLZCMxQ\nuPndJFpuyIAlaeXvk6fKOHCcwwFb8kvtgHFhyzesSgm81lQz75DyKUhN6kJo\ntglbD14qWtqqovt1/EeGvplxeiCQkaAyonWAA/3pxR1okl13Hpxw6KbsR0Kg\nsduIys/JzaMhVbZZrMfR6fSoEQfh2ZlvoNUo4yAZkR2+rZJ9gCJ5guYHEl/m\nVLfaaXs2S/vaTua+laQo6JPrkGgdXSF4Bh+ER/m2seq/PAbyNacrZRE3lMui\n9rGc21CtPwGhSgLFbTXL68js+DQj4DRKofELLBli9x0GvXqnnsQtcFA4AzG4\nIPr5uARWaqpgcA8+Ap4mAmJJhQfonP6hskg7Z7opDvhkt5IoZbD91qq9HxWe\n58+tMiOT7Ww2GNJZ0aobqfT0uHYPEpdHRHCMGWnG21toqJ7+3H5+TRAN7qDs\nKJLnPNWL38dSTcZUoCoNSjqlxvqSUJ/PudH85E1XeBsz3V26FLKt1fNS7Ras\nzQo1RPx+rq77RzWXujMS/n5xND3OAeKf0RKyG2s6DFiZ8ceVssrfXNVtxUEX\njB6rn69fGC19tgvQTqYz2SitCmhfdiX6V6LAUPt/N9pkL1pIDQCRLcYkFNb4\n56qG\r\n=7rqv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/highlight": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-beta.47_1526342854456_0.16272006759578184", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/code-frame", "version": "7.0.0-beta.48", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "ff1c11060a7c1206e0b81e95286cfc2ca3ac405f", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-zwyam2DrinaJfhmCaajZT2yRCoN3cE8dwW7uVWaHay9z2Dwn9Pidnekkz9g7kXbg2qfVqmgDKeUTVZ4+bMlfZA==", "signatures": [{"sig": "MEUCIQDVOU2c0qfbPSfo1iWFaPdac6i2UeBMT8ln0KSOzLDwWAIgX9javCi3piek/p7BaNmHUZLfA9Bqg9gvcJqVZZ/wow4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8555, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxCXCRA9TVsSAnZWagAAebYP/jWJ5A+glEEj+IScjDwB\nK5DlMtKmGnBEfPzVufz2yrI4Vwi0osNiSpYr7qAQp7yXVbos7v8D51IlQArF\nHzWtqGl0eVx0ivhFBTrzj5eElDsCYv7jWA1AZ6Fz8hRBUf4p9zrrWBvh181g\n8ySqMdqCtWdkyd+Cqe7wqft2LZ2yUDCfvILCJrKG35u4HHsMQndHPA969/uf\nqiLYm9o+rLIfbAKjBa0mj+cWgIHJIJxJfDIkj8VCO2tciJT/WO5l8hxQjjTI\nHnFV568Wg7g2jqU0Pz2AZtk9SFSVsk6UNHFFD4nvB0WbHFmvEeKeLZGVhYJq\nwvG8ymgp91cpD9/QMXsUa520cY7CLXhRt4sX5V+ErpbKfQ2GUIq8K0MfuX6G\nPA/A1lGt32cQlBON9Ob9MmN2QIr5j7BBJoziX+MyJmrveO4CKqlf3X8KQnka\nUBdZyDD30MRWzgVOiaygkMN4mq+kRhr10/LvobHpZEOGcifcrMxJA+EHGrpM\nTbnac2fs/ZGtlquwrXNXiGLKugW9fBzholY44JyZZhHsl/ch7TrjvLQyVkTK\n7OgVePvX/LCuv5BQvMSWNJ6t5oVxPtyvLNRaDmSWQDVQ+EMGQgUt20GFWiEc\nAYG1qdClfXyTYpUsw6Q2rHcIqxciQqSuumyP6rwYtUqxsLmSuw2qBmqAmjKM\n5YAt\r\n=0AVW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/highlight": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-beta.48_1527189654739_0.9844284926097895", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/code-frame", "version": "7.0.0-beta.49", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "becd805482734440c9d137e46d77340e64d7f51b", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-DTiu55HotSTi33qt+mm3zPXc70JH/NQJVDvVlOXnRsziD+Mw51O9TgzXDVvjYyq4Lm+Np2ozTGlVCY1b8Uj2KA==", "signatures": [{"sig": "MEUCID7w6Yv74JhRkkqew/4JgsrmdPzTwu5A8BvoUYNWNjzKAiEAgZAThUEc1HZvPWPhjECVNNBltm0X2KWrkXY7dDREevI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDMmCRA9TVsSAnZWagAA3LwQAIDsbt1malDBqibHIvj5\nUmJtmFUZa/mtdzsPEFLQat9m8srQp2Ofa5TzPfMtzleuhiPWWDVuRahSjbsG\n4VdllCp4J5tKXnJuSHysdCcyEG9sGCae4b89RrmwGBOmajUQSHkzEeQkKYW7\njhQDetKcLDqvSJy3SgiWUlSQjygIq9295NcCZBFUclpdN6bf77GseSMguBuK\nbqmUiukEmPqyEbh8mbP+QqX8B+lUZNwOgxS0GPptsoFuxbqfhzS/Ethhn099\npXoyMd9p9JA+mOm7iV9w80CJh27PKky2a/t72ymetiJaQDpPH2IO03xYHnKm\nOSWlJPm0S8dmHAvis+wV18A0hQPCRcI7M5DPi6vrDNB7KIbjQY+E9RLY/pYj\njiy0XGjQLiSBlaWxVn26Mzg3XAWTUcyYBf7IRZVegIy200MFVL6feYRRPted\no/aSLY+6CpqasfPtBVg0ZQLDqmCyNQKeJlaM/Ux+feD5hakxoq3G5+sJZoBi\nQlSLEjDYZ4m2YEzmvrT03dfTWFqMhbsZrPfeGwEfpW3zjKhb0eYmKGiUSu2f\neDu/fHlzNQJhlaDpwkXPV7uzWN2yXibTcC52Vrzhb/r68ol5pM7+yZjPXFZC\newt+D8UYizbNzq2BDGieSeQZCmpcQ4hVKTkDW1CHR2doyX9B0kVP5Tchar3K\nmbwm\r\n=zFDY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "becd805482734440c9d137e46d77340e64d7f51b", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "3.10.10", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/highlight": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-beta.49_1527264037123_0.5793528821634604", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/code-frame", "version": "7.0.0-beta.50", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "45dcddf66dd003c511019af4ceffde91e8121772", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-9ozsN/qk+1/mkEngs5ucU9BCS2rUqxvIxnugM0AFapUG93KmP4hSdSXZQCTBis4/gd3zyvi1cpbV5myZOc/rQQ==", "signatures": [{"sig": "MEQCIGP8RzXZzksmP8U+Qi7r9uBnsMVKw2Sd/8Nu18TEAGx0AiAAzyhxavP1E0Ty5l0WODdIoiln1XUAeVX+6ptNKWFveQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6064}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-beta.50_1528832809327_0.9772915864795306", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/code-frame", "version": "7.0.0-beta.51", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "bd71d9b192af978df915829d39d4094456439a0c", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-G/2/3uqI4xzQl0japbzp3y4tXg3SKOwSBO3+IosDpozqxMdAlsd83dlp+Sf7XiHQir6OKF0+L2XkKoNoLS05vw==", "signatures": [{"sig": "MEUCIBhY5fKlNMHzavk2AXx36LDokN8Z1Zs82XgfKzrfTU/EAiEAmh9AqSR/gZRI4nC/dY+5nsCWkOUtYcD6ehDeH9deLMA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6064}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-beta.51_1528838355835_0.45347976380128086", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/code-frame", "version": "7.0.0-beta.52", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "192483bfa0d1e467c101571c21029ccb74af2801", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-3fGm8tQRcKmfv/GVqW71TCwRxdbCWYhqdlZcXte1xRQpk8oSkFLQjKYyVTEpu8EGaECvR8YvvtO8ojBFKkbFhA==", "signatures": [{"sig": "MEUCIQCmG/5rSbMJ8g5CgyJ3IiXFsqK2thkBLKdeGAThND6gngIgJfhKqdHD7DPdVsZPj5b0GUL7CQygTVknSkUpqp9h39E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6063}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-beta.52_1530838751878_0.17452087340516265", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/code-frame", "version": "7.0.0-beta.53", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "980d1560b863575bf5a377925037e0132ef5921e", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-6o6EnDfG+zQqfrYDLPc5kGp6+klZFFFqGucljRcUa7IZuTBpvALWG0O+7rtOGFF1sYhr4jBib995RvFuNFxDMw==", "signatures": [{"sig": "MEYCIQDIi8H0QxH1nRgTHD7MJuuNIL5AK1IqGXnKfYNj5c8+CAIhAOA3traXn98hvnCKTg9nEDJ5CKBg8DaqgbvgExLCk9EP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6063}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-beta.53_1531316401331_0.11657329284349993", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/code-frame", "version": "7.0.0-beta.54", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "0024f96fdf7028a21d68e273afd4e953214a1ead", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-bXOKRNPL6n+9C3GSGuWKpvLDOhXL9CtdGnVpTHx/dJpb2tKMXSJaray5NHoXHD5h4tv2DnPMrAAa+XN096/K+A==", "signatures": [{"sig": "MEUCIQDUWdbA2MeocROPvQ7CsiYS41fOUECcU4yc5keXe1SghAIgRHBBUuERpHQmqqqCkecL4u2bQDG0SelRIPhsdEeCiWw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6063}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-beta.54_1531763991664_0.9734289774245615", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/code-frame", "version": "7.0.0-beta.55", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "71f530e7b010af5eb7a7df7752f78921dd57e9ee", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-DE//AcvjgTr5X66NXw3eksEumR1cGjLNF4cCbva51W+bwEHANEoov+WF1g1YmKDkaWmCqekIQnTRtkN7gmzIqQ==", "signatures": [{"sig": "MEUCIQCnbmk1uue3DtHqbn8X0EAFL/AweEgkpQMOMlZvr24rkQIgJgE2R+jjd2vmCqcmVx8SJMm025fmUmInKG6D9gtVBRA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6063}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-beta.55_1532815616105_0.19993891170635703", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/code-frame", "version": "7.0.0-beta.56", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "09f76300673ac085d3b90e02aafa0ffc2c96846a", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-OBeGs8UXWpKl0oK2T5nUXNl2yu8RKxqL/7aUnMtKDXCU6VUrNP3npdrPivBA11HPB15TVI49nWf2lntTzoUuAg==", "signatures": [{"sig": "MEYCIQCkjSJfWKk1jNxMOpBx83j1TkBdYYi+bkwHI9+wIXIVLwIhALQDlKTmAeLCT3DLptvjNyPpvNd9sZJ8LYuYZVtaNcyk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6063, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPtjCRA9TVsSAnZWagAAPScP/2wNIb07hCJdbaIyNjh2\nECOiOk6bfIQzGNlTW6U/bivnqq8/ByEx0VQ5WqMaGZ3gDGXwMUacbrjW772Z\n9n75FzaYLrllzYrC91A5b94RMx1MGXZ7flwzS77w6zc82NPnBZBGZFzXkec9\n2m3PQEI6wE0vPtTIN/yWryo1r3G5D8EZF/Jpb1rM3EbPFYsepPgZzZRnuGoM\nByDSAVLM0hGlpx4IoIBeOuW4Cqh7qcKqq+NjCI+6QWvy1WQBHQpSvt4rrrux\n6iP0xbtklW1NFC03HcPX8Fy7wMRBNyAi+tt/5hp3jRScnDu7cxLKSu9+g5hb\ndTKpz5/xdTsxZmQRUwYSWRtj1sNdlsnzs7f1+sc1m0BRSccUrwNTibTywxa2\nP2vVGuW9a0JFvbwQmdM8fqOzZ+7E3YWYCmtP/jC29WbFWePkTjZvBKZOhhr9\nHOR6oYP9dYBnqKF9PJuezZR2bJ+vi3Z7gE0t0lPukLAmJDSW6NduPEjt49cR\n01W5zE2YKayCeUPRWSVa9M/QHkRUDRhah+ydZ7gScw8puD/s2h87c1WCzZMu\nYNR3jzIJdsUTSFhJd+gWD/bTwf+Uwb4f3dVuy9DeOwH/y0mhLFT9EHlurdUx\n+Ib6mckDEdMoJHe/7hK4ByLthefOrwSnUEbWn3e6rcDADeYuwO9aF8PGs5/e\n3o+o\r\n=3M6k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-beta.56_1533344610756_0.747754469440072", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/code-frame", "version": "7.0.0-rc.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "6732d13c3e303d1dd2e45a23a8cf7c8196062c86", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-P8FKeGAeK+GbQ/D+1IwGSLb3Qrfw+uE/KuTQdFz1Rop/+k8Yp+YlBV2rQcSxKaKU/ZfGK0SNtzC5XagDx5/2Ng==", "signatures": [{"sig": "MEUCIQDJQfuAK9k7Y3ATOLLdDvJ8HMYij0GkTtP0vFV0p0oHFQIgMNhsX1aGmrmY0ge5qGgnSBmeBLQabNLUnx1R/wd3gaM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6057, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGRBCRA9TVsSAnZWagAANS0P/imqO7/C3bHYIXkpyEk6\nsaP7OZ5BAxhkaiRKuHKTxk6rL9fyv5eATFV/A+JjC9vZplVOPcelejrO4UhD\n+BY2dlCsl0hPNf+N8y+GSoB2Wb/sEg4OcaME64nFlabtvzeagE9xLegXdT06\njsOwqBJ2yYN4TfU539tXOyiHSlISp4IUmlHrHgCFkCjo1tG8jPx9xHu8LCBJ\nV0Pn2rNSmwsHkzbaCrEbWBS6hfs0X1+UyUWIv+auEpqTFKuPXmHNhjIXWhUX\ndYWOTtYFAbrK4UYAXzc4mtymvMhIeSj3+MGp9jS+MYmdbqaNg6kOgpvrGrm8\nEM84dzfL+lBIKgZG7sVgtgI7yxaBrIdFFRpENDW5JsICPeGRxt+viiJsDG89\nw+q8wI8BugtPOMfMkhFNcmrhVMDSVvRvs5Toi2DFUpiLQPSAHtMlbiRUz1JB\nJ/A1UtEGmzq6eI4Bu2Q2xErBcaz58nxBQ/ETZNZw7EYxtopoD8dkpxNL5ZLv\nqUrxAzkmUpsD2waCHOXNHXU+RzHbQuW6zIL22b4IJ6fg5SdEvRBieAljO0qN\nk9audUXlG3/FNA/ZiaujNPJRt8nqxSVyNg1ozKl0hlJRNhw7nfVsI6Px3Q08\n3eJRT2i1ybdyo8E3XeuPzsNWgZ3Gw4btqHBnbc1uelsfKqwzSOl9ys8HD8gK\nrlB9\r\n=AMkr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-rc.0_1533830208854_0.4801452888222868", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/code-frame", "version": "7.0.0-rc.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "5c2154415d6c09959a71845ef519d11157e95d10", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-qhQo3GqwqMUv03SxxjcEkWtlkEDvFYrBKbJUn4Dtd9amC2cLkJ3me4iYUVSBbVXWbfbVRalEeVBHzX4aQYKnBg==", "signatures": [{"sig": "MEUCIDboTwoIT7Wxk78XTEuHlSL7bBjfEHK2fyG52rULapC5AiEAtO5HDldTIu51Y6YC3is15vpXWx/R+O9UBJyOIQwCd0E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6057, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ7RCRA9TVsSAnZWagAAMhUP/3Tn3dBRzDCZfNK84syS\nV74+lRccE2BR8PU8BnJXTRZMBJg2zUbMnLpn/ZAicTUHR832y2+OJSPRN1b9\n2V2qOI+SCmtGy8HpsSDYRveR/gY5THsHQQAJnsYKLaXBZlAEN1Bb234lN2Ly\n8JnQtoqUTEFI4CaAcE7ks6zygPil/AOcG37awhAJaILKwFtGLgrMy3CsCU9p\nhZYGlr6/PEtyC4K6w8fh+B4j2wGm6y0ve7gognWLByyoi9sxS6UyZE5KPLcU\nsnDTNhidxkL8Me6voooGp5Hcmd9Gj0JE3yrFrLbPqSErhrxrzPv79yhfPp2v\nnwfwtSnBAgDo98qe6O4GOql79tdDdyLxOVZfPmEJRxlxkHIe3p389OD0spv8\nfiN1wt+CiaXH5SCOy+mfYbhlveuNbi6kstsr0f4yekFV/q260apN7kKWABMn\nMH56RrTE4CcCydbcKjGp62oCQq+aJnaCOWKXPDNtycD96Jj6Hck7ayqq+jab\nmRDdJgjpVvMtLP/UFkarAgS/9royJGUZb2J0RtV9MIZpEeVyeXazzozPpsHL\nMhmfj3XqtFy5Q9Zi0vp8DHqEzkZ6CH3d3onG4elsVcszWc/CrVF7c8dpFqD3\nTQOTkPLN9cbcZRFp1Uup8u994bAyYLA2DKEowSy6IXAWEaxQlyAakoVJ46Y8\nsZUy\r\n=6Ezv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-rc.1_1533845199572_0.1229856328585186", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/code-frame", "version": "7.0.0-rc.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "12b6daeb408238360744649d16c0e9fa7ab3859e", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-+cVix+HBNakVp7IU1WReJV8dnJl/yaBA5JRXc758BSrvJCH2hKp1Z0xHIiUaOvxMwKXc3EXGIYhlnx5T+6ofGA==", "signatures": [{"sig": "MEUCIQD23d0k9Yxhta1jVXNwIZqmHXwD10nGDYVqNqqjRqjZ4gIgU3G9cFukDMmjT1K0jyNIb7xOMvu3+ImIuoF5vd7vfNM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6057, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGaDCRA9TVsSAnZWagAA5ZkP/1WHzMFBk/KM7cDmK20B\n9xdjKVBo70pawvRwf7S36fJZSo4dGIa5a1SL1aqVA4a+O8Yd8jjKctPkWi5a\nChAkFKbKb+WKvolQwIsdKsH2QblbCo9og5Yr1GV0BXd7NecJUPKvLm1MXlUI\nI0DurOd8W9cb2UEpPIMfTVnJQP1zj3ouBxZTTWS/lkgi4/0mNX9rjKcOshrH\nkOG4YqMIWySHp4bdpMDKz8AmrnajKUtCFGtkwQK85ZlI4pyliz2axoxzsOiQ\nM2oEm8ysFKNF/rWHQX/N7PX0kAs76uW/K2D3nNonOMpqmekheoU4H67Ivo77\no343TVm5+U3J6YJqDMJ35jI7AU8mw9L8AUkfTfr2kLDfAVxCpzz24peTzCZd\nwJ5n3g79GFKuu7aUakj4hgqWSreI7TCUAXqNh/bnSDw4A2w/Gf7gjEkwkW2Q\nuEQobeKgsxljvb0SLqETDxtE275fgI6g4O4SSq3dDn26qufFhXOwTjnl7RFH\nshocLiFyEb0NMm0U7wGB7Z9pTRXJLqy+KKjqH87ncxe0QSbPJJeagIf1uqon\n1DpdFfIAk+GmSakuyidfneIkv73BBgbKPtQChxD59wjiKCtIsS4xQ9kyQsKU\nh1Qwk+jrHu418BSq1zbw8AFwKWohG+kDMVggxrPeBxRW5Z8y0Aoak248a5pr\n9CwR\r\n=xiaJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-rc.2_1534879362811_0.6501065164422171", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/code-frame", "version": "7.0.0-rc.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "d77a587401f818a3168700f596e41cd6905947b2", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-vH+ONMtvkQpjvKAXl5shNFyIpBwmkgKjo+buySLpQsMNDlqbJcFIMiYhwDrK4isZsae+QeHJYbqUJ0BYwyKNZw==", "signatures": [{"sig": "MEUCIQDwpNSaDVSJhOTc3ZiG5bCQc9Bx2IcxxxPX1ujKh/6CYQIgJUb4bZa932EIGqRz04zIO8UpHXdmX8XYdesAIT2b5Sc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7156, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEkvCRA9TVsSAnZWagAA3bgP/RYhsEqidvJVClTe4H42\nYyyRYIkIV3FjOR3h+zIPRsgussYcD2tOOYYdqUPKKB1OUj629qrhsx8+jK0k\nA9C8dPoeYBlgn/hLq1ndXSshbWBJQA6/Ut+doMAFUZBVcliEqBnNBgGGDNlv\nSvHOtk/xrjQ0tGgVLMQxjLMFIjUsvh96p1NofAvJLGlyJd4rANa57t0rGbIi\nW6RYCzp5HwnzTlF9nO1C3DBeLkDDqk9BEPkrNS5n08U1AU2a+hT5wKdmiJfn\nuMF1jO3CyEiVJUBtNXYr/NDHNisOXdou52S8+76YUkv3WuqD8MmR/l9K1PI3\nEgv5oSFNRx8GIBjKbeHFv7OT4+vZdOlXrZn0Hhsogdnvj3yly4KS6fdfBqeG\nZ2744HfaKNBr3niREklkxri2rCsBYxoCC+Kd3LwDOCeDjSud8NuwTuu31Bm5\nRft6SjTFgVPsMXV+t2NCM/mfQMq0je/bWlWgLgNbmANxnRPnEaqvv1pVJXOG\nYq7/85hncfItDDIaYqUYJgerDHSBLrVjKeJeDfMPzkaofJ2qouQXNAB5MPDA\nZW7PNDw0SgTlIvxw1UynJT5a6smB6lNhWq77lKR8dmr7/FlXpb/O45STXBzr\nt6cwOvtUwfWrFm8ZNaEEW8PZtzwD60bDGWVB/+GzD3ig1jvBnhVxop6KPs2s\n3FC0\r\n=KM3a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/highlight": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-rc.3_1535133998931_0.11203309242640414", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/code-frame", "version": "7.0.0-rc.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e15e3ba1193bbb8d474c244446bffd8c6f804556", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-Uh3Rhu4J1gsvZFPEKP1+F530GKWz0t92gDYPZ7Y7Q0dgohLt9WdREEYiu2JzlzlBDtJVPVnTYz/oWVUMxntuMA==", "signatures": [{"sig": "MEQCIBM82H/k5D67Uv7MBhZU7kagn7lKIlhvwj5ox9Aka6lYAiA+nhVIVtHrO7yRfky1kOPcjFYStG9wHlTR0Hu9nQiirw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7157, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCoJCRA9TVsSAnZWagAA4Z8P/2cA264Q7ln1RqTFoRwQ\ng45B4WTpEFoU5pAJ2w+i5u50fhW92uyHgpvDSrg3ChJebrfZGUtvDTTDqd78\n2QwmsJ9ycmWLuAFzy/W4NaV2kg0c3+p60xZNMtoNIaH9TY4DqhQZWqYQmhaa\nqtwyjfADrigBSrz4fhhXwz2+soEFHcTFdNNw/dHdQaCRRTdp+1tPfJoR6Ua7\nMINNAXoXcURh+ViLMklwHZ4RktNqlbKm19HQg9XOyHaXgFWtG+g1sVx+Tigd\n8EMHoQfE0slS/ZPTb7K178yVo+pvqSQ0jtvzGtIJ966gfXEpKTnL/kcOSjQh\ntmMkHjJ43ouYDI/2iX6mbi6NyLXNKBsJZjt22IWnLZlkRYHwa/Q4JUJkJka8\nXtdyhalUSiN2ha+4gGLpM1J8QzkbgKPlmpvGt6kHmP/W164PshBHH00Sa8Lg\nKxNHsdJC9DZnOg9tuiOehCCHfcdcrQNdizKVXcTjsWmZSVNHqP5HE9BAYgpx\nu+JjUfRKJg+lNQMRfjb6vk4UgmcDgWMj2jBtVXC/EuRmBhbMp28e9R7k+i1J\nb+EyBMPAFXTFqzlnPfhLeVaHEsCNySaL25y9pEtKgiNIfHvUF4Yvt9AgCk6Q\n7mDLPB0ekWASRDMSa8YXsfN4QZhSJWiivJEjnhV0E2u/DOSee69X8TlSdQif\nk7G9\r\n=kVjq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/highlight": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0-rc.4_1535388169331_0.21601484765349488", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/code-frame", "version": "7.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "06e2ab19bdb535385559aabb5ba59729482800f8", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-OfC2uemaknXr87bdLUkWog7nYuliM9Ij5HUcajsVcMCpQrcLmtxRbVFTIqmcSkSeYRBFBRxs2FiUqFJDLdiebA==", "signatures": [{"sig": "MEQCIGUbwiak9getJ8v76oW5adjrEi9ud1YcgflmyPLE9kLwAiA1nG5njkwgT3b2FeTGxvE1qi2REMyoOSJ12sb4saA/3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7147, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHAoCRA9TVsSAnZWagAATeoQAIrMn4alJ+K22lIVKcmk\nhOGPFksHQXxPAaN3Yr8vAVwABGptGGOu9LWG7AypdfmONgC0AOPLZYowSQ8c\n8GX1bPprjHSxhDzrajBAZGjOMxx5DUpgoX4Q84pSd18lJ2Pv2Qbt0GYUqV3C\nbckqRnxUoKIumE8jlAbGBfygcOISptI9uwfMS83DYNJBA5fRuFPAYOyzs2W2\nYQKDLLTsmvAVXu+neUZrqd5Atw6xvWDG4ZAn546ANzRzG+9KpNZHMU6YcPuY\n3ddlWawQ1byVLqJ3vlqC0bkz0DEnMS4y5/h55bhwd0aTKF+OZQDmuin4lGMN\nFlJ7OSFAJcTKjj20zKUEuEZ4bHVDkXMdhcFLUDLzwnCjN3xsnT4xdXCrNwaB\nisDu4gB/AuoX16UtXK76zyC8MCB/Z4Tk+kWXtLfMRYwRpuHbnvKpZkgPiyt+\nscgSy3knNwGuh2BkYAABTC5bSiIvdOJcm4jsm/sEEtJpw1wasEFWMxiKcdzx\nxucYPa/B9noZBN/GoSkjeU1HcFF9+99BTK0dFX+nxIVZjWf4PsqVRGwcrT+p\n83+s03ixGKtsNStmYehVpOZaRZvh/woAysegvpMlS6pfGLJJT8RTGzcRVLMQ\n16FvK1E6Sj+QrKZ4JTXuR2AbRM7mW5f5N9l4dQDi3H8imezNdtG2ZGZn6w9A\niQHV\r\n=Rg24\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/highlight": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.0.0_1535406120240_0.9711164960643741", "host": "s3://npm-registry-packages"}}, "7.5.5": {"name": "@babel/code-frame", "version": "7.5.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.5.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "bc0782f6d69f7b7d49531219699b988f669a8f9d", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.5.5.tgz", "fileCount": 4, "integrity": "sha512-27d4lZoomVyo51VegxI20xZPuSHusqbQag/ztrBC7wegWoQ1nLREPVSKSW8byhTlzTKyNE4ifaTA6lCp7JjpFw==", "signatures": [{"sig": "MEYCIQDmvDfN/qK4vnpaHvVFE67PFADZyACncRzzSseyZd2yBAIhAN6CBuS8+2fsR3nV4lXiqpEXRNVrdVztCcsQH3NwP/4x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7312, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdL5FXCRA9TVsSAnZWagAAF7wP/RXhso8+0gUvcJZhZ6vt\nSrXimIBf82u7nbBEU6DhWqhVDcSGUext1Jy+U2g1eVEiCbJTb2VGcPNa5gKm\nagXXrbRJiTCZaUEQaIURGHA7+Z78UkJ2G0EA2WqSlO7ttX4pMpNYNKDZsrNz\n47kROLsb40fLtpeolfEJe82XxfmCvDej1Y4xVQ+vKaadDZ4JRnGLry4pPDkS\nWTW1StiOksrggTDiZo90LwbA5jOWm+FBgT7vohSDUhlf7aOaIirQ3EljOOYM\nr7RT7HDkVhpKAEtigJYwYZnOl4eJjWjTiFQ12w90itRC5xR6rFSpxVkQhowu\nZmu9Z2vWJiDv9gPpsrUtfMMnv96C/pWKlj+tZDXGqr44QPP+VPA+iuq1BR1V\n4DLN9jKrAEJkig6R1xqtWxyN6tkUARrk/0jZA2Ep26oy+DDa0ybTfWt5wMcq\nU6wGq8qJZe0buZbiGQokRufxkybMCgrg7F7dR79Hmq7PFpqbJyneGJ+26qna\nZI2G/aYNDudT2eJNcUTkAjUsBz+howa4C01tFsukNfM9DKH6DPfrYLe4VKnn\n30qBEvM1/8Gj9b4LaP+f6rLP1rSogivq+IH8ALQpzOfg41PHxczqik8veMl4\nB8MeIfrErTluUSJxMiTaPj34fvfaOyGgmSoHrGTURIYF7+Yw6lGpkWrKaHsN\n4o/B\r\n=BBzr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "0407f034f09381b95e9cabefbf6b176c76485a43", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "lerna/3.15.0/node@v11.14.0+x64 (linux)", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"@babel/highlight": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.5.5_1563398486466_0.4030239254922583", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/code-frame", "version": "7.8.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "8c98d4ac29d6f80f28127b1bc50970a72086c5ac", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-AN2IR/wCUYsM+PdErq6Bp3RFTXl8W0p9Nmymm7zkpsCmh+r/YYcckaCGpU8Q/mEKmST19kkGRaG42A/jxOWwBA==", "signatures": [{"sig": "MEUCIGAFKkfNZHgs5aV2o46S+OKUwKbygdSndJEmrucfQhQzAiEA+wc0qW2B4raPbJjB37gDG2uR/iBbN+lBboEQywfH/Zo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVECRA9TVsSAnZWagAAl9UQAJZq86FBaNU83NEc626D\nCwpskagFmg09W4GHqBDBAiQwC/BDAXcyZdic1tgCJ9+z2T8596xfx0GsX5cW\n1fHrWM4KbnyOxR7Fat2VHamuTdY2SCygxPdBqDpM3cBjI8oZMaxDIouCIOsg\nxekmGNK8IqH+ppgY0j4l9UvUK8hS7uv7o/NzBqmoDf/Iw3riWRvQA4NQL4WG\n3IkuxDrpPAOOXX7wC+uEqsg2wEnWoTb9MjQLQUMwCzJpAx8ou6tEEVMIkLYs\nCTthgpKkUSRbG+SWu0l2NpUH3ISKxdYbx0raXEczuci+6autyIcBZRkN9TFX\n0azLhiDByFq6C+oshK3XwPGceaPtnBe1c8ttakamQFezdtW/NuVk7tBDm6lD\nQ8kvryKeaLinDeidYwZ2sphYAfgnQL/2oNivCI1aoUiIrS15pPCykDcfWzjf\nbGCKAxpmAaaMkMYiP+log5H6ILH/nywo3jlWdmDzwq1agKMDwrFygeqlwgGJ\nkhFR7MCz9czlmmku05iCQ2T9BwVe2HQ9HLH3uJPDwbKYwnfdo03I70UA7VSX\n39UguofdbKDrN8pMAwXd1znWyysri6k4ZmeUzgGa/Wwmn7vnoIFojLS5n6E6\nYJKVmYdPAT+xFIfa/yhDUZBkDZNmi235vpeccxWjym/OHDrbRKDCSHG1nYnk\n/SQz\r\n=NR/i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/highlight": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.8.0_1578788164533_0.13718188358234062", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/code-frame", "version": "7.8.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "33e25903d7481181534e12ec0a25f16b6fcf419e", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-a9gxpmdXtZEInkCSHUJDLHZVBgb1QS0jhss4cPP93EW7s+uC5bikET2twEF3KV+7rDblJcmNvTR7VJejqd2C2g==", "signatures": [{"sig": "MEUCIQCVbG3yOgXkpgyBJr4HLGbikkr/vmu9jeOBIyPLZNbzXQIgBAXTkOKVJMgYUl1+WmBw1qMM+vpTRfr+1NSfVifSWXE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQXCRA9TVsSAnZWagAAf24P/23VFmaeJNZnfGJLhZlh\nbXLxQP6+3dFDD3M0kwuUIugo1hwbyQNX1NcEvK1np4b5FHk6HeXq6wgAE8/x\nxJcaLCquudyYLtPS+Dfp/BgV2t/+gFDFU1YXmEmADhhKdbq/81e5Ihj/u7qd\nccbHQfxg+5q1QGcIRrrB5QCk8ormUHrCgstxk5p2BnEzVsBkkXtK/lL/xL+I\n1EVMYex4vZsICUNOWZdfJ1v0TqYaHP0jVpOpLs3852OAXEhl2baznXn+AnWF\n9coiAb3j7KXSwBGKKfXNljcuEm9aL5IW1Nmfv4dWcMdCYAiGU9s1z3vHIzos\njUEWxjXM90fNXU4qsE3g6ZiXf7YdO+kT3L740QiAD0X6LwtDOT5dZ6nOT8Lm\nq/nQKwNnKWSBmOmPadHBL77eze7N66OP5cXJvMeZ+xrG0g/zfsJcGNYT4UXE\nCq0YRn5+pT7PVkKwIEZO1oDwy0XKp4gZHLGzoxKHk6KAMKBoe4pzZjMB4YAz\nqqosqOQwoq+SNGSvbZAyb0IyxPYyT8B17J2NK0o9t09xVQSD3Ou4Au0gzfqm\nvNlUULoHz7178RmE4g97xVBUiBU+N7AShSEi8ellPKhOFmp21gGT1r4OLpk4\ng39F0ELa0Sy731kE41VaFFRbMkkaq8a8m5Q/S23VE7RmhaO1840QiH2MJ6H9\nf1rl\r\n=5KQB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-code-frame", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/highlight": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.8.3_1578951703334_0.16275252224361303", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/code-frame", "version": "7.10.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "d5481c5095daa1c57e16e54c6f9198443afb49ff", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-IGhtTmpjGbYzcEDOw7DcQtbQSXcG9ftmAXtWTu9V936vDye4xjjekktFAtgZsWpzTj/X01jocB46mTywm/4SZw==", "signatures": [{"sig": "MEQCIDMSV3U/E1f2X5Krfa9Z0uvGS8D0sZ9Oo7LdCBOUpgtYAiAxWmFee6cXiZSK4HeKmzQvbWEhD2vplwMD/zbFB27AKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSuCRA9TVsSAnZWagAAzoUP/1EKWLuLYIsPmOTI0h5c\nK2sXQ8gAySKn6sACbfT1wHm2QMnF5aw3JIPftVG04LN19Vj27uvSgUIlGiWH\nnMiNN5zD+ETfM6m7qogaquoE3TGgt3mT/kM+9xyExaMthHHd4hYqI7onhxez\nWQDDEABR+ptc2QWp5u6oReSQc/AVM8zDR1dyat0h90TBObyuI+lLDv0JpxrV\nAINq633MrQn5qAtrhPmt7iHC6HNNplWAkk/SERsr4nxzAK/UIMWWu9g2UwAX\nKU/VdoseJ539D7zmB1HxBS4MoHArHD/m6QTsW/7SI7X0XkzIaS3Tv8lca2Wa\nKGEAiJP1KHj/h62MLdujG7M1jbWzSWW0TDVRVtHfGXCcAQQf3e3u20NkPpeN\nyKZSF9769W/jFtErSA+dqeSKJmumaxELw1jDnVw7RrbmpWOfuK7JXPubFoqp\nLfDJ8+EzRgMDkJ0GfB/05MEtqRarFL3FZbkquTk0b2JfRvO4SnidK0XCxOlk\nE/5jtAUMqsrY1DEf+VY9dQNBDCrZZKfdZxoyIOpRvhLF/a6bLrhhZgQ6pHZH\nLDuz/NPZeO0Go37mA4gbcUA5LXIbPUg0s0j9up22BkP8JZY3yzSCfuVyAtYk\n9Plo2R+YHK2tcURvikaTbtz/8OgxH+dN4nGj8Y9S6JT5z7FMHsiPtHCz0ylg\nFNPE\r\n=BmN5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/highlight": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.10.1_1590617262286_0.9342618695808165", "host": "s3://npm-registry-packages"}}, "7.10.3": {"name": "@babel/code-frame", "version": "7.10.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.10.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "324bcfd8d35cd3d47dae18cde63d752086435e9a", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.10.3.tgz", "fileCount": 4, "integrity": "sha512-fDx9eNW0qz0WkUeqL6tXEXzVlPh6Y5aCDEZesl0xBGA8ndRukX91Uk44ZqnkECp01NAZUdCAl+aiQNGi0k88Eg==", "signatures": [{"sig": "MEUCIQD8MRFBUc9oqr8MlLCubkg6H8IgUeU6ZDSXBueZO/GBVAIgSqn1vc1224DmAh3YWemfZ9AxsoQC31YNu/wgfsE7pic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7SYBCRA9TVsSAnZWagAA3mwP/2RDEznW1hEddpiqBiB7\nL0Klgwa9iKW3aZhSvcOOisvn9uwntohleTTp22dIqHj4ypQZxlCtfrRb42uT\nQC9AOm+ajyJsmX++QWwd2Tx5m37gabNrnDMXd0X8SywbB9dkMnl0xtnfFbL2\nSrvLT/pptknxRve2maNw0F3oIHX2A6mBciDsUtw13pd8saoov2Ok6cHP576+\nmQZn8jlp8Jw+qVGFNr4Og0qQWwDQIuErHAFd0LZ1RQxra5V5WSeCSphc5b7p\nJLI7xRmVSkbp8Jxgv4iDaEIddgEmhzIO86r1Cp7CSWvJZok+RA40KjotMuOV\niCZhKnBQ2KNosh7CN8M/oiAoc/jks2LRagtHRa7Ek4tSVancX006ZzO96Odm\nGxiuwhPta7cesIzuYLazoGnf2mBGlTuY9AP+escTxnxQL+dZhZChkW7iPeiX\nmHT1U8xP6dUWIh1XNlvD3/srWQNFC4IlkdXUz25KYIGaxCJQ+4j8UrHCwho/\nucbPOnGwebkqc9eCwjH+XdxnXivItTcntiKz0EDxVSV02Wxt2OT5OR2wjL90\nbQ0BmdMWgj2euCmp3m2BJykCnKFsasZcITqv1NRUYlvyHkjwwx6fKrJg5+Id\nf59pz3gKxfy4TslD0Ir4hy9pNPPv8SEOsuu/X6yx0B2N1dC/eD8QFA7Yljn2\np8/A\r\n=066+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2787ee2f967b6d8e1121fca00a8d578d75449a53", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/highlight": "^7.10.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.10.3_1592600065483_0.8310540295503257", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/code-frame", "version": "7.10.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/code-frame@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "168da1a36e90da68ae8d49c0f1b48c7c6249213a", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-vG6SvB6oYEhvgisZNFRmRCUkLz11c7rp+tbNTynGqc6mS1d5ATd/sGyV6W0KZZnXRKMTzZDRgQT3Ou9jhpAfUg==", "signatures": [{"sig": "MEUCIQDSjBYOah3mnIxnAjEKv638MySMCxhZ0J9pexriecmdogIgAOaTRZg3UeVxrs8Khzr78+n4T+10hMn2Z9DRR7k5qEM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zovCRA9TVsSAnZWagAAe8cQAKMtKF7FJx92Re+ol+4B\no0f3yGdzxWUhl8B+e8eXgjYcetMe1MZpG1oQF5ocYo45mZ+ASWMzfp1caT1p\nqt3F5rtADrL9int8ti7ICrWkIGQ3ccnrTxmtx1y9NRGANeARYjlB538xg0xy\n2Gin+2NchK2gxkxeX0nPH7LOcLlJpXecv1p2BK8gFZfabhM4fKfcMSSeljUp\ngzBGU/0CQp3++KsFAS/GMtgo6ZLw7wnHn0IVtDbwhA6A7hpDZar1Q5xsleui\nUTFIJCegGBx9exO1z0fYLGjOuvOdB5790fQnGsspbVTMwpImWpjspmuH1kWI\nTtk1ocnUnvEu1wNK2FMspUeKHNmOi5Jr6bkdGTxecXV4W/p9oEoPe3tHItL5\nbG9gNxY4IUkHeL93D86w9DtgaYtFSCYD6+sY7mQQQdhBrilR06AugBtQG3jP\nFpEsOLSn8vhYOQKv18CN//xaJM/uar40NTfZcQTn4VLXjUsuR4W+3eAv+qb+\nSKEpf8YAhgXJR5EFG1m3m4VHCp40SM1oSibh3/Ib74VZmlF/aq0VcPDs9tD2\no/MibYbQcP01cYxzpfObPXRczTH8TSl3scslcf7aVcLuhbyJtw8gblKnmXjo\njNaZrKjGKrpKRYStMBbCt8ILkL+OZTcDJcihy1x0v4oOWT43lrHnPntOAg7D\nqQbp\r\n=RhVa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/highlight": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.10.4_1593522734690_0.6416145193889038", "host": "s3://npm-registry-packages"}}, "7.12.11": {"name": "@babel/code-frame", "version": "7.12.11", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/code-frame@7.12.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "f4ad435aa263db935b8f10f2c552d23fb716a63f", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.12.11.tgz", "fileCount": 4, "integrity": "sha512-Zt1yodBx1UcyiePMSkWnU4hPqhwq7hGi2nFL1LeA3EUl+q2LQx16MISgJ0+z7dnmgvP9QtIleuETGOiOH1RcIw==", "signatures": [{"sig": "MEUCIQDmA8WThFCwhZ4nNDXS7JdZobox52ADG6gbMtK/vP7/QAIgQiydn+JKEGo7up2rhz0wB+CFbX5o7LNuPPDois3cvSQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2U3TCRA9TVsSAnZWagAAlPkP/1TPgMlTPsv4MbZBGM+u\n21xFilhvVtFfO4q4/fl9Bb3X4VSwREfXjrkdDAF1ojqyTW6kTzngizfuxMch\nvmxsB7ZT01pA996T+jZ7wYfc7YL7EpMJvUpB3b4jK5j8hRLpC8zZXL185FYp\nNJT5GUAjEkZrCi/702ARe1KJB9HxdK08N7vg2YPQGtGbiL7F6777nrXYF2eg\noC4knN7Yo8B19JFH2I5FTbc/QxWR82WkuVE312Kgl2ZgqAU+F8RyGqKxntKC\nLKtT02DgFcUUBT7xOVfkY2klc9OqNkguCy0PHzGcLQHy2b+fzSn+6U330vbS\n/ckSdenWWlW8caRgAgkassJ05k/xu0MgQzEVb2qLUjVJ4R/heBRo9+SELyJI\ntgJjKwJH+QfglrvND70AqJh5tEYJIfdvmskAbWoVylTVrgyjUGd8c9o28wfE\nlpYr2/p9+cCPirVtM7SZx7XezGfxlA9B1O4InLAQ9IjS/P99cB/6zrTXNxyq\nA2FHyUwIOgrU/f+KSjObziMzozCiX01FYZs9QX3D8thYJaaCdd3InEG8mfkl\nsrRlratxjWZVX+k0NYMAH3ZSSPb2p9+Q03P/qzvVp/p13mNY1hcO4ntIpqwh\nPrH3wG+RLgYI/0BzlhjqvlDYJypJXPK5T8mtP+UseT4cFNOoajk9uVhPbqOb\nSXcF\r\n=5eSN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0", "@types/chalk": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.12.11_1608076755438_0.6670873440875222", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/code-frame", "version": "7.12.13", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/code-frame@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "dcfc826beef65e75c50e21d3837d7d95798dd658", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-HV1Cm0Q3ZrpCR93tkWOYiuYIgLxZXZFVG2VgK+MBWjUqZTundupbfx2aXarXuw5Ko5aMcjtJgbSs4vUGBS5v6g==", "signatures": [{"sig": "MEUCICGLViTR4OnFlLlhazRLrxJWudNUXdPcwKsbSENNzS5lAiEA5SKWpWhVQm/hNxXa2XoziUkQ2QST6U+lwgFE26lliUY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGffsCRA9TVsSAnZWagAA+jcQAIqgKwP5u+oVxbp4zjFy\nb1Y1GTRqFkeMWjtUQ5y3v5ueDzGLa19eOo0yQ5FOWI1h1WgepQym5EvYHch4\nHWuXGZ2TQrui5KhNJn2V5AV+86S0OI1kNFkd4/pT1awe4vcdbF+JCJZLwe/U\nFoLVJZYFN4sMCnwn94l8yBkQ4Ivrx7RfJb8FTJ0gk/2mKwMRULc5mnOkwE/k\nGII/LxnyC0QVwGTM5qbzfZW5QxvChBO5kUzSn3hqdp1DeFZrnOE7wqGs1/ij\nRiI1aSFAp+UUAsTnPjsWyqDU7bWBZ3QctBuc/5RFGEAcMIa31Br3uSXbtIgv\n+BDx9wV6ZlWjcJlo0hXJnkyC9wjXe8s41AdibzToOimQB4/waH8BZGXFcxNZ\nOPOa+ONCw1r0v5ghAXZWp47nlON0X2l6tFLHtAYWmbSdg+PsRQiXQuiSy0kC\np7rcqJwRz/ejo7eCgXFpBPD6lYzb6egeuOZX0zU5LWmy5vVEmjGyie59qdiv\nDeMno/oomfKmQ2IXTkn1V+hDWIPEZ3F9qbnijIVWT6y63U/sGP3QFyYRIW68\nJVERy5EWO8PHtxwkbbTv8sHQZbFBe7FAo2GplZjgZMhJNIRO0Gggji9Dx7YD\nGqv/F3/l3EWdoFU/sJhKhc29T44O/diXJAUXnVe4XIAth7mS5tm5VCgauNxQ\njqu9\r\n=qjAp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0", "@types/chalk": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.12.13_1612314604007_0.05465442405547383", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/code-frame", "version": "7.14.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "23b08d740e83f49c5e59945fbf1b43e80bbf4edb", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-9pzDqyc6OLDaqe+zbACgFkb6fKMNG6CObKpnYXChRsvYGyEdc7CA2BaqeOM+vOtCS5ndmJicPJhKAwYRI6UfFw==", "signatures": [{"sig": "MEUCIQD3WvjT7aYgGWfki0ujdMozw2nuAT2+qKIu7+YTd+4GfAIgSUHIpzFp+gDpE8zb4LcRKw7a7I0q5zWAhZhyc8+IGIQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrVCRA9TVsSAnZWagAAP2MP/3ZZRGeC84abI/q1Mb8L\nqhKhgRAwgxBRLAtEtnKqxrOcGpkEMu8ntIVqk+3NBiM+ZeR6KyvolIkcUhKS\nuKls+cJEMdy7HUbTLZbNCzyU+Zaqh0jQtXSpIa2wOfhFWv6MXUlNA0oDpltK\nttMn4hZSUEaIUyqqdHWUJpQMgEP72nWT81HeUKSXDm0PIwAFWREzv0Ij+v+6\n4F21PGSjLZDgZU/zxUcOoIF24R4IBSTlgM9GuYwXHXWo1nNwY7y26Q1vAEVR\nlxqFkS0/xBhYSvp+rra+IHuRjDhCvXF3SOcmAjCofMvwtrgdzx18aPGMH0m+\nmGhqyQ3kK+S1OEl18sCZuBN2S95jZSkrngU8x+g8ezkqureAslyKovbc2zJG\nKDOZ+TupmvLQSi4zJDXDXI0EI05dPSkqEjAdjFTfrVRZukMTlUC1SPZGU34c\nFPToQZWPJotwy6gvJ7yVXSE94nq2vCp3cMyMiP8J+LpT4fcUgwJOcayzLq3F\nkh2IVNgaFZ8SlVwRLGMUGZLdltEyY9Ilb/q97GbX+0OUPz7lJU3UKVsXgm++\na5ooUk7A6h02mUsyci13lYVYZyjFRmE5vM86R6KHXOD9AQiBI/Zlo1SerVMK\nQQKUlCBCqA4w7DNwjCy4oLklpc/BV0lVCa1rRIb8tgwv59nHORaCc3OGOAhZ\ndvmN\r\n=/5/B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0", "@types/chalk": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.14.5_1623280341620_0.4476406179404928", "host": "s3://npm-registry-packages"}}, "7.15.8": {"name": "@babel/code-frame", "version": "7.15.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.15.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "45990c47adadb00c03677baa89221f7cc23d2503", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.15.8.tgz", "fileCount": 4, "integrity": "sha512-2IAnmn8zbvC/jKYhq5Ki9I+DwjlrtMPUCH/CpHvqI4dNnlwHwsxoIhlc8WcYY5LSYknXQtAlFYuHfqAFCvQ4Wg==", "signatures": [{"sig": "MEYCIQD537Sh1r4Qmkmb/GcUUkypA/6HO2VotOfwnB/qWq8dpwIhALhj9+OVji6U5cArZ8x1Gb4tos0GHCfnXd4Zys6piglY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6957}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0", "@types/chalk": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.15.8_1633553695020_0.3123068814613743", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/code-frame", "version": "7.16.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "0dfc80309beec8411e65e706461c408b0bb9b431", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-IF4EOMEV+bfYwOmNxGzSnjR2EmQod7f1UXOpZM3l4i4o4QNwzjtJAu/HxdjHq0aYBvdqMuQEY1eg0nqW9ZPORA==", "signatures": [{"sig": "MEUCIQC8uMpDvb38DtQmGjfeQJaZ0Wi5kpx2b+KnwMn+GdzM+AIgCTs2uH1/EN44FKp/ibx4EsgDJQ8/3p8fmk+KIiVZhy0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6957, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzHDRCRA9TVsSAnZWagAAB8wQAJ9KyFBSyHB+4c/wG28S\nkMq+2VbrznF3pdCb49Hh/OYcPU+VSPMSdA+XgzGP+rCrWTOIjN+i0M3SJrpu\nawL+ckuiJBXgVBLfXfBtOie9DB76oXU1iDY0IAOII/0m8IMkxI4c4tg8CYDl\n3iMAT7zOWvVPcPdOjdfSWcQNzQN3ItZLoTMtqmdo7HyRMp+0aPgZV4z0Z97t\n2cXEsyHr4YtPLGX40fVAJmaSEc8h/HFsFmRFAX05e2i/i9rMGTHTVJy5xvAr\nKqLtjjTSHVsMJjwkcm+bsEitJ/ApUZQDgGkXgVFMaKGLxC31jmrgUowlDUFL\nDV69zlIzT4J9vWhkOx9FX5aZejmBgihalhj2mJR/qc8BqVUqx300hKW9YXs4\neSO4EvLKQyXTkuWgXmACvTC6nvCTewPsgbU1YKx87FYszU3P9Kp/CqEyTnSI\nGPsNPLOIxywwH3VTxNQgRKynSoa9OdJZSFmhRlxhlPoXZ6GvlS4tiqrudYPy\nkVfxuqdqSL5SSe1NiOgKhlQLtTXeNE2qj79gRzuWTWTz52DdXYIGx07N8+RP\n6dP8xWZ0e5G1k/1LekvUbUJuSsfHQ9nIEHFmVHS/r9UyTfbo/h9As+pKygJQ\nv9fxE3k5FX8c0DnfKZaw4vfdDrHV5/V13pOpRW87cBDzONd5gBGSEP375Ruz\nJnN7\r\n=VFBN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0", "@types/chalk": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.16.0_1635551260835_0.47911995928666395", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/code-frame", "version": "7.16.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "44416b6bd7624b998f5b1af5d470856c40138789", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-iAXqUn8IIeBTNd72xsFlgaXHkMBMt6y4HJp1tIaK465CWLT/fG1aqB7ykr95gHHmlBdGbFeWWfyB4NJJ0nmeIg==", "signatures": [{"sig": "MEUCIEgaL1AbAeID2ISFc7jGu6n6uIvvMpetw+ijBr/dIWyPAiEA+N8dyHmbXICyjUmiVEmIhsfAWAXDKmbzoOAbBzMDEbA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6957, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk05CRA9TVsSAnZWagAA4EcP/irVM34cdSI0GHoGHYPE\n9EhY1mcGVCOmja6L/tAtH4+IzemmtQupYIH1E0UWOHpV5+w0P1IfyPPnI4Lu\nAsAEOFc3sVbHKNysXr+4hajj7VkwbCuikkuLL0z1v0KLGkZ6+lO/IQP3IGA2\nEmHTbVB4Rujrj691z1NRkPaD4MuZLl5cRIv7f+gIB3IUsnwJASRHCEUOUqVa\nBIHi3njIDXSJBf0T8q6p4kfKzEh866y6eF6FJ8so71P7DfzYCwFH8fMO5xzH\ntvnN3w9sjxnqdLTBzWjtdMapcu70wR31xNl7vy5Nm++dyHUSucegV4cMpdcq\ncgYoyA7D9xM6gWYOWaNhIS16wffRTIqOXAqN0wQ+ZSSD2XTMLxzktLfj0dFD\nHf/COn9lz/u8mH+yHDa77ntIrGhhRHCcAjb0ZrHbIpDRDj51m4SeAr2MIogn\nQm7NOIFXuenCKr/WkUq9u/74U1ntlmb97QPCFTmN28K5H2DNYN8lj37AQq13\nNA2++g9jDdhhGUoQkBT+VHjyteLA+s9sfCBY+SJJsxK6bixm3azSxUb0FGuX\n+ylbSArOQrebaj7riqkUeak1y/bGYGlnaEgGJYc3nOOFwAkVfEgMl1wvv29O\nmGPAnVXLquzzF8sJxV/lJfCO748yt55Eaia0NPX+fmnIPJdLf3k+EsUFJLMk\nPIa1\r\n=ZaRm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0", "@types/chalk": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.16.7_1640910137508_0.11731036872537048", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/code-frame", "version": "7.18.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "3b25d38c89600baa2dcc219edfa88a74eb2c427a", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-TDCmlK5eOvH+eH7cdAFlNXeVJqWIQ7gW9tY1GJIpUtFb6CmjVyq2VM3u71bOyR8CRihcCgMUYoDNyLXao3+70Q==", "signatures": [{"sig": "MEUCIQD+5XzGMdT2KJfHr8f15nHHuCDT16enxIdayfcjHVULPAIgf02LRGDPLDSOhWdbbOviMbXZ3PslLpOP3yynKWaMZjc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6979, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqE9hAAokIhlPGTZnymHmQPYRGVftKS7nbIp/svg1Tq57oydJVaYADl\r\n6nLkYFNnG+JiWAqEkqIIGlXk+jQAKbRUtyVFgeqtP7Puyctp4Kq8HiXTTioJ\r\nrgpdEx6pL6p1Dp7ucZyumiQDnwxckeo/AuzWQVPnMESLE3kAeT+xt5nx6YjL\r\nApsyuQ8jTskZscwamjQKDYhk+71MAhyFI8Ll/LsPRDob/8lqMzY9K67y2bY4\r\nZCDK59YfQKg8N8fdMOQXj8xie9LJofvDNsbfhZJX0yATU1hPlALTkx9z/IZP\r\npPQH5LAHkcvcCoidnAamee8iJiyogisF1C7ADSK7uGhJoO1CkcC+FfPZ27ic\r\nlUgAgiP5Vn33+hAeyiYTpoRjFgdm8x/t8khXYzrj1l9xhXb4kODbB0cxpSq6\r\nXHI8u0tg3AzjQOa+xLjiW3iTnAZfwnV+eGwCOXseuPj0raH+RAiQiZyQt8xc\r\nGBAijz3ToAebe1fXJQWDB6+Ir2KNgLlL1vlp9NyJO00TQs/n4K1kriTgxb8M\r\nFNW6PKIcMGUzWv4BOqN1q7psDCJ65SajH8eyXnUaOhH5jStkGNGnP/Df/tHE\r\njyZumGGD3bJfqREYB6wr54qtkyQvi/nblVV6knV9GY9m7uPJoCXfii3KjiUe\r\nPXO8q5M4ZHPhrhkX4E3pr3g/TP3L4hhzpMA=\r\n=JUhp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^2.0.0", "strip-ansi": "^4.0.0", "@types/chalk": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.18.6_1656359407550_0.5506906732008645", "host": "s3://npm-registry-packages"}}, "7.21.4": {"name": "@babel/code-frame", "version": "7.21.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.21.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "d0fa9e4413aca81f2b23b9442797bda1826edb39", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.21.4.tgz", "fileCount": 5, "integrity": "sha512-LYvhNKfwWSPpocw8GI7gpK2nq3HSDuEPC/uSYaALSJu9xjsalaaYFOq0Pwt5KmVqwEbZlDu81aLXwBOmD/Fv9g==", "signatures": [{"sig": "MEUCIA/b61krRS87WGqEc9sl74BimW24fjHuNJbcEgJGfHxRAiEAmpEGK0qVoE/SeVeinIyW2GwX2Zxwy9lGzY4oGrsCpgU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJqF/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWmA/5AJpT1T1ycS7Fa0I09C9nqGmSO1HRtrLdMICyRWb7qWHesWRr\r\nxuCa3GNPjX7b1Us3xdscO4vtgYKIwvAVMHgBLW3paGomWXv+XDa9mjQTASyI\r\nopNI3I7iDc+9tCjbacMBXG0xr68vJ4f+UmobaV624zMBiqy/CNfKClbb1y8Q\r\n0o/fvhZZ2hHxORHgXW82gQ0cQL3afrpPFuUCZmhmTkD+YNUt8VyIwZhSampq\r\nM4+mVF29G6biglcHsTl/ZuQSHlZEMYvaByz1LgnEBISkouWnT5onzkkLQrF0\r\nIpdftVPT+TKvIRWwU6Db83bqDOIbccKiekH1Chm7kiO8qWrozt+vo6Are/su\r\n0jpSqpUHByhtWlcXjuikO4fMInRoF/gpEeDQGexUUhliZS4FLLGvvu+nB9vu\r\nJk5OLsGcfD6fjoF3tfobbeGPeZIdUMTsmo4fSHwETRdO4kOY4/eQMfi53Vt3\r\nWruAxn0Lfk8MLesbupSZRArdsz5nVnqz2HCR3Va2mjMpJjgBz1J0Q8aaHVh/\r\n2b6owDKMeIZSJq1njInqCOUJ4vV7Vv+aGKHNNVi334HYknOB1KVY9Hc8b4uF\r\nudhEkMttJJDvbZoGxlbr8pqRD+d32twJNil89oyy4PncHChO9PqmRSZlLHmV\r\nd55TKJLEjR0MZk7mj06Kh/Z128EGMyWwRhI=\r\n=S3sN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.21.4_1680253310823_0.653273126031797", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/code-frame", "version": "7.21.4-esm", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "57d675780d9fca609863eabfa78ae8b8cddea3cd", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-/Vpjp6mkaphUC7+KcDSu7EXYIil6J91ht6nTL4qFhM1PO8/pBwTLPcj8lF/55gKOoXMmVFK3FrXI2/iDHzT5SA==", "signatures": [{"sig": "MEUCIQCwFWKrZ/ple+70MqYGBNWmpf/vingCtoG+ImSgAWgnhQIgIfXo+cYbXvauhX1h7OuBoRUuUaedJpuZR2eKVCjynjQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20062, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQQw/+LoReCWrO3eLhcPU0DZ3zj0L0snLUdX1IFsuEW04Hqj4s+xvU\r\nX2Z0GIBY3wHWUk84NMpM0PyscfBmAyGgySa0LrIy5BuIecWKHkY9gTIxhQ3G\r\nh1jdQR3F88XoPKorWiRiGWkBa+5oessHQri7XrK/V+WDd8q/U5DCAt24WH5C\r\nINh9OvmV6syDxKDM6h8F4CeAZMa6Xa1Hj0RD/Iu6oUmLhDjw7/AxPChl8HZf\r\npN6VyPxdWFtTSd6izxY2HgR9UjuChP2h/z3h0s8NJJ3mZdCWzA5wqYkhzvvc\r\nSMOP/JhMFqvZFfx+neeFXhfcJJcbxZhAIV9xc5Ur/Bx/H1TrfT4rnJTEAIOP\r\nUf8IxRDcQzxd0FdyWk0JoWmWTpdRpff0XR8MB1y49FNovzubKADjBNAIsItB\r\nSYaKC10XnGOFJ7O39eKyagltdw9iVmHyPKeMkhGVp+UwTO50W8jIo3PvoDiQ\r\nWo5DivaBepfo6AjYAfPMy6Pa1OjektKs6Szu1ThC4pQFDNbYpn13TT7sBxPd\r\n9CImIDcStEKqXtjmsACaLNw7GYVypJ2WR9CD4Riu7/ktMI/f2wQ9zQKmwbGB\r\n9iT5vb96QLcR5X1W6T2BWHIUg+fODoGrsn1ykj70GaXq4dg/BnQ2IQME58ZT\r\n2r71fwCQIg3VsAK4R+PW0CxKHzmJhFj8gik=\r\n=QGXN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.21.4-esm_1680617373844_0.9978087000542704", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/code-frame", "version": "7.21.4-esm.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "82c5b25e4bd27ec32c29538391bbd97c7704a780", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-2FlV4H9BfXECjPhQfAocKHhBW8SRVZE7FSACUzp2DYTCoVOj/Ba+VYilWvF2dPrYAAZE25Q6ocrkA5bPfYPeeQ==", "signatures": [{"sig": "MEYCIQC6H0lglZm/hixVrgpptmqL0ebbVaoRBHTXUI1fSBLXSQIhAK/Cd3q5ZkyORHj8Tm6c0QYY0XMsTj2akfmEFp3gDWFi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19870, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRYg//Urk/dZDjUB3fO/ecygWakLZMooRC7nnd3eeH9DCgQbQwby3u\r\nN7KM1GIRzOnwUwxwqwErb+iKzcNfjPhYa/WCTUwogHUcCpa7W+BFC91KLKIJ\r\nF1RdJ3ReupfLqcxGAmDB4PHQosFas2G9RPkLhDmtuRoaIQYDRJfO2bfBedzE\r\niU5UEOHflO8Dv8T9LUeF3CmvaeXUcG1e2kzi8A4J5OjHuhQxerQ8CzGomTKj\r\nE/**********************************************************\r\nmioJIPz0i7ljk2MT2NbgURRqWe9RM8gespNMgCqVyTKolyTkZH3n9s32XQqg\r\ncKNpwBCBa2x2lKAaBeVQQSI5KJ/WLK+Oc+vme3ppRbFDhuSI7I99oVEqTE/e\r\n6aT4P3q0EQzPRTHu28E7abiGSY7shy2GRjHmFACcoIyklRJwJQcSqto4eSg9\r\nRneErZTXTLGVR8Tthx6rR7f0yzHBBXVU2pk2MTz0XtO7SjaTCtOxpr33Q4uz\r\n14gKaeqRl5PxIra5RLYaHz5osTpjMv+G3DC8ZWH3pYFnZc7QmmwMxPRpb/Td\r\nd47K4M9hS7rQwxqTh+OZtvQhQSsD22+xtfwsxlGcK+YCmI/HHRUsVhixxIbF\r\nuASgu1X7vwJBtgRSFveThdvtyYrcYuVen58=\r\n=Duiu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.21.4-esm.1_1680618086067_0.3782032330802192", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/code-frame", "version": "7.21.4-esm.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "0b6ecad8ec469e599bc16ee81fca63c0bcea8b6d", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-4jLmZP10Y7ZGQhNRniAZ1qHDcrG974M0kII1Ke4VV3tiu8JSd52bKNgFz5ap/J7CeaIdKDSzH1IEqvTEiM/pog==", "signatures": [{"sig": "MEUCIQCSFOp7+N8f5g/4RYp7tAuplUDgJRWG5iotDX6ThE/0dwIgbL9OKz0KaMNfvJWvvTgDy4P9VQrdFE6/8hK+/mVH6Lg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19849, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDafACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGaxAAnGmukR9/n7Yg/45CCI33nQAPGgxXd2OvlFzkNSAL3bK85MOV\r\niffEuV/wEIpb10PvQsn9aP7160KlQEEJCURLczEetEZJOv/z4pe6ehcxep0l\r\nbzEFdLvCXjAmNmzf+NvtVVJ+xz+YiP52TMCVECZ6bS9tXkRkhg4QVpAcFIjt\r\noU8zOFpv222R0aGgikuzz0m45UBSdlj1xzbw22lZuRkJ0svgM/VfsP4EnwFK\r\nb+CFo4gIPWT/05pVVKiUHV3ut5g6jUCWzlz3dz9x/K+FE5Rm+xpjRA5Ikidr\r\nYaRpWf0mckK2pQVr2/lSME1aWNFc7YT1S5Buf46dMT6qS+IHs44vjAHRpw35\r\nRHxs5FdQ1wJ41K149gr88fW8COUteY+NP3GC/vW5jkKtEfuyhyWXuRySyWbJ\r\nbSp0XN+k07hr7rCijPe/NLUuRuqYm97ELnUkk3FFB4BG9lTrcZnlLLZyXezC\r\nY7g6+4bWrLu4GqwFBHhg11meFBVeGo2nt1YS/9q1OtPu9LV2FDLF4uhvdnd5\r\n2652sN782q0NtdZYTvnxcqdZnVJjp5fyer/aW95a9ksG44CV1iRXFhP772cr\r\nRNXCotssNFT0GVkxPUalsx4VMeC7eAgeN3HjzPWRgRT6nsJZdQ38W+X2dtfy\r\nh0PzFm+RRrcir6TN/H/evbhrfnA5IOhvgdU=\r\n=M7ZB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.21.4-esm.2_1680619167161_0.6927712516761759", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/code-frame", "version": "7.21.4-esm.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "d1db4ba2216e099f8814142f9f60c3186e5b43e7", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-98eZCF8Az6s+55sJHBh3qN32c3UMZ6iK+YVsJsnYAioKK1DP9wy2J0XTD29esHBuxER8B8XB6fRmNW7MJ26+9w==", "signatures": [{"sig": "MEUCIBwffqT/ihGYt1za2TLI8fza4qqk9GlOZ99nNXFSbvg2AiEAx0VfuLZnO2BBz2+mdTnC5S44cR4f7PzNWmCim9P+86Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrIzhAAiE+OYBVLiQB9DswPZ1azWFa1o6rrqBZbMVJRpn4GtCya/NlF\r\nLOOcthWRr3AoEtY08gbrNBcyli7xA8M622XAU0dx9uPl4wl72RmFZo/crgY0\r\nGAko29eqYGiLEqsY/lWSLM68NxLdSEe/D0ySqXHKPG6cYAVjs1jdlu0G5+xS\r\nqwYoTl8lfrjTkM/YyWFD/YuvD/TDvjrLQp3w/I1OQ0kHrdeG96SuizUqyWvZ\r\niijk5cXUcNgsuwD8gmXlahlRcdResn1y8DBbjjzstngMDypwvM9q+8Ne94b6\r\nR2wdZvA9XVIQ7/yt+oVcEMbSi2N2XJyBywCtKDk69K4FsGKNyLTIYuc3gGc+\r\n99P0jIEUGfFrawQmn1pyBLxNiEHZ0xTT8QDaPYpFP7ohd6hcxuFLjV/DCo10\r\nx3QOFkHnlzUn+uQXkwS2CAQRCTDF7a2tXPSFvRhmsXVzHiMTW33D6rjs2tJw\r\njkEDcR/QB+AVhtvkE04qTRTfZXmv42t6mo2jx8x4NM3AZvTFot14KiQTFxxY\r\n137E8ULbBF9Ej+DOI6q+DObUqzlZaRAXF5rC1Wm3k0mMEx3TAsjrrxtobX/N\r\n0h5XIIz/gpTmXDrg0MkrvNhUFU0mUVosWYhpcn2uyuaQPVEKJeitOgiiy7Ja\r\nRa6bGXpJ3cTa2L1doZa2Xn2mOqI/rHx7S0g=\r\n=H8ip\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.21.4-esm.3_1680620176024_0.740630029340589", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/code-frame", "version": "7.21.4-esm.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "7d8fef5aeb1bb5d9a783ef0eb21f5d9fe28aa48e", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-D2Pp83fYFVgtryse6dEfdsTKkC0zY/+zi4dQLud61wmGbqfY0yZy/3NBQteJC+itmHMZR8q1HD/k0nm/pBuVeg==", "signatures": [{"sig": "MEUCIHpZxn48m177g1hTgR35PpSYeUcwDErf2Wc9ghvLWtmsAiEAyTXFXthghHSFZuUATl8TsVBmv8TB2vyytAu576MURPA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19869, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpdPg//S4794PVwJ5anQzlGhu6kV7Z+9QDHTfkbJssB/aAIrK6KHpgw\r\nXWoP2R/ynDkU6Zw1FyLgJ82hIk/WV8xmeqaPlkixkxFBA07amYsbu2Ja741v\r\n350uHvOGQrqNoTKU7zlkM97Mm4ThVZ9VR04JfRU+u9B6se0ZTjyQ5/zaa+c7\r\nG/C6Rg6x/FPxaY8RSqpdLRXaZVbX2BVUAL0V6WCzG4BelmTBQ08BRt5LZ9Nv\r\nTB+jnd++4XRkvdFlQMpT0EzBvjAKBvsTT9x2zt83ie0GiQf0GAkjujrmFaut\r\nhRx/ej2KKmidiJrI1Ib7I29pFUdto4IRlnwiTfGr9HARKs8asdfVfqVyFSFP\r\nIXyxa/gQE9dek5+/3ep44ZJK+pgbdk8+pI22IeJdtl687a3So+PgXUBiuYYD\r\nTeDm74ce9JF+ngHg77/WxUK3BlL6gWiUgq3RYZ1s6dRUZBDT07CNBkEyeBeF\r\n5KXJ6EGk7Q+0qOTcUPNFaW+pesHalVUTnkGX76oL+HMz8Juuv8kuwA+ubuFg\r\nQnMhZFN26TN4s/opzUAwPIwTFqthTmIlHMYtEGx0jlAcgaoLuCzjIVeKXput\r\nkyuBZJPbT9qKKt26xCpy8yDpUbrCZLrPVZ7rjGnSBDo2JhkrYlmbAUxFD0ZX\r\nEJchCJr3PDhNxs92+BBGuwzUpNZYTKJI6oU=\r\n=E/4P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.21.4-esm.4_1680621207619_0.9354220419879653", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/code-frame", "version": "7.22.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "234d98e1551960604f1246e6475891a570ad5658", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-Xmwn266vad+6DAqEB2A6V/CcZVp62BbwVmcOJc2RPuwih1kw02TjQvWVWlcKGbBPd+8/0V5DEkOcizRGYsspYQ==", "signatures": [{"sig": "MEUCIQDh4Tr/nqttqVTwg2LmGffAjPt3Jaaesute8W2kZ0QySQIgGnf63pPbHYDE6fawoJKeBZKS0GS43uqMhcSxkjqnmGI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20034}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.22.5_1686248485286_0.4812422926813713", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/code-frame", "version": "8.0.0-alpha.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "316f731e28ae0698502bfcc33b83a3cdcfcc215c", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-w5ERAAyu6eFf4qvtCiOy5+Zh4fzrkSnu8TfZJTqN5IfLisSIup1NWJUS/iZ7Hc3e7kZSWiatq5vS4PP4YlUpCQ==", "signatures": [{"sig": "MEYCIQDWbm9jKVusA36uPK18XvTC2vGOFWPmSeFPPf2BBlfSbQIhAOur6HG+TjYRGZ4AWMH3hq7ekNJA+89LO4aLIeK0bwp7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20763}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"@babel/highlight": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_8.0.0-alpha.0_1689861601674_0.6174469354183447", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/code-frame", "version": "8.0.0-alpha.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "9d4dd7b419930fb0e4bc452ab929267841a74ab1", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-lFaogDHlXCpz3uVxNdL0IzhS0E/VVY0n0f+hfZk+HXenuKRnI4fB6kNk1nhifLLiCZFlWKUEAiJW2zMo5PNAFQ==", "signatures": [{"sig": "MEQCICrCfEzVePuJ76Pakps+52o+MFQTXcwwqGOEKtNmDMMyAiAW8CV+K0msYnLDXhg3IdwrTqQuc6Lyf2UB58i8CwDM9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21684}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"chalk": "^2.4.2", "@babel/highlight": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_8.0.0-alpha.1_1690221128424_0.4018754795084478", "host": "s3://npm-registry-packages"}}, "7.22.10": {"name": "@babel/code-frame", "version": "7.22.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.22.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "1c20e612b768fefa75f6e90d6ecb86329247f0a3", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.22.10.tgz", "fileCount": 5, "integrity": "sha512-/KKIMG4UEL35WmI9OlvMhurwtytjvXoFcGNrOvyG9zIzA8YmPjVtIZUf7b05+TPO7G7/GEmLHDaoCgACHl9hhA==", "signatures": [{"sig": "MEUCIQCpXx6JmIbuhYfosxK6CfoI6UBgZEr6rhrN8jn64ad3OQIgQeLIdkfz8zFLWzN5+KS8zVMGtPb7/KdyYwZQ5NFlZSs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21513}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"chalk": "^2.4.2", "@babel/highlight": "^7.22.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.22.10_1691429120345_0.7282067627409476", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/code-frame", "version": "8.0.0-alpha.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "447466754a3d82aad58a17c207d70a35c0168c80", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-uM1On1MxYaZDFVq1gPbGJx1Rf0N+lwlPOCBHZPS8RNVfP9CAG05xzViguK45e16n4Wo6V3WfaTkdmbuNusBnJQ==", "signatures": [{"sig": "MEUCIQDn+1SZxAUEKcgnE8yneiMLyxrUbe11kYGPenyBK37D0gIgLNyia8KeuL89YlwEaFp4FiItcEFSrVlIoOP9Ucgq9b8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22026}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"chalk": "^4.1.2", "@babel/highlight": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_8.0.0-alpha.2_1691594101591_0.46216893419635063", "host": "s3://npm-registry-packages"}}, "7.22.13": {"name": "@babel/code-frame", "version": "7.22.13", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.22.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "e3c1c099402598483b7a8c46a721d1038803755e", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.22.13.tgz", "fileCount": 5, "integrity": "sha512-XktuhWlJ5g+3TJXc5upd9Ks1HutSArik6jf2eAjYFyIOf4ej3RN+184cZbzDvbPnuTJIUhPKKJE3cIsYTiAT3w==", "signatures": [{"sig": "MEUCIQC8scpY6679qAwgpj8PEbfJGcZ6DXK72SKOn5m9yQjAXwIgUzPnpAbDMlyLg4iP17Q1ovY+q9Inoge78BRwrEVLiuA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23714}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"chalk": "^2.4.2", "@babel/highlight": "^7.22.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.22.13_1693242117668_0.5727678450760927", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/code-frame", "version": "8.0.0-alpha.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "b645081f9b24c9f873d062f22bb5b4facf8707fa", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-RShalplAqggyZed6TXbcTtT8hNRWFs8t13SPLVV302qZ/Pw3b84SVaDSVee4/JD1HXytUGh73Wdd6S0AgthZ0Q==", "signatures": [{"sig": "MEUCIBF9fKkWBpTjZHd6Hv+33jTVQDqzR8IpKjVHSr6kvPb1AiEAqe7v+9l0k6kEGMtyTggAkILI9MAvjRRWLS/pfF8TWQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21828}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"chalk": "^5.3.0", "@babel/highlight": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_8.0.0-alpha.3_1695740224824_0.4700028571414676", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/code-frame", "version": "8.0.0-alpha.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "d1deb7be4b01c6a79f7a0c0da99faff66fbeb81d", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-qU5ng/Qu1+LtDPpYx/mwSnnfROMTcsMC+C7jdMmmjPujZDmEP4cdm6QhXJzq/Dz/SgnYGVxmn4holOnrkELS1Q==", "signatures": [{"sig": "MEUCIF2PbPx/mxEb8KEXl3vnNKDWmkwhwBV0iTReqBiSTvRiAiEA5fLdg9PX9Wc5DM0MOjHLFxbPzCpNPtCNWhuG5xhlbC0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21828}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"chalk": "^5.3.0", "@babel/highlight": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_8.0.0-alpha.4_1697076385279_0.9760864834318879", "host": "s3://npm-registry-packages"}}, "7.23.4": {"name": "@babel/code-frame", "version": "7.23.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.23.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "03ae5af150be94392cb5c7ccd97db5a19a5da6aa", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.23.4.tgz", "fileCount": 5, "integrity": "sha512-r1IONyb6Ia+jYR2vvIDhdWdlTGhqbBoFqLTQidzZ4kepUFH15ejXvFHxCVbtl7BOXIudsIubf4E81xeA3h3IXA==", "signatures": [{"sig": "MEQCIBa0EKJuPIZv8Iz0SwaoWz3Z97uUlLlxbi5kxpka/U3NAiAo/RsbH39HvKa5XWKlmXcTnsluCij0Zx/i2iM9Ko0QLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23336}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"chalk": "^2.4.2", "@babel/highlight": "^7.23.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.23.4_1700490135327_0.24997893186216147", "host": "s3://npm-registry-packages"}}, "7.23.5": {"name": "@babel/code-frame", "version": "7.23.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.23.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "9009b69a8c602293476ad598ff53e4562e15c244", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.23.5.tgz", "fileCount": 5, "integrity": "sha512-CgH3s1a96LipHCmSUmYFPwY7MNx8C3avkq7i4Wl3cfa662ldtUe4VM1TPXX70pfmrlWTb6jLqTYrZyT2ZTJBgA==", "signatures": [{"sig": "MEYCIQDFjHYAxKDEX7YbNfcJvtQz9/Vlt/6scwode2Djy0fDdwIhAPWb+Dcfl8RnYxjAOV6j1+PSFuzxLnixA2DvLkz31HUy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23336}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"chalk": "^2.4.2", "@babel/highlight": "^7.23.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.23.5_1701253537488_0.5974930720073222", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/code-frame", "version": "8.0.0-alpha.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "55aa76e28289ffca559f673d541cc592d0470b6d", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-voUyLNaZKUWoPTV0NDS4fd8lnoPPLlbIWapKwTnNdWOIhn8Y87iKIrOrNVKexuRIlR7YMF3zlQlqqNkLE7JERQ==", "signatures": [{"sig": "MEUCIQCSWFRlyZCyaFlgPib0poY0AfBN7KbVMQ9VFMUX2J/wiAIgHN4vJHQ6xFli6c1llkAg+CUpPFKXgTy0lZVvWV0AwZ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21828}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"chalk": "^5.3.0", "@babel/highlight": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_8.0.0-alpha.5_1702307943832_0.9782318836319028", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/code-frame", "version": "8.0.0-alpha.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "dae7c177a7aa35d4e67f2442f9bf5e4e548827bc", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-w29LUiFqVMe+Ybp/+xtQKAp6rpxjHxtCW909I0OMHvFKzrGB0K+1yQGRzXhTqrd4f7ZRRgzkAxdr0EOEbHt3dA==", "signatures": [{"sig": "MEQCIHPFkEX1dvo7xLaRRQjXQ+5j8AQQIplgd9s0uYvv0l6MAiAUDKhok70ZViZxvjYiYRf6oLbm/u1teXIalir0NC4pMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21828}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"chalk": "^5.3.0", "@babel/highlight": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_8.0.0-alpha.6_1706285654357_0.7067057478307361", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/code-frame", "version": "8.0.0-alpha.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "44d3b79ecf36f78159727bbbbb853b42acb7514a", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-TDKH8df0INLlLxwAmZfPYW8s2zyMgy+ZKSxdHLR6PyLJKcKRg9CoAuFnUjcnOw9xMaWRlb1NNieCSMN0RTN+aA==", "signatures": [{"sig": "MEYCIQCTztg1chnWlpmOIedgaw/y2GYqptUmc/1uwXA6CDGs6QIhAJsySURpsFKBRfDYNGT+yJfJ0/vQJRs96ZYZvuDXJM8m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21828}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"chalk": "^5.3.0", "@babel/highlight": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_8.0.0-alpha.7_1709129103395_0.01512953545350637", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/code-frame", "version": "7.24.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "8f4027f85a6e84a695276080e864215318f95c19", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-bC49z4spJQR3j8vFtJBLqzyzFV0ciuL5HYX7qfSl3KEqeMVV+eTquRvmXxpvB0AMubRrvv7y5DILiLLPi57Ewg==", "signatures": [{"sig": "MEUCIQCqdH1iKBdNGb/cHsxfCmK+2YMAdndZOCPQsrO9j0mx7AIgLVkUof86+Vikg0vy/mK3+TQjFd8LqEZYU5Z7yllRu94=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23484}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"picocolors": "^1.0.0", "@babel/highlight": "^7.24.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.24.1_1710841757493_0.9671163053915475", "host": "s3://npm-registry-packages"}}, "7.24.2": {"name": "@babel/code-frame", "version": "7.24.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.24.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "718b4b19841809a58b29b68cde80bc5e1aa6d9ae", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.24.2.tgz", "fileCount": 5, "integrity": "sha512-y5+tLQyV8pg3fsiln67BVLD1P13Eg4lh5RW9mF0zUuvLrv9uIQ4MCL+CRT+FTsBlBjcIan6PGsLcBN0m3ClUyQ==", "signatures": [{"sig": "MEUCIG/px2ZZW8+Fjs9VvE2re2jJglJyKzSZ1WoQycTeoFnkAiEA/tjxegaDT19xYVAHcnCVUYSgleix+umRBsjYDg452+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24112}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"picocolors": "^1.0.0", "@babel/highlight": "^7.24.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.24.2_1710871655325_0.12862249532471837", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/code-frame", "version": "8.0.0-alpha.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "6b1eaebef1fbbdfd9cc8197c6572390fe67822fb", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-PaXRlxddNNx+S+OZ/laLOqhiqaDgBQCvxR/Z0CHcpXgPRmtBNbRxvWhf91+YtST1O+O6j2K0F4bb5DvA3Hr4gQ==", "signatures": [{"sig": "MEUCIQCeTCuzN1GUfY25brVUJu8ng8mywCh3ljfdr2rqm89FCwIgHmV8mEVCxT+1/t1roKiRkIrZeHPuKzDOT7apFzuv0dE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22626}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"picocolors": "^1.0.0", "@babel/highlight": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_8.0.0-alpha.8_1712236796470_0.4501444459253905", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/code-frame", "version": "7.24.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "ab88da19344445c3d8889af2216606d3329f3ef2", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.24.6.tgz", "fileCount": 5, "integrity": "sha512-ZJhac6FkEd1yhG2AHOmfcXG4ceoLltoCVJjN5XsWN9BifBQr+cHJbWi0h68HZuSORq+3WtJ2z0hwF2NG1b5kcA==", "signatures": [{"sig": "MEUCIQCu62iigrpWDGwlrjinvMCrJASegD2q34F4dPTWLVFmBAIgdWXQft9Hn8Tx+OZ1grM/Zsyw9pb905IrflFMkvnKlto=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24063}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"picocolors": "^1.0.0", "@babel/highlight": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.24.6_1716553478737_0.49449839711946897", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/code-frame", "version": "8.0.0-alpha.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "3130ec986c2b7eb13e00c7752f4e9cbd248a68f1", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-8.0.0-alpha.9.tgz", "fileCount": 6, "integrity": "sha512-dd5W/UcXSe896OEdA2B+Y4e1tIcBdsQQyvHmyHeU7bTo518GqVvPi5RDi0H1755GJcGlnvytf950fQGDEmJLQg==", "signatures": [{"sig": "MEUCIQCp7xynt/SBQV3+9LbCqaPZ/XIDoEShjW9UB1L7hxlMEgIgcBNvu5o/yk+8YwBI68UzlqhojwdXZbhDfdF3dqLbZBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23954}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"picocolors": "^1.0.0", "@babel/highlight": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_8.0.0-alpha.9_1717423464868_0.821538695983413", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/code-frame", "version": "8.0.0-alpha.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "5fc1a44c9d0f988cdf0c713894110aca740ac828", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-8.0.0-alpha.10.tgz", "fileCount": 6, "integrity": "sha512-ZomLvZDgm4HwXt4sldtnMwiPOQK+pmX+zKOMst5XVEzC3jngdl+HIwXoUtthoBhiIzlhfO1KdBF9zvILG2izSQ==", "signatures": [{"sig": "MEUCICPqsfd8gxFSe3EZPOgD7NyfPcuYpdl1Nq5cR8LJwyBlAiEA9oLTK5S2KWWjhwn10//B267vSYysGCEPr6wjUD8fsSc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23956}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"picocolors": "^1.0.0", "@babel/highlight": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_8.0.0-alpha.10_1717500014810_0.5760430607906748", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/code-frame", "version": "7.24.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "882fd9e09e8ee324e496bd040401c6f046ef4465", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.24.7.tgz", "fileCount": 5, "integrity": "sha512-BcYH1CVJBO9tvyIZ2jVeXgSIMvGZ2FDRvDdOIVQyuklNKSsx+eppDEBq/g47Ayw+RqNFE+URvOShmf+f/qwAlA==", "signatures": [{"sig": "MEUCICLSvvFaWC27ne8aVJ66C5I9PrJbo8edspzoOgh/MOn1AiEA8zW58rOiavlGxB7femhTW0LqV03x7z652+hLx78e+0w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24063}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"picocolors": "^1.0.0", "@babel/highlight": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.24.7_1717593328559_0.929047913526402", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/code-frame", "version": "8.0.0-alpha.11", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "56d2c9819f0de708f465ad24b462cda86e6c2727", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-8.0.0-alpha.11.tgz", "fileCount": 6, "integrity": "sha512-K+BGQFWY35HqApwKj9/YYHkdkb2p7cTJvyo102e3OTeZqAssEDkG2viLMElAqgVW8Hfy6ZuVkujFXLDF1q3koA==", "signatures": [{"sig": "MEYCIQD9B+wKuz862WdflhHDrWynJEDDZmJG8/kwTCNxLVSbhQIhAOAVbOUr49xTKFr7nSNdkoXP+qxU3JbNNCqzIacn/Opb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23956}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"picocolors": "^1.0.0", "@babel/highlight": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_8.0.0-alpha.11_1717751739123_0.7984083145262808", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/code-frame", "version": "8.0.0-alpha.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "782e3a693fb587f6a191ae9195878a64d3ae7cbb", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-8.0.0-alpha.12.tgz", "fileCount": 6, "integrity": "sha512-2DvtrKU5igZbxo+TuYxrkiOn5LsN16SjL+I2gTrh/tdzG9E8RWOlQxtUcrJAaoUVI2eO88KCCNCRowpz0X+evA==", "signatures": [{"sig": "MEQCIBJ2wqyowPT7ATos/vIW+xO/pL4BhWZ/5TOqwGXfHsmkAiA3kEjVZ49PfsSDDsT5FwDBwnUpSxPlwR31srR06nOgEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23956}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"picocolors": "^1.0.0", "@babel/highlight": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_8.0.0-alpha.12_1722015215558_0.6028697298709096", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/code-frame", "version": "7.25.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "438f2c524071531d643c6f0188e1e28f130cebc7", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-0xZJFNE5XMpENsgfHYTw8FbX4kv53mFLn2i3XPoq69LyhYSCBJtitaHx9QnsVTrsogI4Z3+HtEfZ2/GFPOtf5g==", "signatures": [{"sig": "MEUCIQDK1tuKq6dlkeEbSadPUSfGXHsqsrGJNzBCzAOmgruZ4AIgaT5jJn4uzESpeXY9//KdjvmSdxWwDHB4FXMAtg2q0OQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67153}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"picocolors": "^1.0.0", "@babel/highlight": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.25.7_1727882097914_0.3614732569969812", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/code-frame", "version": "7.25.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "895b6c7e04a7271a0cbfd575d2e8131751914cc7", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-z88xeGxnzehn2sqZ8UdGQEvYErF1odv2CftxInpSYJt6uHuPe9YjahKZITGs3l5LeI9d2ROG+obuDAoSlqbNfQ==", "signatures": [{"sig": "MEQCIGTMYYblIlv61vBnyx/Z++6UxWX+k9T68gA2EVDcfD9+AiAcJnOPBGG1Ld+2O+JIYhY2AaDZk6rPTi6HHa8nrlmwyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24063}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"picocolors": "^1.0.0", "@babel/highlight": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.25.9_1729610475935_0.3814183499612245", "host": "s3://npm-registry-packages"}}, "7.26.0": {"name": "@babel/code-frame", "version": "7.26.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.26.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "9374b5cd068d128dac0b94ff482594273b1c2815", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.26.0.tgz", "fileCount": 5, "integrity": "sha512-INCKxTtbXtcNbUZ3YXutwMpEleqttcswhAdee7dhuoVrD2cnuc3PqtERBtxkX5nziX9vnBL8WXmSGwv8CuPV6g==", "signatures": [{"sig": "MEQCIFuxue0ApfP2gWfWANBO+jiCIjcivzbetRjsQoU8ljOCAiAppqYYOYpPAtiZgsVlq9Zgc2QIL4K1e4CnWq0f6JV4nQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33142}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"js-tokens": "^4.0.0", "picocolors": "^1.0.0", "@babel/helper-validator-identifier": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.26.0_1729863006311_0.7665332363511035", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/code-frame", "version": "8.0.0-alpha.13", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "9e7d7380ef5b89a4ddfbaf9290bd4049d9b2012b", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-XHZI7g8olZQC9HANu8xmsV55ir/AiTXW1ehzk3fsoFewfZXFjP+XJ5ppkgZFGyyXncH3+1FRzn2vc8xpTOH0sg==", "signatures": [{"sig": "MEUCIQDsmVCOboQTtUDEc/XLX+dKfiaL3IoT1rzYNjx3pguwfwIgbHfoGucCtdKXI2uR/sLgtlOKxTya0yuerIQi3ZTxVOM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36504}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"js-tokens": "^8.0.0", "picocolors": "^1.0.0", "@babel/helper-validator-identifier": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_8.0.0-alpha.13_1729864428397_0.8316318227237787", "host": "s3://npm-registry-packages"}}, "7.26.2": {"name": "@babel/code-frame", "version": "7.26.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.26.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "4b5fab97d33338eff916235055f0ebc21e573a85", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.26.2.tgz", "fileCount": 5, "integrity": "sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==", "signatures": [{"sig": "MEYCIQCkTbdAlv3Lo3ZUojo/G7CzjzTZ4EJ01Hth1rTH1h7cqQIhAKA1JWz44dqI5ODDaM+fH+eDbs+M21uRWTQo00qFJD5H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33160}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"js-tokens": "^4.0.0", "picocolors": "^1.0.0", "@babel/helper-validator-identifier": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.26.2_1730310371547_0.10158680584745627", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/code-frame", "version": "8.0.0-alpha.14", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "65bf215963a5dd59a5ff0603abffc280bb35098d", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-96RgKvnmCLMTcV4ZrxmkPrxchWeEam/FA66QJBAt6lz/9V+9qHP29ERNB6ecf2EjwUyPoGWD0zIueNq//2PZQA==", "signatures": [{"sig": "MEYCIQCO8GZBx7F+Zo78tCR6VFQZHzSRePJ9CnT/SauOAYvFCQIhAMufJX6Z5jUfabnpMVuzNEQF2URESXqc59/KKV/lbsMW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36522}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"js-tokens": "^8.0.0", "picocolors": "^1.0.0", "@babel/helper-validator-identifier": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_8.0.0-alpha.14_1733504020526_0.12956388322176515", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/code-frame", "version": "8.0.0-alpha.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "2be2120b12933630895651be9e707ad834f60d6e", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-616SNeaKlbCKN87Q8TKQ5IVwst4c79tVqsgioWvg3cKyD6zWKJo8QAOfs3nGD77wps38rUHeISJFNDzsK5rHDg==", "signatures": [{"sig": "MEUCICcGe/2fPjoaCblb6QbOB1earFmA/3FrKxGr3LjPeBtnAiEA2++lxcvnCjkwV1hmRQ3V9QGQADHGgWrQODheNUbOCBw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36522}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"js-tokens": "^8.0.0", "picocolors": "^1.0.0", "@babel/helper-validator-identifier": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_8.0.0-alpha.15_1736529843698_0.6319031030962703", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/code-frame", "version": "8.0.0-alpha.16", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "3eff73c0a5d0f74a6cdaf8d30a578079e7a83dbf", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-QJyNWnKl+ivQtyF221FUUeVDpgqQzsTzfsBbD1Dj2b8SXS4f9H/lRh7U2jrjOX+JZ0eGn9efAjAgqJYluDXmMQ==", "signatures": [{"sig": "MEQCIC/MqgahPq3gF903hTOKKzyBUtGh/d+FznwpQPBskXnaAiAtLGITRVoTjaC3Iw03QJBWNoyJ9DpWfbLrHCD7zLtYrg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36522}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"js-tokens": "^8.0.0", "picocolors": "^1.0.0", "@babel/helper-validator-identifier": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_8.0.0-alpha.16_1739534321616_0.7239771825183035", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/code-frame", "version": "8.0.0-alpha.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "1f5733bf3d5c156c718aff8dc2cde362bd235601", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-1/AC06RmGAL/Q7g7aCGbTEn9EXyLgR3MqmWV1F4zgDmRhYG8Ewb//QIz2nZmRIHnWhXqyHKTUGCCxq/VcLv7bQ==", "signatures": [{"sig": "MEUCIQDhbXcsRwOmUpBcrsk5d30hs2WrXCPmb/07yS2XPRHJkQIgKIWkJSW5Dg6R+KC3WHT3G0G9CH3ZJB7f/TPDzi4WE4A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36522}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"js-tokens": "^8.0.0", "picocolors": "^1.0.0", "@babel/helper-validator-identifier": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_8.0.0-alpha.17_1741717472136_0.7414458815871883", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/code-frame", "version": "7.27.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/code-frame@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "dist": {"shasum": "200f715e66d52a23b221a9435534a91cc13ad5be", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "signatures": [{"sig": "MEUCIQD72KgHb2i2MBRJ/Hk4gQNohJN6slpOzS5TvCbZ4F60uQIgCFiJcvxEYZYMdvx8iEkqrd+inOCbtfPrz/3HypYWmto=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 33160}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "directories": {}, "dependencies": {"js-tokens": "^4.0.0", "picocolors": "^1.1.1", "@babel/helper-validator-identifier": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"strip-ansi": "^4.0.0", "import-meta-resolve": "^4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/code-frame_7.27.1_1746025711517_0.02929143332896378", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/code-frame", "version": "8.0.0-beta.0", "description": "Generate errors that contain a code frame that point to source locations.", "author": "The Babel Team (https://babel.dev/team)", "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-code-frame"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-validator-identifier": "^8.0.0-beta.0", "js-tokens": "^8.0.0", "picocolors": "^1.1.1"}, "devDependencies": {"import-meta-resolve": "^4.1.0", "strip-ansi": "^4.0.0"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/code-frame@8.0.0-beta.0", "dist": {"shasum": "fea256494d72f482267d2b965e7e874586f5360b", "integrity": "sha512-Bws+VjMNVNqB3HXD6UxscrDnnIcsUp+SzX5nw3KvxEsLOuVTClyP2/G/BPrl6xcICS0QcB6SthW1+DclN717DQ==", "tarball": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-8.0.0-beta.0.tgz", "fileCount": 6, "unpackedSize": 36507, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDuzbyG6S1+eTlwKD0Odwsx1iB+YWiSEYc15WqVWwuMCAIhAN6v7tKsts4V7ACyxKO5xLa8Ro2AzD7ZeKUivjYY0rGx"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/code-frame_8.0.0-beta.0_1748620244583_0.06619819549056793"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:34:05.205Z", "modified": "2025-05-30T15:50:44.924Z", "7.0.0-beta.4": "2017-10-30T18:34:05.205Z", "7.0.0-beta.5": "2017-10-30T20:55:53.706Z", "7.0.0-beta.31": "2017-11-03T20:03:06.690Z", "7.0.0-beta.32": "2017-11-12T13:32:57.085Z", "7.0.0-beta.33": "2017-12-01T14:17:03.737Z", "7.0.0-beta.34": "2017-12-02T14:38:57.119Z", "7.0.0-beta.35": "2017-12-14T21:47:29.867Z", "7.0.0-beta.36": "2017-12-25T19:04:12.961Z", "7.0.0-beta.37": "2018-01-08T16:02:16.986Z", "7.0.0-beta.38": "2018-01-17T16:31:35.278Z", "7.0.0-beta.39": "2018-01-30T20:27:22.199Z", "7.0.0-beta.40": "2018-02-12T16:41:53.906Z", "7.0.0-beta.41": "2018-03-14T16:25:42.283Z", "7.0.0-beta.42": "2018-03-15T20:50:16.725Z", "7.0.0-beta.43": "2018-04-02T16:48:06.732Z", "7.0.0-beta.44": "2018-04-02T22:19:49.142Z", "7.0.0-beta.45": "2018-04-23T01:55:43.419Z", "7.0.0-beta.46": "2018-04-23T04:30:08.389Z", "7.0.0-beta.47": "2018-05-15T00:07:34.518Z", "7.0.0-beta.48": "2018-05-24T19:20:54.851Z", "7.0.0-beta.49": "2018-05-25T16:00:37.267Z", "7.0.0-beta.50": "2018-06-12T19:46:49.413Z", "7.0.0-beta.51": "2018-06-12T21:19:15.875Z", "7.0.0-beta.52": "2018-07-06T00:59:11.949Z", "7.0.0-beta.53": "2018-07-11T13:40:01.472Z", "7.0.0-beta.54": "2018-07-16T17:59:51.728Z", "7.0.0-beta.55": "2018-07-28T22:06:56.147Z", "7.0.0-beta.56": "2018-08-04T01:03:30.905Z", "7.0.0-rc.0": "2018-08-09T15:56:48.939Z", "7.0.0-rc.1": "2018-08-09T20:06:40.826Z", "7.0.0-rc.2": "2018-08-21T19:22:42.888Z", "7.0.0-rc.3": "2018-08-24T18:06:39.079Z", "7.0.0-rc.4": "2018-08-27T16:42:49.375Z", "7.0.0": "2018-08-27T21:42:00.298Z", "7.5.5": "2019-07-17T21:21:26.592Z", "7.8.0": "2020-01-12T00:16:04.711Z", "7.8.3": "2020-01-13T21:41:43.457Z", "7.10.1": "2020-05-27T22:07:42.415Z", "7.10.3": "2020-06-19T20:54:25.624Z", "7.10.4": "2020-06-30T13:12:14.794Z", "7.12.11": "2020-12-15T23:59:15.550Z", "7.12.13": "2021-02-03T01:10:04.185Z", "7.14.5": "2021-06-09T23:12:21.758Z", "7.15.8": "2021-10-06T20:54:55.170Z", "7.16.0": "2021-10-29T23:47:40.990Z", "7.16.7": "2021-12-31T00:22:17.638Z", "7.18.6": "2022-06-27T19:50:07.729Z", "7.21.4": "2023-03-31T09:01:51.008Z", "7.21.4-esm": "2023-04-04T14:09:34.002Z", "7.21.4-esm.1": "2023-04-04T14:21:26.232Z", "7.21.4-esm.2": "2023-04-04T14:39:27.360Z", "7.21.4-esm.3": "2023-04-04T14:56:16.210Z", "7.21.4-esm.4": "2023-04-04T15:13:27.786Z", "7.22.5": "2023-06-08T18:21:25.442Z", "8.0.0-alpha.0": "2023-07-20T14:00:01.976Z", "8.0.0-alpha.1": "2023-07-24T17:52:08.600Z", "7.22.10": "2023-08-07T17:25:20.470Z", "8.0.0-alpha.2": "2023-08-09T15:15:01.844Z", "7.22.13": "2023-08-28T17:01:57.853Z", "8.0.0-alpha.3": "2023-09-26T14:57:05.126Z", "8.0.0-alpha.4": "2023-10-12T02:06:25.507Z", "7.23.4": "2023-11-20T14:22:15.556Z", "7.23.5": "2023-11-29T10:25:37.659Z", "8.0.0-alpha.5": "2023-12-11T15:19:04.028Z", "8.0.0-alpha.6": "2024-01-26T16:14:14.517Z", "8.0.0-alpha.7": "2024-02-28T14:05:03.637Z", "7.24.1": "2024-03-19T09:49:17.644Z", "7.24.2": "2024-03-19T18:07:35.501Z", "8.0.0-alpha.8": "2024-04-04T13:19:56.617Z", "7.24.6": "2024-05-24T12:24:38.944Z", "8.0.0-alpha.9": "2024-06-03T14:04:25.031Z", "8.0.0-alpha.10": "2024-06-04T11:20:14.949Z", "7.24.7": "2024-06-05T13:15:28.697Z", "8.0.0-alpha.11": "2024-06-07T09:15:39.313Z", "8.0.0-alpha.12": "2024-07-26T17:33:35.719Z", "7.25.7": "2024-10-02T15:14:58.147Z", "7.25.9": "2024-10-22T15:21:16.203Z", "7.26.0": "2024-10-25T13:30:06.473Z", "8.0.0-alpha.13": "2024-10-25T13:53:48.603Z", "7.26.2": "2024-10-30T17:46:11.750Z", "8.0.0-alpha.14": "2024-12-06T16:53:40.721Z", "8.0.0-alpha.15": "2025-01-10T17:24:03.909Z", "8.0.0-alpha.16": "2025-02-14T11:58:41.811Z", "8.0.0-alpha.17": "2025-03-11T18:24:32.339Z", "7.27.1": "2025-04-30T15:08:31.722Z", "8.0.0-beta.0": "2025-05-30T15:50:44.750Z"}, "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-code-frame"}, "description": "Generate errors that contain a code frame that point to source locations.", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"fangbot": true, "flumpus-dev": true}}