const { Events } = require('discord.js');
const { getTeamManager } = require('../../database/teamManager');

// Store voice session data
const voiceSessions = new Map();

module.exports = {
    name: Events.VoiceStateUpdate,
    async execute(oldState, newState) {
        try {
            const userId = newState.id;
            const guild = newState.guild;

            // Skip if user is a bot
            if (newState.member.user.bot) return;

            const now = Date.now();

            // User joined a voice channel
            if (!oldState.channelId && newState.channelId) {
                await handleVoiceJoin(userId, newState, now);
            }
            // User left a voice channel
            else if (oldState.channelId && !newState.channelId) {
                await handleVoiceLeave(userId, oldState, now);
            }
            // User switched voice channels
            else if (oldState.channelId && newState.channelId && oldState.channelId !== newState.channelId) {
                await handleVoiceLeave(userId, oldState, now);
                await handleVoiceJoin(userId, newState, now);
            }
            // User's voice state changed (mute/unmute, deaf/undeaf)
            else if (oldState.channelId && newState.channelId) {
                await handleVoiceStateChange(userId, oldState, newState, now);
            }

        } catch (error) {
            console.error('Error in voiceStateUpdate event:', error);
        }
    }
};

async function handleVoiceJoin(userId, voiceState, timestamp) {
    try {
        // Check if user should earn points (not muted and not alone)
        const shouldEarnPoints = await shouldUserEarnVoicePoints(voiceState);
        
        voiceSessions.set(userId, {
            channelId: voiceState.channelId,
            joinTime: timestamp,
            lastPointCheck: timestamp,
            earnPoints: shouldEarnPoints
        });

        console.log(`🎤 ${voiceState.member.user.tag} joined voice channel ${voiceState.channel.name} (earning points: ${shouldEarnPoints})`);

    } catch (error) {
        console.error('Error handling voice join:', error);
    }
}

async function handleVoiceLeave(userId, voiceState, timestamp) {
    try {
        const session = voiceSessions.get(userId);
        if (!session) return;

        // Calculate total time in voice
        const totalTime = timestamp - session.joinTime;
        const timeInSeconds = Math.floor(totalTime / 1000);

        if (timeInSeconds > 0 && session.earnPoints) {
            // Award points for voice activity
            const teamManager = getTeamManager();
            await teamManager.updateVoiceActivity(userId, timeInSeconds);
            
            console.log(`🎤 ${voiceState.member.user.tag} left voice after ${Math.round(timeInSeconds / 60)} minutes`);
        }

        voiceSessions.delete(userId);

    } catch (error) {
        console.error('Error handling voice leave:', error);
    }
}

async function handleVoiceStateChange(userId, oldState, newState, timestamp) {
    try {
        const session = voiceSessions.get(userId);
        if (!session) return;

        const shouldEarnPoints = await shouldUserEarnVoicePoints(newState);
        const wasEarningPoints = session.earnPoints;

        // If earning status changed, process the previous session
        if (wasEarningPoints !== shouldEarnPoints) {
            if (wasEarningPoints) {
                // User was earning points but now isn't (got muted or channel became empty)
                const timeInSession = timestamp - session.lastPointCheck;
                const timeInSeconds = Math.floor(timeInSession / 1000);

                if (timeInSeconds > 0) {
                    const teamManager = getTeamManager();
                    await teamManager.updateVoiceActivity(userId, timeInSeconds);
                }
            }

            // Update session
            session.earnPoints = shouldEarnPoints;
            session.lastPointCheck = timestamp;

            console.log(`🎤 ${newState.member.user.tag} voice state changed (earning points: ${shouldEarnPoints})`);
        }

    } catch (error) {
        console.error('Error handling voice state change:', error);
    }
}

async function shouldUserEarnVoicePoints(voiceState) {
    try {
        // Don't earn points if user is muted (server muted or self muted)
        if (voiceState.mute || voiceState.selfMute) {
            return false;
        }

        // Don't earn points if user is alone in the channel
        const channel = voiceState.channel;
        if (!channel) return false;

        // Count non-bot members in the channel
        const nonBotMembers = channel.members.filter(member => !member.user.bot);
        if (nonBotMembers.size <= 1) {
            return false;
        }

        return true;

    } catch (error) {
        console.error('Error checking if user should earn voice points:', error);
        return false;
    }
}

// Periodic check to award points for ongoing voice sessions
setInterval(async () => {
    try {
        const now = Date.now();
        const teamManager = getTeamManager();

        for (const [userId, session] of voiceSessions.entries()) {
            if (!session.earnPoints) continue;

            const timeSinceLastCheck = now - session.lastPointCheck;
            const timeInSeconds = Math.floor(timeSinceLastCheck / 1000);

            // Award points every 5 minutes (300 seconds)
            if (timeInSeconds >= 300) {
                try {
                    await teamManager.updateVoiceActivity(userId, timeInSeconds);
                    session.lastPointCheck = now;
                } catch (error) {
                    console.error(`Error updating voice activity for user ${userId}:`, error);
                }
            }
        }

    } catch (error) {
        console.error('Error in voice activity periodic check:', error);
    }
}, 60000); // Check every minute

// Clean up sessions on bot restart
process.on('SIGINT', () => {
    console.log('🎤 Cleaning up voice sessions...');
    voiceSessions.clear();
});

process.on('SIGTERM', () => {
    console.log('🎤 Cleaning up voice sessions...');
    voiceSessions.clear();
});
