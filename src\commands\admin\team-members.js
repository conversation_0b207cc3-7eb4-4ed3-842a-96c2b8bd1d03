const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, PermissionFlagsBits, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const { getTeamManager } = require('../../database/teamManager');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('team-members')
        .setDescription('Show all members in a specific team (Admin only)')
        .addStringOption(option =>
            option.setName('team_name')
                .setDescription('The name of the team')
                .setRequired(true)
                .setAutocomplete(true))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    async autocomplete(interaction) {
        try {
            const focusedValue = interaction.options.getFocused();
            const teamManager = getTeamManager();
            const teams = teamManager.getAllTeams();
            
            const filtered = teams.filter(team => 
                team.name.toLowerCase().includes(focusedValue.toLowerCase())
            ).slice(0, 25); // Discord limits to 25 choices

            await interaction.respond(
                filtered.map(team => ({
                    name: `${team.name} (${team.total_points} points, Level ${team.level})`,
                    value: team.name
                }))
            );
        } catch (error) {
            console.error('Error in team-members autocomplete:', error);
            await interaction.respond([]);
        }
    },

    async execute(interaction) {
        try {
            // Check if user has administrator permissions
            if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ Access Denied')
                    .setDescription('You need Administrator permissions to use this command.')
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            const teamName = interaction.options.getString('team_name');
            const teamManager = getTeamManager();

            const result = await teamManager.getTeamMembers(teamName);

            if (!result.success) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ Team Not Found')
                    .setDescription(result.message)
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            const team = result.team;
            const members = result.members;

            if (members.length === 0) {
                const emptyEmbed = new EmbedBuilder()
                    .setColor('#FFA500')
                    .setTitle(`👥 Team Members: ${team.name}`)
                    .setDescription('This team has no members yet.')
                    .addFields(
                        { name: '🏆 Team Points', value: `${team.total_points}`, inline: true },
                        { name: '⭐ Team Level', value: `${team.level}`, inline: true },
                        { name: '📅 Created', value: `<t:${team.created_at}:F>`, inline: true }
                    )
                    .setTimestamp();

                return await interaction.reply({ embeds: [emptyEmbed] });
            }

            // Pagination setup
            const membersPerPage = 10;
            const totalPages = Math.ceil(members.length / membersPerPage);
            let currentPage = 0;

            const generateEmbed = (page) => {
                const start = page * membersPerPage;
                const end = start + membersPerPage;
                const pageMembers = members.slice(start, end);

                const memberList = pageMembers.map((member, index) => {
                    const globalRank = start + index + 1;
                    return `**${globalRank}.** <@${member.id}>\n` +
                           `   🏆 ${member.points} points | ⭐ Level ${member.level}\n` +
                           `   🎤 ${Math.round(member.voice_time / 60)} min voice time`;
                }).join('\n\n');

                const embed = new EmbedBuilder()
                    .setColor('#0099FF')
                    .setTitle(`👥 Team Members: ${team.name}`)
                    .setDescription(memberList)
                    .addFields(
                        { name: '🏆 Team Points', value: `${team.total_points}`, inline: true },
                        { name: '⭐ Team Level', value: `${team.level}`, inline: true },
                        { name: '👥 Total Members', value: `${members.length}`, inline: true }
                    )
                    .setFooter({ text: `Page ${page + 1} of ${totalPages} • ${members.length} total members` })
                    .setTimestamp();

                return embed;
            };

            const generateButtons = (page) => {
                const row = new ActionRowBuilder();

                if (totalPages > 1) {
                    row.addComponents(
                        new ButtonBuilder()
                            .setCustomId('team_members_prev')
                            .setLabel('◀️ Previous')
                            .setStyle(ButtonStyle.Secondary)
                            .setDisabled(page === 0),
                        new ButtonBuilder()
                            .setCustomId('team_members_next')
                            .setLabel('Next ▶️')
                            .setStyle(ButtonStyle.Secondary)
                            .setDisabled(page === totalPages - 1)
                    );
                }

                return row.components.length > 0 ? [row] : [];
            };

            const embed = generateEmbed(currentPage);
            const components = generateButtons(currentPage);

            const response = await interaction.reply({
                embeds: [embed],
                components: components
            });

            if (totalPages > 1) {
                const collector = response.createMessageComponentCollector({
                    time: 300000 // 5 minutes
                });

                collector.on('collect', async (buttonInteraction) => {
                    if (buttonInteraction.user.id !== interaction.user.id) {
                        await buttonInteraction.reply({
                            content: 'Only the command user can navigate pages.',
                            ephemeral: true
                        });
                        return;
                    }

                    if (buttonInteraction.customId === 'team_members_prev') {
                        currentPage = Math.max(0, currentPage - 1);
                    } else if (buttonInteraction.customId === 'team_members_next') {
                        currentPage = Math.min(totalPages - 1, currentPage + 1);
                    }

                    const newEmbed = generateEmbed(currentPage);
                    const newComponents = generateButtons(currentPage);

                    await buttonInteraction.update({
                        embeds: [newEmbed],
                        components: newComponents
                    });
                });

                collector.on('end', async () => {
                    try {
                        await response.edit({ components: [] });
                    } catch (error) {
                        // Message might have been deleted
                        console.log('Could not remove buttons from team-members command');
                    }
                });
            }

        } catch (error) {
            console.error('Error in team-members command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Command Error')
                .setDescription('An error occurred while retrieving team members. Please try again later.')
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
