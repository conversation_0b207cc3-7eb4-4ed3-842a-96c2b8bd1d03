{"_id": "ci-info", "_rev": "41-df6b1370be3ffe5c163a12a29556b871", "name": "ci-info", "dist-tags": {"latest": "4.2.0"}, "versions": {"1.0.0": {"name": "ci-info", "version": "1.0.0", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@1.0.0", "maintainers": [{"name": "watson", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "dc5285f2b4e251821683681c381c3388f46ec534", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-1.0.0.tgz", "integrity": "sha512-ew7IacPPJ0TskWg2F9XOYpHEyTn/WGYc5PID1k8kGci8/MLuJVVajQ2RrzckPPkORD6eoE9O6IsrUUZc3j6nHQ==", "signatures": [{"sig": "MEUCIQD16v5eyzwp/oyjUoCrrayfWeCeX72IklDHI9gtb6XJdwIgThoeLP0fU6Rq0bi+A+S9eBy4HgG2uHoVxc+JFcuNfVk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "dc5285f2b4e251821683681c381c3388f46ec534", "gitHead": "b9b1629b602f09ff8ee0b6158bfd16bb5af432b3", "scripts": {"test": "standard && node test.js"}, "_npmUser": {"name": "watson", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "2.15.9", "coordinates": [55.68768499999999, 12.5955698], "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "4.5.0", "dependencies": {}, "devDependencies": {"standard": "^8.4.0", "clear-require": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info-1.0.0.tgz_1476979279035_0.6885495139285922", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.1": {"name": "ci-info", "version": "1.0.1", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@1.0.1", "maintainers": [{"name": "watson", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "e5913b63f7e1fda8db99a3125d45cbabcde55ba9", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-1.0.1.tgz", "integrity": "sha512-u+kVhyCfR37aSKEneBwRcsff8tCLIyBvngsqL/fgOoWJsBhSFVVRaXWUI3HuAQKu8cLawFFRxNxpDUktZUXScw==", "signatures": [{"sig": "MEUCIQCqUgOoRZiLSOe0bhc0hz+zsCP3csLJrmed4cU1yC8mCgIgEYyhNc5y+P834RRfMrszeAuPfLyqMWrUo0Kg/mUK3JU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "aac11e070bf228af71192d8720c2dff6ad2b56ba", "scripts": {"test": "standard && node test.js"}, "_npmUser": {"name": "watson", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "5.3.0", "coordinates": [55.6773705, 12.5614183], "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {}, "devDependencies": {"standard": "^8.4.0", "clear-require": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info-1.0.1.tgz_1504539054919_0.6077480302192271", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "ci-info", "version": "1.1.0", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@1.1.0", "maintainers": [{"name": "watson", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "37f6e917002864c263af2579cedcb4abb24fa467", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-1.1.0.tgz", "integrity": "sha512-Kdy57gJlQwVmleFb/XKd1ZJ/iSgYGlh2sKeT8uY4t544qv27FdcnWvb1BH8LKok2lgdKa/w5qHlvAOqz2NR0bA==", "signatures": [{"sig": "MEQCIFG1gZMPRd/voG/CBkMEkghBNb4nflsiak4vlzRsG0irAiBOrdT3n3h47G0HfSi5z7AcwgFT+GM8xZgy6ucmrpX0Gg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "4142f07e702c4b9aeb9d7161df815c2043d2f96f", "scripts": {"test": "standard && node test.js"}, "_npmUser": {"name": "watson", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "5.3.0", "coordinates": [55.681141, 12.5643633], "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {}, "devDependencies": {"standard": "^8.4.0", "clear-require": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info-1.1.0.tgz_1504611213844_0.9689220797736198", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "ci-info", "version": "1.1.1", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@1.1.1", "maintainers": [{"name": "watson", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "47b44df118c48d2597b56d342e7e25791060171a", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-1.1.1.tgz", "integrity": "sha512-vHDDF/bP9RYpTWtUhpJRhCFdvvp3iDWvEbuDbWgvjUrNGV1MXJrE0MPcwGtEled04m61iwdBLUIHZtDgzWS4ZQ==", "signatures": [{"sig": "MEYCIQD6cSVwpE22dfC2IcGP/n3MIjN90LnDckDMnva/oCbQ8wIhAJbJsmGt7CWlg72ha3jbtuVUzoeA2oOKfw+0dMjSmOLf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "e75edeafca0330955d71ee0eb0ea4af412d63003", "scripts": {"test": "standard && node test.js"}, "_npmUser": {"name": "watson", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "5.4.0", "coordinates": [55.6773705, 12.5614183], "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {}, "devDependencies": {"standard": "^10.0.3", "clear-require": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info-1.1.1.tgz_1504612390657_0.2857714928686619", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "ci-info", "version": "1.1.2", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@1.1.2", "maintainers": [{"name": "watson", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "03561259db48d0474c8bdc90f5b47b068b6bbfb4", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-1.1.2.tgz", "integrity": "sha512-uTGIPNx/nSpBdsF6xnseRXLLtfr9VLqkz8ZqHXr3Y7b6SftyRxBGjwMtJj1OhNbmlc1wZzLNAlAcvyIiE8a6ZA==", "signatures": [{"sig": "MEQCIFy6hcVQ2PiywM8GmQbKbN7RfSn1eC+L9Q117Q7Ig8oHAiAo/u0BvKfqE+zOy6dXTGlUuPLDTRwgTTr33+eRPVXu/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "bd4e2a1e4ff616764c4967e83f3e3b3c415ae476", "scripts": {"test": "standard && node test.js"}, "_npmUser": {"name": "watson", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "5.5.1", "coordinates": [55.777569, 12.589702], "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "9.2.0", "dependencies": {}, "devDependencies": {"standard": "^10.0.3", "clear-require": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info-1.1.2.tgz_1510945915514_0.49366038176231086", "host": "s3://npm-registry-packages"}}, "1.1.3": {"name": "ci-info", "version": "1.1.3", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@1.1.3", "maintainers": [{"name": "watson", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "710193264bb05c77b8c90d02f5aaf22216a667b2", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-1.1.3.tgz", "fileCount": 4, "integrity": "sha512-SK/846h/Rcy8q9Z9CAwGBLfCJ6EkjJWdpelWDufQpqVDYq2Wnnv8zlSO6AMQap02jvhVruKKpEtQOufo3pFhLg==", "signatures": [{"sig": "MEUCIBb8W+d15IXXHN9ONxY9f78gCsueiLwD9/UciG0w4GW9AiEA1kX2emBlQv2rge7pzvyKx7VkPkcThxfl/2s1JvN/uE8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6120}, "main": "index.js", "gitHead": "a8d33c93296bcca821b0b9b56d4567dcf5393235", "scripts": {"test": "standard && node test.js"}, "_npmUser": {"name": "watson", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "5.7.1", "coordinates": [55.777613, 12.589943], "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "9.6.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"standard": "^10.0.3", "clear-require": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_1.1.3_1520884277112_0.3492393126009503", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "ci-info", "version": "1.2.0", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@1.2.0", "maintainers": [{"name": "watson", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "8d1e9cb4051482ff70cd52a89b4b095a8fb635f6", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-1.2.0.tgz", "fileCount": 5, "integrity": "sha512-U4aoLsSz44FhOyZ2E7bCufaBr2IUzNYujBd+b9vHiFH7SUzIhKcD94PQP5QSFn7ngPof6OF2yPk4/hygqwMJhA==", "signatures": [{"sig": "MEYCIQD7BP7LHE/o4zM0UTTWdfFe1m7d11GgUaxJR5kBfqUF5QIhAP1VvqAcHxnYuDh0HEZrhjgvRa9yrQzSH92EobY7qeg6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7142, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbcpKdCRA9TVsSAnZWagAA5hQP/jCwmJ7c7vBO3N9GjA2M\nUqSjQoWKJEANIO2j3AovAFU0KlzLzxXHSjYa26oOCJ9uzwc1Lv6hM42jo8kS\n+uPi9+fGgk2eTbEtNTY1pjqDHpxHtgiFl/edXfRLg1ppWnMDtjMDF2EHREOS\nffZ1SQD5o47rIRuGBbjOkoZnE9Nf0EfyEvpXaAoJfJCfHzdTiHLj/84hjGE3\nsFGkd4ldtTGaCuKhf+DYsEm/L9KcZtNr3rOUfoKPhKG4iAUOTLS9LDavTDL+\nJS3hefGZuvmNHNnSaV/VVvodFRa7Ojk+AcLX6Ka4fbggLgNmLLjRkE+2SAXu\nMQjpXlDnrysQi+1swp8sM4bGqaknAe6noMqH2q9Z+CcTr2jerud3Wt5FJtpf\nzQP+zu42wSZz+SkHfMoJMD6pD8IRtBDoSW3m7NBZYKjO7Z4r3XCZaH6wG7kC\nAKsu8pq4mnr0cWjc1kNXyIGOc+VPkQD1dsSf3Szwso8yYRr7j8xuPHtD5M8n\n5p6/CWDoPBTK7qHFoBfrz1TI7sgqqKrUaKO6Ty1N7IwW48YpgkJ+tesfLRSr\nP1p7tCMkbnZh0MZtWPZbHxCv99pyAz9gHAJtbU4tsZ6FU0dhXRqjoSox8+wz\nm24hguI1KPEobNPfZW7IN45GYI050vwVevulylXXg5ilWqVAyI9PZuPPbTjy\nW0co\r\n=Coai\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "cf3fbe16d1b1dc4a14ed41267cc5439fce61b557", "scripts": {"test": "standard && node test.js"}, "_npmUser": {"name": "watson", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "6.2.0", "coordinates": [55.6809148, 12.5644041], "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"standard": "^10.0.3", "clear-require": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_1.2.0_1534235293084_0.09870438557547523", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "ci-info", "version": "1.3.0", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@1.3.0", "maintainers": [{"name": "watson", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "ea8219b0355a58692b762baf1cdd76ceb4503283", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-1.3.0.tgz", "fileCount": 5, "integrity": "sha512-mPdvoljUhH3Feai3dakD3bwYl/8I0tSo16Ge2W+tY88yfYDKGVnXV2vFxZC8VGME01CYp+DaAZnE93VHYVapnA==", "signatures": [{"sig": "MEUCIQCoUlPFeFj7qPQl3vM46oC0aB/pU5fW3en4ZkFWBnrv9gIgEW2Dz8WwRoZVhyiHxdRaqMVrjkCvqfkSddvLfRy0tos=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbc0eTCRA9TVsSAnZWagAAFQoP/3IS1P2bKTht9EcFb353\nd/uNyp+EKFKb2gOKPG5cZWiFZOSnfNEsBf84sdR4g8WcGIHyvejV43H8NTNq\nVC2AjlP/aWcRs4Ocmr8olkl6+p6yM/H2zZRLWnalPBytoKiqtxmLsCqwZAy+\n7thjekY5Buyh088udaNYFHtz68lT0DG+a1LbvVMWL5l2RNImXMgLXCpEk7sd\nS5e8RzJSwVrtTjm4lqyg9EGTY7QBS5JcRxsEoHx7rRP9j/eKIUO+eI+uC50j\nG3YjQJgZ2hAVPxIsWLU/zpjIvlTNKuS/cnMF1hk6ND9rs5Z4lJlEgAAIu9rN\nNGRdGJ2LcYn3YntzkIorRB8v+UfiVTWBTSom9XgA+VwaSLcA+o6IIb46mv3K\nk29lWXlgkyds2+9pifGcb7UfIDWr/2uejuodsjiyAVtJJDwh5BW59XrMkM81\nsFNgSR3z94UBXAQUd3hG7pPxXNiApAiV03iDL+qI42VAG1IPfx66XUMwtnVw\nEAf/B1G5s98862EJIS5qrRl/fjXi/bf8+DyqjnGOnqk2FgRKpvxnhEMet75K\nHXp/PwX+4vLG2j1bGC062GkqEbfGG9fASUG57aD/Zp8RIUQbhSjs4LsiIKST\nVRyUMbUY34gNePaojZcEYMwixLlk/MxRm6OuVH6iZevbVJ9F5IVdjCkVNf1/\n0xSt\r\n=70AB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "fc23ab9ad05a32d690eacdef5821d691bfc48a65", "scripts": {"test": "standard && node test.js"}, "_npmUser": {"name": "watson", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "6.2.0", "coordinates": [55.777197, 12.592028], "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"standard": "^11.0.1", "clear-require": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_1.3.0_1534281618862_0.3978395804022148", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "ci-info", "version": "1.3.1", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@1.3.1", "maintainers": [{"name": "watson", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "da21bc65a5f0d0d250c19a169065532b42fa048c", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-1.3.1.tgz", "fileCount": 4, "integrity": "sha512-l4wK/SFEN8VVTQ9RO1I5yzIL2vw1w6My29qA6Gwaec80QeHxfXbruuUWqn1knyMoJn/X5kav3zVY1TlRHSKeIA==", "signatures": [{"sig": "MEYCIQCpGf35yVM9TbtjZZJPGOUeM2AcbGRrTOHauLFhNqOsuwIhALnHnFmHt0Pks2FQ1fbG3S2ARd7HB1X96mE83brkq9vX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbdZx+CRA9TVsSAnZWagAAKDMP/1ETs7jSSHS0tUlOPCZx\nzFUqUhtSQoxaRZ3xQLI5C2OihxCdYVdXMRCaDpBUebLBHLl9unR8zMtdS3kp\nrGw1xWi3EbLfraRXceHh5CMHJNM2HUf37VOxcqMOlv3P0zt2aLjrx+radMCm\nSlIbAtaUlNs4rLzHITK5yXCZObOVnK3b1123OKWkE8SsKkOWYYlJ1Ginuq3g\nW/CrDswcgbNtN9Gzb9cXJeaZuuJ5snrQV2xkR/4XHbUFmVGM01Fs290uZ9Vv\nKQddshENl+n/BhsROsqsxlyg208enGVZYZozeJYMk1sH9MHigNUYJjteAzEo\nQJ7+2DrODwAZl7sQcAqk0QJ8Re0rv0zSJqaQaxXUphS6hOQZ4pkXSLJx23Gh\n0wJ6/ONjzm/T9uxydcWawLitFgtHLSrwJKMD0SN30bzWsH2P7Qq6q4jfFLEx\n0qENy0g0ZK++eaSyuO48gtNLxpfeR2WV4ESBCAyCtC6aFsL73mem26JfriPj\nTz8rQ+QCK91gllw58kl/Hr5CIw/WwDSDfoGQ7lacUCn80LUvLvPAa9g0aMfH\nMpTndzYQOebI7BR2Y0sJmszKiboz753rfihk+0Ljwy2Be9BFJ444duYjiIfu\ndFxBVPxPyTdSvAD7oAuwQ1QOoDi8YAbdiGVKZkvETE8yt1Plgyh0ur5uqHJU\nEga5\r\n=+CgS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "fe48e8e67211262d10bfc8744cdcb1b69c921b95", "scripts": {"test": "standard && node test.js"}, "_npmUser": {"name": "watson", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "6.2.0", "coordinates": [55.777514, 12.592202], "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"standard": "^11.0.1", "clear-require": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_1.3.1_1534434429486_0.6573735856162741", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "ci-info", "version": "1.4.0", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@1.4.0", "maintainers": [{"name": "watson", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "4841d53cad49f11b827b648ebde27a6e189b412f", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-1.4.0.tgz", "fileCount": 5, "integrity": "sha512-Oqmw2pVfCl8sCL+1QgMywPfdxPJPkC51y4usw0iiE2S9qnEOAqXy8bwl1CpMpnoU39g4iKJTz6QZj+28FvOnjQ==", "signatures": [{"sig": "MEQCIFUof6byPBYiJKF+mNCOobwT7+q3IePWDHI/wJoAtTzkAiAURH6/uxf3rxtqwDHWQLNx/wFN47A6neJ/ZLb1rkuMIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbepCFCRA9TVsSAnZWagAA9RoQAJSxL0P6PUyfjG+ClvG+\nlAAjyF3V5vi/LDrK4ULf9vHhLKGNXYugaGsrIdf+sA1yx6WIwpkMpV2PCaZR\nNuSHDIdheOY2IvrQI6lUE01dbWHg9sTWsJUO372Te6Sj9LSwqAJd4exfhNr/\n9E9VZklGlp9/kOVlucSZrvcUPh7DzjGs1ym/Ng1QnpHWltP5GIgqGiwJds2J\nnUineJxFdqC9Yf/jitbfX35cDruk378VYpNjZG5dewy6k+ZQLg58rAGDTyDR\nge7LTMasne0eTk96w/SB8++4wy4kafY5ZlIUHjtHQmT7ge1mJT0zaqBZDGh3\nCtY/n0n0ypx+iJtdIjoazAH+VNxMYH/8RGsfq0+RcDOWUufLHWzUV0S/eJVi\n0sDTvVKrFGGaUj5AdlOEx0AEKz62DqjYMungUIOKf21/GolF1Md8BJ5hQqHf\nyns3bbFnEq5OzgxOTdGqZ7dXCgTRyYqeYMZZDObgDglGOMth5ckBGbs/yv7H\nTaxe9HdZGGxNBNr8whWAqTPGW6zkSI21jtc7v3YvIzzoVafED2AvamnfB9+d\nKkFan2HKKdCOxUabj2wcmfeEXuGa/voJaZj2Cr77zxTmUcrKdjWAF1J9RxSN\nZx3sxetKi2zgqn4uCRQidqS5wDrx9jorm05nl4CyFr7Grr0dJOdSr5bfAgqf\nKGLj\r\n=dyHn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "5f1b8850452ad7be2ced88711cc3087d854c1878", "scripts": {"test": "standard && node test.js"}, "_npmUser": {"name": "watson", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "6.2.0", "coordinates": [55.0113914, 14.9747494], "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"standard": "^11.0.1", "clear-require": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_1.4.0_1534759044627_0.7635631480866367", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "ci-info", "version": "1.5.0", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@1.5.0", "maintainers": [{"name": "watson", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "38327c69e98dab18487744b84e5d6e841a09a1a7", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-1.5.0.tgz", "fileCount": 6, "integrity": "sha512-Bx/xWOzip4whERIvC97aIHjWCa8FxEn0ezng0oVn4kma6p+90Fbs3bTcJw6ZL0da2EPHydxsXJPZxNUv5oWb1Q==", "signatures": [{"sig": "MEUCIQCLt/Qmf91dCqr+n7XIYuNg0HBDqgL/bixuqnMsPCHMiQIgBFjvByj1Wo//zaSOg43uXRF/98o1gxZvvb/slBdJ+I0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJblGJVCRA9TVsSAnZWagAA1/oP/i9cxFjqPcE57cdVRFiv\n6bpRVlJ2LMfhGPpsACbDIUdUoku2SE23DnUUvjrahhWvfgStwzUsEOhug1OJ\nFW+uUKRxpsPBT4xnHVe9U0Zb+w/pzyH+qVcbL3JXJ0Ekmc0mdB8omb1spYB7\ninssw1kbCbNEyer9wixOOgXQlU6QJuCivPeyc5Fj6lQ8crUi8m0w7hnlP1z0\nGW9TJPjY9iKBCJ3nJfowmt6tjgrBR7jadVw8UnUfBeEPnNSN+upD/1/IFpfT\n7gvLVHRtBuJPCL7lMQl/sKZJJHggEXw9hhhXvFZ6l7XN5c0bNJrkMK1LpZr+\nkSLz0qS9KWnfOkWW7RAXCcQKL6DLtzQC6RTqSPCQMdHnI3rDLWPNrT6CYPtp\nZ7STW3bhZKlb6CG4SL50FGU2fXmc1ByJRutFt8CMIbYzUInwJb/9F4o0ptPE\nOJZRgSTRLTvc9xo/rxtvhAm4Ro9tn0b14/ZU/wkM5Ta5sU8NSgudnngF7cnA\nuWfWCGzs1ioFb3iEmvNyvjWSuA9cteskfa08jRWMA/vrjUMrc8VMxfPFLcXo\nuos/LavXIUdCwH2sXZUxtkPhd7kMYVsMFHY7bY4br6hI0VOxO2FpzOFQ9scH\nqRAKaYZsZKAB/wTIhYUDNeD9UzL/+Vu/eoCMpKzUu+v4tQ3EC+KNZgbh0mU5\nRxzk\r\n=gLea\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "32a557ca8e9e193dbca085773842e4db4aa98752", "scripts": {"test": "standard && node test.js"}, "_npmUser": {"name": "watson", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "6.4.1", "coordinates": [55.776761, 12.592082], "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "10.10.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.9.1", "standard": "^12.0.1", "clear-require": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_1.5.0_1536451156728_0.12514766830933355", "host": "s3://npm-registry-packages"}}, "1.5.1": {"name": "ci-info", "version": "1.5.1", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@1.5.1", "maintainers": [{"name": "watson", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "17e8eb5de6f8b2b6038f0cbb714d410bfa9f3030", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-1.5.1.tgz", "fileCount": 6, "integrity": "sha512-fKFIKXaYiL1exImwJ0AhR/6jxFPSKQBk2ayV5NiNoruUs2+rxC2kNw0EG+1Z9dugZRdCrppskQ8DN2cyaUM1Hw==", "signatures": [{"sig": "MEYCIQDy/D9q6zr1WRUNrssDzz/jQcKJaHQVGYs0QRPoeAmSQwIhAOuY1hZrhR0kItspoOYncOPwV/XqTiVXB1SoqRHe3wgQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJblPerCRA9TVsSAnZWagAAp9EP/3VdZoPIJEXby7IgLqTM\nJ7J3sCBTPb+Oxa2kd94Sav8EQ3KcA76HfYvCT7tvtTtChNlymZGz7LNfgmSJ\ncovADvxHZ5oglqEvfLLxSqlwMtq6jGABL91thJqBit72C4RNC4Ecyolkr6CE\nDINeteOjFLlJDGAaCXbdw32+FYCyufJFNnbl1mpF6oeSil6ACtIcTwnGLu1R\nUXsTG9YJcQlDe1WZmpufdHv2i9GP/BeQD1fRIHxwjkjCNtR8Xp5knyw6Yedr\nAbJTdxMvJds8rGLGsSRvyIYR5HrAUM96PXgpwyTR2CWmGJ9u+zE7q3BCvFWq\nVtLHZGu6ew09S8d//b0V5ASaytGVzyQPavRjAQ5wibZHT9b/5pbj/sqSqJjE\njZIC/pa5dlG+Lt4sSF80R5+bm704Jmh+PX96z/aQGb2IQMoNaWVKvBsN2rod\n+AuzQZyX25RLCeFVKJLCf9SET8pTz+R9Aj3vVw+rcRh5Xdhi/E3D9w8Hc+wR\nBcsPKHjZGN6kRFHsI7Rjk8OUs5cLDtU366UNlBVMEShJYMRLEpw0N5EJI4SU\nLGZ8kEDO53Pvj0M71HadECDJVXgm+g73yoadvQaMTgCsnknCUANnIjtFqydc\n5jsSj8cUPelZ49Zg82dKnvxDr89GPHjkJ69b4ORkYpjXaRoBRZJOrzfvPCLH\nH2nw\r\n=AseE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "d2837c895da577a1a63698d97fb6a78780c1c404", "scripts": {"test": "standard && node test.js"}, "_npmUser": {"name": "watson", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "6.4.1", "coordinates": [55.778273, 12.593054], "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "10.10.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.9.1", "standard": "^12.0.1", "clear-require": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_1.5.1_1536489386481_0.010115633667440393", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "ci-info", "version": "1.6.0", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@1.6.0", "maintainers": [{"name": "watson", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "2ca20dbb9ceb32d4524a683303313f0304b1e497", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-1.6.0.tgz", "fileCount": 6, "integrity": "sha512-vsGdkwSCDpWmP80ncATX7iea5DWQemg1UgCW5J8tqjU3lYw4FBYuj89J0CTVomA7BEfvSZd84GmHko+MxFQU2A==", "signatures": [{"sig": "MEYCIQDWmLKQkrfG1FlGPpBWSy+OrgS7Q4TRz618T0Unm6oWfQIhAPH57Q/BDfNHQ52es+2xiLxERrHSPVdMT9QCDP1KTYv5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11845, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbo1wtCRA9TVsSAnZWagAANfwP/ROk4A97KwBmsJw5o/ev\nlaftMiVwnI/966mQ6Ak1GfTWx28SjLWKnE+IGKK+1dXDOoXrb6td8TCl/FUQ\n2e7khqDoPswsPtUJfJWY4dYCtS8UfvfJk6BlsMR7ebHxDc9dVpIhZ+V0MpVk\n7HdW3ay5sGoLUXenULN/8WoaRkpujVPJ9sltX25ZkEj1fvGuF33VszNAC7b5\nYXRKCPVFsMZF7APoJiMs9120tlIdw+uiT812ZY6QiwX0HlcHnYswE33h70NA\nZ7Od1js8nAWpdZi1FPiJtCQtl99iAIZboBijRhpkDILdeGR8S/f25HDTvjHW\n3oXk5/Im6hzFnPYezUs3EKf4GT92Bs4fDuCF8Sp1EoChtE+r35L09WFNRKzE\npp7L4+ZHPsszHz44z7Q84TZKtmBPX51fwoTSL//2ix1g31ZrMfYuvCA/s1Yf\n7+3IVlNMW0sUnzGepgF8+jevtOE238XcLHBrTl4LhTsSwmEyR9sRKs6xD1Lx\nQbmyBRWpsluaHnmxaQVoVxzJCz0J7RReE6/UUfGzzmeF6vrKtd6eCv2ZUECk\nSQwVW90PUjr1IgPaVa8stjq9kFxQtG0mwTS2eEO+gxM3hb0RT2t3ecdG3Zq9\n0INHULVlKDtKMXpoQ2QEV3XaZM0X2j2sqnQxuYsQHt6Al45Q43rcz/yGgjWT\nICTT\r\n=iAOM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "3d07175c39b07090ab939471c47ef363ea74ab97", "scripts": {"test": "standard && node test.js"}, "_npmUser": {"name": "watson", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "6.4.1", "coordinates": [55.778271, 12.593091], "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "10.10.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.9.1", "standard": "^12.0.1", "clear-require": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_1.6.0_1537432620983_0.7446311114890938", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "ci-info", "version": "2.0.0", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@2.0.0", "maintainers": [{"name": "watson", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "67a9e964be31a51e15e5010d58e6f12834002f46", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-2.0.0.tgz", "fileCount": 6, "integrity": "sha512-5tK7EtrZ0N+OLFMthtqOj4fI2Jeb88C4CAZPu25LDVUgXJ0A3Js4PMGqrn0JU1W0Mh1/Z8wZzYPxqUrXeBboCQ==", "signatures": [{"sig": "MEUCIBsoAsixI44RnCqjozvTi6bjljMkliQ04Lgq+CQkrCQRAiEAwBeOcbH64Y9N+KjKy38Bfzh/JgIpyojYA+QMwrzfiT0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12808, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcCEClCRA9TVsSAnZWagAAew4P/32dPsJ9LK3e9rXxaZtj\n32KCjxPFBFTCTNQT0CYdSU0plsSDuyc0WLM+CXmlVHUDp9nSkqayo6prPQhV\n9qjDh7y0YPRSgQF+QXzMhlGafHTdkMXy3OVE/2DLSLD7DkeEe+4XL1H0sSke\n4rS/y2EtanHtkp0L7wdIrkdVveBqSMk952vkIjyT6ehqOQoz22n5HGZOJ2aD\nxkkDemY0tZwDFOcGRNMPj+3D6bvppIPt+daZEK2jCVk2rAIeTCV8UIvwpoPR\nNt+49d0q84iiV9Q1sYg6VYv7qGjLgDPXoZwFw1NEnp6bOccT5mvCV7+jPeup\nw6S92VLOFP1ovGYX8EwvYXXOzw+bjCoqGUUSNf4IuMOA736G+nWXFkIVEi4M\n+jZSr8LYLo+ehabsqseDqlCLtKrWG7hXreqSgUaFuXn+qbu3LSicaWVs2VUs\nUeddGWOVcYJuJpoVy+emVbqH9MN9rRDm18/FI6We+u6u+ES9MtxRV6wcefaM\nHOaUUKI7phseccZUIIlu5EdFAE8pnCaBB9q7yTSva7DvtaesSEH4IS99dvzQ\ndIx6ASWwq4Djkse4dybrXIMQTSMy2FSKQDabHVFG1WC7RuckSg6T4im8cN7E\nHWwBEf1SivrkTUCoJ8UsWmnZA1A0R/0MiiN2q5/DUtEq6G2BDLs914AjCgrV\npShr\r\n=Du3l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "18fe88088b626a6dcefc66e66fb32badb05ea216", "scripts": {"test": "standard && node test.js"}, "_npmUser": {"name": "watson", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "6.4.1", "coordinates": [55.778231, 12.593179], "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "11.3.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.9.1", "standard": "^12.0.1", "clear-require": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_2.0.0_1544044708403_0.40541284673323297", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "ci-info", "version": "3.0.0", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@3.0.0", "maintainers": [{"name": "watson", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>-s", "email": "si<PERSON><PERSON><EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "7c4a5bd48129d2874dd1490f8c0923a096c3bdc3", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-3.0.0.tgz", "fileCount": 6, "integrity": "sha512-vgHOnehN2JJYq8H9bChdPQJYeNNDD2Uf+wHL7SHt0iTyDPujeVe2JVCEBZLYEfw/OGpEvA7EEiBvlSR84TA8oA==", "signatures": [{"sig": "MEUCIDsKckRYVnmI7b9FI3HijsIwedi6L4hYAwvU26k/A6JLAiEArW52oYSbt5dUupcLZ4tVkkjV0adKmAzIQeNXRQpzLII=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16528, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLUfeCRA9TVsSAnZWagAA43EP/iPT6BH+7fKUabMnoffa\nUe1kpkdsO+nAwd/Csm2m5nb5Bj8s6Z8xNAyph+ysakhLpzqUt2SHajT9WdKn\nz1nVLQU8YvjKcB0uNooMMq4mcjG39a2dtQrjrqgkjfRUHd6HFXPA9ZAo3ihI\n22GBbvyVgPAZasM5EthxkqJS5IdLChho1OAhYY62qiUfA8wCRjUPd4Mc528U\npeUnWB7YV3fGpRTQyUfQbZkgtGGrmlkIh0Z6+m8Pc6DB/nn4xM+WKlGLyph5\nPMQS54X6NjYIVq6I2pN2ETvVma4lM/MhKTzy/yXh4WK1A75GuTwHT/+GVffE\nXx8eDxKzPX3/aoGlce83cQw+vaJbsi+DyaAl26ZD3tacJqzSEjFsaipL5Gxu\nqypPaY7HUF2G4GYcVMhB0OmqymA3dM/DilDjeUFrxnBrjnBMTA9haiFhzYfd\nG2W9X3wYPBevUKtigTcxWpHi24VMc95Q5ICCeSoWOsdxYV4Dt04qHW7WXrYC\ncQxvdltt9MZBJ8ZRaUIlQTHmb7idiL2gFow9FtH+knXaZAFsV87x8MF3VHRm\nu2RiixUdkLy8KnlnTl1pcQuO4lWxZvnIDwc50uvebmPmBhkGC8WCLG4q9pn9\nNiMG1p85G7VgXReqKsVpm3+wf+n3Cesn3Vn42iNtmLajOq1IZT367EO7WVR9\n0wIq\r\n=BrHS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "25f3b6a5c2d7d1240f29e0d6555971542975b92d", "scripts": {"test": "standard && node test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>-s", "email": "si<PERSON><PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "6.14.11", "coordinates": [55.778231, 12.593179], "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "14.15.5", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.1.1", "standard": "^16.0.3", "clear-module": "^4.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_3.0.0_1613580253974_0.9590706093280275", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "ci-info", "version": "3.1.0", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@3.1.0", "maintainers": [{"name": "watson", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>-s", "email": "si<PERSON><PERSON><EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "88317ee4f73d176c6ed96ab5ab5715bc1620f481", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-3.1.0.tgz", "fileCount": 7, "integrity": "sha512-/ygnIwc7KxTbHzSsQIkYprfWIsTBcmLrfrbpozFo/Rcalh4rOa68x4DFJWHxyouwAhDJ1bl0JW9xOP4F8+Q4Ug==", "signatures": [{"sig": "MEUCIQDxQQEzPWN7irF/EJ694D1Fnh6TGyHz3ixqnG+ILJIBiAIgTJoU4Q/Q1zpOBGx6zS2JkhrZORJFO/ydNCtQvGuLKNk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18678, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLdlhCRA9TVsSAnZWagAAVL0QAKJIfhcVwV4p1m67ChWe\nP2Z7QPXTQQEgpie0v+bWc2wfU60kjrE5CSUFksD1g6p5seXjX7q0Ylkc4ENA\nKWwUFurUUZ4rH2Ek7cCo9RU8Ba1fzBDel7M90R6CcFIjl6tT9j333KaXb0BE\nnrgly4x7UhOmnRtfpTebKuQJVAbr+COu/iNnjqh9/dD5bJ/wnfRCk4hKr6Hi\no1gshFjNtagkRzJ8dcZycPJKY/XVP88k9UBAiNVZFHvKy+WCj0WzAXemriCY\nodPEwoYR9ggmVqC9ZDTIYCg2o2cvGBOd730+1aUEByC7UwOKhda/pHpzlUzZ\nSJBExqfio+FBO+/V5osyW3ChsCJmyXhoBMbXOspT93v7YTVUwcWcmgp2A4vo\nZvQiOkjG+qerJQ7Et2lWKh18htmLIoamFMcZ9an+YTCF9G6IS3ffcskndXVS\nDzHhowzgbxBeX12f45TY7r4JPmeJUimPRhpfJjsWgJ9cqUCtZK6M2c8IzUsj\nDC0mHKEYqPgoHJAXj32V2CyGZDMtquEmIr7vTiPO+Fw4JMwhr+sQYCN/0tra\ntkHRVyMiaX6W435VO16hJFOUdRtFGs/gjF/5BUrvadtAvJpCcb70muKGgWLw\n/DALu22+rpbS+NFcKTIrEXHih+b7dm5PG2a8Kj89SKM0UV87UsDJYvJYxC5j\niClD\r\n=yVU9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "4f1917bc97aef4a7ff7cf9a523755d263d647112", "scripts": {"test": "standard && node test.js"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>-s", "email": "si<PERSON><PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "6.14.11", "coordinates": [55.778231, 12.593179], "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "14.15.5", "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.1.1", "standard": "^16.0.3", "clear-module": "^4.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_3.1.0_1613617505381_0.9147246041658699", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "ci-info", "version": "3.1.1", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@3.1.1", "maintainers": [{"name": "watson", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>-s", "email": "si<PERSON><PERSON><EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "9a32fcefdf7bcdb6f0a7e1c0f8098ec57897b80a", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-3.1.1.tgz", "fileCount": 7, "integrity": "sha512-kdRWLBIJwdsYJWYJFtAFFYxybguqeF91qpZaggjG5Nf8QKdizFG2hjqvaTXbxFIcYbSaD74KpAXv6BSm17DHEQ==", "signatures": [{"sig": "MEUCIQDOGnNWC+/0wEMlC+zBr2Q1WzS5S7pbWIphnfk76ouP7QIgAeWkV/Gh+99gwFu7d7QYFXwxLmNoJmuCPtiZR2okR1A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18711, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLgWICRA9TVsSAnZWagAAVXcP/0hiPJqJKdhV+tM+0Ba+\nukgJ2Ke2mOLfmRnCxfEDCapY40267lX6GuZCNPJvh/XJ/C/2dYGbH1oOTI7z\nthVNtoQROHvjFXR5KNqhdt4Wv2cJ5xHotPqgRvEDWDLmLCYdqoMdkNlL4lKu\nSKx3ozUR9XzPfKN8sj+oHfT7lPo+IsDL389AAGfnpS+rzDecKhzg/6diFDU6\nF0PVE/ByKx6Dc0RlynAJWgVjeUB4Jme20VGtO4+h39bwH1x5ruNGY07/cj5T\nmz74YWn5CEdtRy5Qu+oFuka9QwixKPX5/M7sU3gO4YkNONlCNDPPtn6Mipfl\n8glvusY75+q5XPMrhctsk5ix67M/xHinbvHq13D2g0WCVokCDsImAmaKXIfY\n1i/pI3MKXsUMnasYIwC+70ZJhaV8Eb0eVx53K4rqfJVh3daz53ORMoGujLSa\nwyfk+HSQhjBpKDwRODDkqqACmFlpKY2wbY41A3Xv1xTiItkYmfysXygo5qDN\nwPU77A1Hj7iK1giiFNeWroOGMSq26rxkqlhxkRI0rOAvK+/+4b0iUhp0vvT3\nWGB08Ko/gFHzDpP7URgt1jOATFxO1Unu2Z9NiTnj6znVbEA+0l9zZH+2fVvU\nSuNsU+ozF2rTngMH8Y+PN7W9i+T5AD1m/t4jzF8VXPXNd8z4Wn5+MOvQM7R8\nPFLY\r\n=KXXU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "23c0b405946f6af81cff2754679d493f73d09dda", "scripts": {"test": "standard && node test.js"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>-s", "email": "si<PERSON><PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "6.14.11", "coordinates": [55.778231, 12.593179], "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "14.15.5", "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.1.1", "standard": "^16.0.3", "clear-module": "^4.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_3.1.1_1613628807951_0.060794742944832336", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "ci-info", "version": "3.2.0", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@3.2.0", "maintainers": [{"name": "watson", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>-s", "email": "si<PERSON><PERSON><EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "2876cb948a498797b5236f0095bc057d0dca38b6", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-3.2.0.tgz", "fileCount": 7, "integrity": "sha512-dVqRX7fLUm8J6FgHJ418XuIgDLZDkYcDFTeL6TA2gt5WlIZUQrrH6EZrNClwT/H0FateUsZkGIOPRrLbP+PR9A==", "signatures": [{"sig": "MEUCIG9m2AYZNPvRzZEeQkX45FECf2mCLqMsrsJ5YrjEk8clAiEA/ebdc61Eu2EJtz5tG2NNFbvVv+a2M58q3W+f/5QFZcc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrl14CRA9TVsSAnZWagAA59QP/2OLfVMnInHuWeRUYkiS\nN6KSzf7f8CnWh994eiqDyqdaBcQs6HxccZM/PLaXG26Bh13DRXGC4DHGwOfT\nqEkH3/plJVzbrDFHy5Ujnb6aasi5DVIb8WOin4avV8nXWobzRfj5XvVFBu/G\nIHJxoK8X4u7QHRf0aYiS7X2k0tr+PXL7rm8LP2zjtjneln5QZpXIKpEkLXjd\n6HgIUv9vKg0uvP+zdZ3TjZTJdztVagv5EbxHh07oGdWVxjfb9kC1KusqIC5l\nLTdRIt6twtYfQPSL3PdoyLLcVR+w/HdI9JiVqhXeF/tbiG8/6LaxXkNlIxXi\nCXaij9ZgmCr6ZmgN5+PRmFsHHJrXUmy0QFCiD6FuvKKBAxaCtLi7ESELqura\n+MS2xDADLb+cTwy8tYXSqcMBl73LBcuGhjQmR83NJn583+uDRfR+zz5a7L4Y\nmUrkyQEls+SbvOqQjjfhMq4MNtktFtFqUPaEv/tVO6YY4Feik67YXlVjxgQ/\nO2vsyur3szIte+0nLN44qVxAWPYG68LI0/umgl1L9VuhlQ3UzB+H3y0Izhyd\naBeOazG+G5lrswi8JrlSn5VIbN9n5PKI22T/mQWvLYQIKEuYgjsQ0tjoh/Hb\nf6Bp4AJ28Yw8/0AsYFhdlhRu599ncE5zLjMkjmsJd+Ht5TDsQcCFFjsT1wC6\n1qe5\r\n=vdQC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "c4f1553f254c78babef5c200c48569ede313b718", "scripts": {"test": "standard && node test.js"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>-s", "email": "si<PERSON><PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "7.14.0", "coordinates": [55.778231, 12.593179], "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "16.2.0", "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "standard": "^16.0.3", "clear-module": "^4.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_3.2.0_1622039927514_0.3044358133557945", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "ci-info", "version": "3.3.0", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@3.3.0", "maintainers": [{"name": "watson", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>-s", "email": "si<PERSON><PERSON><EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "b4ed1fb6818dea4803a55c623041f9165d2066b2", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-3.3.0.tgz", "fileCount": 7, "integrity": "sha512-riT/3vI5YpVH6/qomlDnJow6TBee2PBKSEpx3O32EGPYbWGIRsIlGRms3Sm74wYE1JMo8RnO04Hb12+v1J5ICw==", "signatures": [{"sig": "MEYCIQDTKduwfDuZh4JIIP3gGf401sGxf3CKfjCtY5QxsMx+8QIhAOtYBJhv8BdeJJGTeoO9aWyQtnRPz6pWnh0aBMB5VNxE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhna50CRA9TVsSAnZWagAAMoUP/jX+Q6g8hZYyo+tS4v1G\nyA7WMO17PDAONwi+5XOriZV7XStSiEadN/2mouIOA3Lc3Ki2/onyvEI4mNVj\nH8QIShMnDjX+9d3fRwxOeQ0HeACybD77tqlOD8kBEJVLqcI5e+RV5uhaBIIq\n6/+jhaqma3MZJWE7BiW/C8YhrIiR9wc805zWzt4ot0NM9z7OZiYogQiyltcX\nkWQiUpM50RDekkYJttwNH7YA9uImclo0MI5tdosa3XfjrC4qoEitJ6FSOrf9\nd73KpaUUuldk9Cbd4FsVtMGKaJCGhxHaaPda+78aODFKmzTrAkQHMICif11l\n09+3xBUzLM6wV0ah1xlImgdE5EgfSANKE6+/K0cYOUEiJradoZAVN0AK/vsf\nvAFWhz3kX9OYHCLcn+a60jIQ2RBlK9b/iSXeITlCxaHNw2fIvDEH14MRCrlD\nsa+Wi3lQuY7cIBQxVyKbJVXx0W2hcjLG5zoQ5rCZrbd7YlfSuh1YUoC1jHbz\nLpmjNTog8t3sjcqI8UuQvsQzx02kvVyE9s4Q0c5d5UX5c2ucIkAmL/dzTNOM\n1KFEev+huY28FI3D+Uu5grzBq57rU/qjsS8A9+xBZ6oz91si4KL9Pgkxk/e0\nn7w5tzbvYisvV+MBWZBkA5yU05S/IrK6kN4bX9vTD4TIUl2Lyp+Ats6Z4oJW\n8PGW\r\n=hA6Z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "1f890078805447ed39b17a6ad136356a7d5cd68b", "scripts": {"test": "standard && node test.js"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>-s", "email": "si<PERSON><PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "8.1.4", "coordinates": [55.778231, 12.593179], "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.3.2", "standard": "^16.0.4", "clear-module": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_3.3.0_1637723763880_0.5401804625594544", "host": "s3://npm-registry-packages"}}, "3.3.1": {"name": "ci-info", "version": "3.3.1", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@3.3.1", "maintainers": [{"name": "watson", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>-s", "email": "si<PERSON><PERSON><EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "58331f6f472a25fe3a50a351ae3052936c2c7f32", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-3.3.1.tgz", "fileCount": 7, "integrity": "sha512-SXgeMX9VwDe7iFFaEWkA5AstuER9YKqy4EhHqr4DVqkwmD9rpVimkMKWHdjn30Ja45txyjhSn63lVX69eVCckg==", "signatures": [{"sig": "MEUCIQClNRcPOQJOKE2tnEW6gqtZQMYXfNE1p4SgmC59hYecvwIgWGXLZefJWmaLdSvpYKZ8qtCR1pXGGcDw+fvm1fzkDYE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie/hNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqmgw/6AvHfydh0eiNRM3kpBzwTKR9qhwkckiPeYkBIpoJ5NgblCen9\r\nRRWsEL2MRNmVu6vS40w73/2/S46ijOSKkGsHkNy2bV1LEyaiyaNHsapyz0sN\r\nUBi6tBYfn1B0BRO0iCcPHMnzcwAEjkNrP9+ufbo6bdGEjOYgQbBs0ds8Rx7+\r\n6of5vRKrl1o6idtuNaKp/aws1X9xdqeXBRAbu+iqqSKMpM/we5U77xSKk4eZ\r\noQV99heQMq8beFKJ59oAezIaCjF8nysjlOR+3MgVfWix3PuAZZ6Va+jreUYL\r\nYgqVQU8+zdPlbL26/PFNphM9mL0GMaExLYH7NpuElGO1LSYFKzvoxt3s0t6e\r\nLgryRqSF4KGQF+HpzeNIu/8QrO1pIIuwr79xjgMk+lXfjrqQXR6j6f/eWYZG\r\nAfgTGLeh5D9SPHRKX3YRzwdkcsXQoU1rzl6AWlb/DbP/iX//VaahDlJHCguG\r\n6yf7zcEC2vzIPxvSIpOP2L+Upidb45Hjbb5hbmuvgVPY5S6ehhK/KGzqK/Xp\r\nR21WiFtir38FBvrNxf++3gOQnRat4GnfNhvxvMv/VvkXtCwnpHXv7830B3Em\r\nKE3wrTflhOUinpMO0c/aGsHAz+iJEgpC95UidWZAkBDSudNfMsK7vLJdKCSC\r\n34ypN9WNqTjmG7rUF8F/1cIuD34kxSaHyAo=\r\n=tghK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "8a6aee332a57d92d30ab0c5f7f3422dc2b0a31b8", "scripts": {"test": "standard && node test.js"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>-s", "email": "si<PERSON><PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "8.10.0", "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "16.14.2", "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.5.3", "standard": "^17.0.0", "clear-module": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_3.3.1_1652291660833_0.18762369057057393", "host": "s3://npm-registry-packages"}}, "3.3.2": {"name": "ci-info", "version": "3.3.2", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@3.3.2", "maintainers": [{"name": "watson", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>-s", "email": "si<PERSON><PERSON><EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "6d2967ffa407466481c6c90b6e16b3098f080128", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-3.3.2.tgz", "fileCount": 7, "integrity": "sha512-xmDt/QIAdeZ9+nfdPsaBCpMvHNLFiLdjj59qjqn+6iPe6YmHGQ35sBnQ8uslRBXFmXkiZQOJRjvQeoGppoTjjg==", "signatures": [{"sig": "MEYCIQD9cJgEwHAyFFcon9SapOSI9yFK2o97URdeZ/hp/fgVLQIhAIECfh75E7313LqxBf/nIlOEg3dbg8lviZa5zhlnYEdE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20241, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqSw+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmosng/+PHGaukTjfQJmsODKh6xSQio9nu0eDjykdWP8pj8N1dpHnId1\r\nZv4Z2encPA1wQN78qfVMkD3TRL4hCaJiOZx+87UFfa9Z1HffY9a0f7JQqqnz\r\nXwZMi4/7RA28mQgv8UOtEGe2Im5SZWihaVtmb8Dsg8VjERbJPIaIwKwo6Ade\r\n24mrW9HPrSRgLahn5n+CJ/WUp0YqVttVhLd1XRhAw7PsKl6rPX0+rmD3nVMu\r\nwVUOOIjfwID9uqMCdSp7U6EYOBZZWj+w5UZikB9E7r0BOq9SjhWHPmD0vh9B\r\nTeRphXycz1dTps8hD06sodSsfDvdb5FICFghu2ecSPTMqV27CfS6ct9PoPJC\r\nReJp/K3HoSYuhFWIDCdmI/3fM4HWuQtk6Ig4TPuoJP1pQbta0Ic5yy8il3j0\r\nk/gFnNbe2zYLeFh2QtFOVUc449nDD9revF2Ja2BbU6VIGsvx0Rrda9smth3I\r\nfTz8an3YuW9ia9W7EEmY0CxvOuMadyrPSlwvK5QbgK41tMXPHsGX5f9+TXlC\r\nrTjZGk5zyafQTPMlIWLtqOQ2Bhx2uqJn6iG+fS8loc5ki+nsIAQUky7XG/Aa\r\nQRmjyUn2kSYY7eYAut1SVPlN75eyt0X9btX2WnXNF6zPnVVrHc+BycLid6nw\r\nrVFFpLAacIyo13KMvKqyDnNGXtR9xJlaOl4=\r\n=JIA7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "26718f7234b31ace58763a4fd28f858b3a2fe266", "scripts": {"test": "standard && node test.js"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>-s", "email": "si<PERSON><PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "8.12.0", "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "16.15.0", "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.5.3", "standard": "^17.0.0", "clear-module": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_3.3.2_1655254078285_0.9030617549380626", "host": "s3://npm-registry-packages"}}, "3.4.0": {"name": "ci-info", "version": "3.4.0", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@3.4.0", "maintainers": [{"name": "watson", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>-s", "email": "si<PERSON><PERSON><EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "b28484fd436cbc267900364f096c9dc185efb251", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-3.4.0.tgz", "fileCount": 7, "integrity": "sha512-t5QdPT5jq3o262DOQ8zA6E1tlH2upmUc4Hlvrbx1pGYJuiiHl7O7rvVNI+l8HTVhd/q3Qc9vqimkNk5yiXsAug==", "signatures": [{"sig": "MEQCIBF3TK/F8Md9TWPda5gxgI8ZP37HCPx5TTt25V0L99pkAiB7v4gNy+PZkkrkuj+gXGhi72/9iNS3s1tplAo7Cy3WdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21339, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjG3lUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMoxAAiUFIS5qsYGiSRgQMt08/kNIfktZX9It2UruW2amJ7nf6Aj3N\r\n5Olexij6/Ei9VagI5DudQ/f/9hOyOa8Fd0WXR16dCwS+3rbbWVm3a4AHfupX\r\n7pVHf2TNe1nOjI+3k7bZiMI4KnrnAUuS459EMUJRm6GKqJwfYEadvy/7LWj9\r\n6C03pHehuPg7dbLxA53Yvj8Aow1ENa+hAVIaO0a/qO/TGw31g9fHIT8tqdo0\r\n2y58TeBqQmmEBjIbaZunq7bhJ9//WyyCs6sZ8lUqnRDGIOvOh3KsgfPw4Snm\r\nAhBZQZALNr8Jwj1cKVPnEwtBm/eyKDa6Da+8gHj7K35V2tvq4HUXmn3M3xL1\r\nhMf9bNWLmxHSPSoLWepiACDTqen2ZVhDs5p+i3mdFkY+11+2IYUjXnMpnhdm\r\nQZk2sDS6GoP8F/OarCQIp5/2qqR9NWzAgpyvHVfyO9UDN724PH9bO0utYQBs\r\n7MMZ/k6jEvj+8XpEj2vAj7HgTbC48/AN4flVwa3mi2t3bWYR97Xy++b4C3Bz\r\nHTxhxCJ9+sUjMVX7vUlvD3r29dCyLaffTyDTts3iW5AifGv8fuZfMioa+PMn\r\nCllzC3+Dy35wF7CSzowtueEQoSJXv68RHkvczNYX8anD1Oa1uxr4ZAdAhAZg\r\nTrDi+RLZLOv1GJPGF8P/wWwvFu6wYhyFR3M=\r\n=1NPs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "7a3c30d127dc48fbbd10576df4d87d95e88eb1b1", "scripts": {"test": "standard && node test.js", "lint:fix": "standard --fix"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>-s", "email": "si<PERSON><PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "8.19.1", "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "16.16.0", "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.6.0", "standard": "^17.0.0", "clear-module": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_3.4.0_1662744916591_0.2115850625244602", "host": "s3://npm-registry-packages"}}, "3.5.0": {"name": "ci-info", "version": "3.5.0", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@3.5.0", "maintainers": [{"name": "watson", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>-s", "email": "si<PERSON><PERSON><EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "bfac2a29263de4c829d806b1ab478e35091e171f", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-3.5.0.tgz", "fileCount": 7, "integrity": "sha512-yH4RezKOGlOhxkmhbeNuC4eYZKAUsEaGtBuBzDDP1eFUKiccDWzBABxBfOx31IDwDIXMTxWuwAxUGModvkbuVw==", "signatures": [{"sig": "MEUCIQCYrYzqBgfpjyT/wp6/V8W/kqB1d0o+eDPN0SwkKNcvGAIgI4dNn2Dyi0ksx/0mLFkYwzkg8N7+EhFPBiI8/5FHkXE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQiVwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq7/A//faba28OvbQ5sttHWm+KK97oWnkvTfwFE9FwhFIG2m8ZDTQ7W\r\nn+QWlNlnMiG7IudYB/l/mkV3FWw3V1CwvX5G7da2kOEAlIz1IcYGjqnBCKbw\r\nsLuximBcRsFG7crwZH96s5bo4Dnt3H2IKpuePfoKS85LbQEu/gwgtLvFnDdj\r\naGzfKdWoCExtTcq+vb3+Kk0INnojuJ6Azhla1cbOVxHraV4R1GO3ZZ1ZD2GR\r\nCLDnZEzVpEkhr9ZdCPLioBPBPkBZAh22ea4+pWZa61KT7AgX9BbG5BLEMSP6\r\nEdqFgaEhlGn0J26VqChpTqNsQJvfrVpzioIIoArxnJSBakOI1kBGutRMvjY6\r\n8rlsUhDhCHbzjNzKkzhI6UYykscWvB/NEGp1Yh9ohtmWLmKdjhNTZQLF1/vG\r\n/qUToFBC1ejz4x7v9G/+FV3crA5iFKmhVc7D7fbl0s1e2hw+RvrCIABUmJjR\r\nGvDYBFUqnnWmBeiKmO1y6lX6UmOz2EAYpIDl/H7ac2fbV9BxhBfJX99tShlv\r\nEUpkTetsM7Z+M+1geU4qqeNHwJb6EnSrXH6pGOwCBngoZtq7mgdx0WbfoPmn\r\ndDNPUdY1E5QjtqYNbmtu9TXuhBNSyxCpb3WtwO6T0t1xePsgmIFoPU3SpSwp\r\nAThJmlC2z02h7xOEM3DXk8qQjyuyi7OnBiI=\r\n=hDLY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "449537c1bd372f75660dc4ce2ddff92179dfc740", "scripts": {"test": "standard && node test.js", "lint:fix": "standard --fix"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>-s", "email": "si<PERSON><PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "16.17.0", "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.6.0", "standard": "^17.0.0", "clear-module": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_3.5.0_1665279344154_0.7026566003477999", "host": "s3://npm-registry-packages"}}, "3.6.0": {"name": "ci-info", "version": "3.6.0", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@3.6.0", "maintainers": [{"name": "watson", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>-s", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "d37ad22393e808cd62937a3f9be726f801654857", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-3.6.0.tgz", "fileCount": 7, "integrity": "sha512-RfY02LtqkzB/aiyTrh0TtDS0rQnQY3kyvkVkdBnPTUbkiGbkkCJhS7Oj8b37Qa/5eGiMilF3kH+/Km1EZxpuyQ==", "signatures": [{"sig": "MEQCIBmnJoPS9dRxZQcMsTOL3Z3F5fC7rRvlVSeZDua+26paAiAgipApcqfZ9AZRBELCvttjhjr8i1OKiCRxz3e+ve6EZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23812, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcM+lACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmprvw/9EEkeGXP/+yPUPft56G0no4pdrZWdYVTdaJp25eCJcEXecqW6\r\nDVy9iVyqZbXA6KgiFJAh6to0Uqz9a9DGOCdiQiTeyPfBtMlbg9gZyFPF+4WJ\r\nauGopp2pdoWAjjI0q7U6iBEHzUqTE91V6x0Un54xkmS1nqClK91YhFoe6OVf\r\ndIPtvxzTwjRj+KP1AnUli5s9L05LmU7TdpiFbWHYe2lfziCTGtMg78s6pUXI\r\nckuLl39CIXboe0dj2ZlpNKZ1Ea74+yYFMQd0HneM+CFJKqr3g4fkXZ97ttod\r\nFPoY1k/WoTFUtk8fyrYm53NIbGgbpMAiX0s2AQYtFjhuvxuUt+9j+oEel14J\r\n5brFsKtsrvLRT2/GZXsgZSAEecD/agL8c7UNDkI1EO44zRY599fbTcd27JhG\r\n0Z3gzASqNUEyx7EQzsFfVI3CkueHCvqwEThScgbMzGTEzDQGKDsNzUtfTNWg\r\n6EjxsRwVE0482eNQFjsjQH6PGV9eZKJXhn3GlSmLn7oUESCuQtFzonmTcy0N\r\nEz/8buniOrT0LO1OkVRqCYA7z3WuOg1HB4LKw2pUnKtBUbURG45Mb9/H/4BN\r\n2UQpU/dWi1BZgHiS1CvAmrgyPD88dqKNKURbtvhrU0C0zyRZY5urRlkaLO86\r\nOI7Y9TH9AEeD9PlddF/6LWVVn0H8koBru2A=\r\n=L5iG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8"}, "gitHead": "ef5a612853bb260ff2a8d860084669f543e9abf4", "scripts": {"test": "standard && node test.js", "lint:fix": "standard --fix"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>-s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "9.1.1", "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "16.17.0", "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.6.1", "standard": "^17.0.0", "clear-module": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_3.6.0_1668337573230_0.4499378239284504", "host": "s3://npm-registry-packages"}}, "3.6.1": {"name": "ci-info", "version": "3.6.1", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@3.6.1", "maintainers": [{"name": "watson", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>-s", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "7594f1c95cb7fdfddee7af95a13af7dbc67afdcf", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-3.6.1.tgz", "fileCount": 7, "integrity": "sha512-up5ggbaDqOqJ4UqLKZ2naVkyqSJQgJi5lwD6b6mM748ysrghDBX0bx/qJTUHzw7zu6Mq4gycviSF5hJnwceD8w==", "signatures": [{"sig": "MEYCIQDzeVseKEzD8281t3WaBg5b5KnOMtTWO0CU6HuO9mhSSgIhAIjyE7mDsnXjWPbgdnF4rrd1do+U6xmNTwIbFTJX1abK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcQ05ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrIZRAAh36cbLf5u+POWMDbF/0YndJHSbg6mTDvIoDXJ5q5rw4WhpYH\r\ndphYaxULqzN9X965iaW9uinCobSxLO1HNr/W2keUrDM5/rAul4U3gwwfqjGp\r\n3EyCdaW9JTCCqWVZE0kab75zA3DzZpHN2LbqjugEuWogUR8wlaV29xNFk+q6\r\nyjRhST0i3OS0pbkxJliH6L5JcBEXV+HCKYDDsPQeghAB347NfRAgtxl3ME1v\r\nxRhG/YMkkr3UCR073xV+iPgygYFxq3a89v+waJSelBU3cVKYEqB8VpCwBQ3c\r\nMi7o3RrRdYbg8JuV3jfkdoRyZHgidIh5mjHqSGMmrplBbSc1dpmUdDK9s91F\r\nIzuQhLh8dZjPfOoV/XChXkFER4idrOl7d9M4l9dcojrgZBp/1IGeCFe72xw1\r\nRA15h/SMJixOVvktLDOHyNJjII6n8TFJaIsMmr++X2NhbI5yq/z2rTpNY0QP\r\nUzLKJCbgX4HwNTCbIe2Qs3yf/yIDK0Mpk0dOFPDQ+A5SbFudbFMb1kGwEGPF\r\nn3Tq/rpzjXWQw4c9dk286/Oj0sjyngPWvi6voFzkN80ewq/JLPCdaJMzSSeX\r\nHjJytAltT5osHUvIQrvLoArvmJm2Y407kPrIrsQpgM7WmXkywqekdPXGHZj5\r\nuV9OdwVtyX7Hh0E3/dD+rCr1QdutnJyny5k=\r\n=UwmD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8"}, "gitHead": "decd4f7afedaef0d9875a41b8049d207798db4b1", "scripts": {"test": "standard && node test.js", "lint:fix": "standard --fix"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>-s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "9.1.1", "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "16.17.0", "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.6.1", "standard": "^17.0.0", "clear-module": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_3.6.1_1668353337368_0.09274481882476215", "host": "s3://npm-registry-packages"}}, "3.6.2": {"name": "ci-info", "version": "3.6.2", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@3.6.2", "maintainers": [{"name": "watson", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>-s", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "362ea15378f1c39378ba786affbc1c9ef015ecfd", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-3.6.2.tgz", "fileCount": 7, "integrity": "sha512-lVZdhvbEudris15CLytp2u6Y0p5EKfztae9Fqa189MfNmln9F33XuH69v5fvNfiRN5/0eAUz2yJL3mo+nhaRKg==", "signatures": [{"sig": "MEUCIQDmUpwihL7hCTZU/E7eKW3l70dL2JwkzweZSEk3vlHDawIgVZDIwNwHTXuSryF1pUSvCiUee1YXRc2wgibLLZptR4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24135, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjfOi/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7iQ/+ORdtYuefdZeUaBwQ3WmAm1HhydGlB/aC0xH2+lCl9zxZaYk+\r\nVniFcltS5At02fRlaQdeq3HibRDVlU4xGhiz5005WkjCaBNnvtFDDY/fqQq/\r\nNsiRlt8Qclwp5bZSnWg99gKywl1u0yZVC8WV7uaedL1AROP/JO1p7A7yT+Yx\r\nWVA7c8TDG4IcfUW1HJLiaJeqaA58hGjLuUQ6+iyN3qi6SAe+6HDr3AKBchje\r\nCISsinMG8YqNnZTi5E958sbBWFra1Xx5sUoGzUf9OnXA1Dky6sAwxFsEch02\r\nzU5ocipdoPr/7hUO9nGO2rDgPf7VmQhoTd/hZNTS2KDBwcDq0aAmm7T/z/vL\r\n38Xj4d30s2DrVqFybnp8qzcKZ3Bd41VNFxNKTT0xqYJQJpwlr52rJ0wtUWQR\r\n7VQfiGXrxppoDlfQiEGlDRQqffWGbgJcnZv5Y5Jz4Dreagn7ahBam3ga7hYH\r\n9ApAEBd1/Fl43ftvnE3IEeA135QtQ8s2b6XsldNyMoicnEdEmCFDn6RRZZ9X\r\nW4LX5qBnVoY7nE7kgJH/NnMFAgR7qtOshHTC1yrCNhfbyZo3A6GcgK6M+pDd\r\n543uqrcZK3NBOsEQs826fOk/Hlp3AwtqIgWzDIEsHV3WnXZgDd7o2VR3goc3\r\nl9Q7Y6xZ8Lm0T2NDy+Wt50i4ri5prdZj7GU=\r\n=gvcB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8"}, "gitHead": "2812bc29ece0a5a55a943aa6bcc7005f132d59ee", "scripts": {"test": "standard && node test.js", "prepare": "husky install", "lint:fix": "standard --fix"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>-s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "9.1.2", "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "18.12.1", "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.6.1", "husky": "^8.0.2", "standard": "^17.0.0", "clear-module": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_3.6.2_1669130430900_0.8646872548784406", "host": "s3://npm-registry-packages"}}, "3.7.0": {"name": "ci-info", "version": "3.7.0", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@3.7.0", "maintainers": [{"name": "watson", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>-s", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "6d01b3696c59915b6ce057e4aa4adfc2fa25f5ef", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-3.7.0.tgz", "fileCount": 7, "integrity": "sha512-2CpRNYmImPx+RXKLq6jko/L07phmS9I02TyqkcNU20GCF/GgaWvc58hPtjxDX8lPpkdwc9sNh72V9k00S7ezog==", "signatures": [{"sig": "MEUCIQDeH1urn1PpRMzG++V7lyALa9VSP7BEM7V3wy+PyZSwOAIgdW8en1li9ExijD4zEnhsxFUsRqlW/FGLgGgGkIjr8LY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjf4M4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5hBAAn4pyeItXSkAAVxDPQFO3u1O5MgUw5HWWyWu6qs3+T++m1tuH\r\nQ7WmHk/ihxN9QLfvAje07wLabR/Sr6uiHkL8jtH/wUv73nBSOs2jrXpUeWhw\r\n8I3zgXcGuSwzh1E7nI6sP0DH9n7poFs32UFuCdPq/POpfhypEYzZPLMUczOM\r\nDpbSxz1X7hcHr4X7wHpiZdz4S6NV0FNW4FYEdlQoHec2Ppqqs28knVJHSjBp\r\nivtkt1Ji84kQc8Dj2zI6xj5Hd0Oy+ycZNOg+WX0nJ38Yq7CJ+T+NZmZdDV52\r\nHsDvVpDrpGQ5pRr5CD/q8WsLDCbzsusnCZ515+sC1nd9JtR/i/kBo90q9AL+\r\nmtDzyA4ACxBOP7LSb9mL4vuta3knNqPNgTMVRbpfBo3EGrf9eKhXFk6/oSgP\r\nnbS85JjULA3aITQfBlUaBa8U7Iw8scWIXULw7H/5cZT0IkWcrD4UK0rYqByz\r\nd2usHykGEoIbDE2jmXiIZuNDxI7tdORAgyyuQlohMrVgI4RHwh79b4gsXWzR\r\nKBnC7YnKxOn5TNG2OKiJQNHrIvCy8ZybtcoTiaUPDr6ZxZG4zvXgjS4OEI/Z\r\n2SwlrPlfR95mCz7y84UUR60a47/7bn4zM+JK3tyTT3pXSH8HOSJxfOWy8s+h\r\nG8zzmZBmA9r96Xl7gwWQlgOf6E9SxdEcJG4=\r\n=ZzzT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8"}, "gitHead": "bf2b10baa6d4c480582e136fa5921bc9e12ef8d1", "scripts": {"test": "standard && node test.js", "prepare": "husky install", "lint:fix": "standard --fix"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>-s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "9.1.2", "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "18.12.1", "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.6.1", "husky": "^8.0.2", "standard": "^17.0.0", "clear-module": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_3.7.0_1669301048721_0.6725610282383785", "host": "s3://npm-registry-packages"}}, "3.7.1": {"name": "ci-info", "version": "3.7.1", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@3.7.1", "maintainers": [{"name": "watson", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>-s", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "708a6cdae38915d597afdf3b145f2f8e1ff55f3f", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-3.7.1.tgz", "fileCount": 7, "integrity": "sha512-4jYS4MOAaCIStSRwiuxc4B8MYhIe676yO1sYGzARnjXkWpmzZMMYxY6zu8WYWDhSuth5zhrQ1rhNSibyyvv4/w==", "signatures": [{"sig": "MEQCIAZ/x0aro7rKjVxe0iCOqnWwmd7FFUTKeNw+7/4xreNDAiBsWGrqSrcL3cDHg0p3+V7/m302RbUQ9sC3V9VFgbnDng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25132, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjsqulACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpayA//UFeBw5t72l4F+XRlDXXryWg3GRfAzJP93sw8+2/PDT3vbvGt\r\n+fy3bwKvrWNmmMVKyqrT/FWGnMTK+eR2Ap5xH6YtrpUiWKfH8uCrISlB+KpJ\r\n147Zevrw2hQTdiMQP4xvS7VnbH6/RxhpvojA4m6s1R+E2xPEbLHMlDV1hNkO\r\nQs8H8EhA2yXq1s6SKsSe86g7ZkNRN2OcG1G5U/bV9/B6bcebLYV4Kgm2u30e\r\nWLs0ROclhpNBztaUktamNoQsizu/owmWZ9+rj9rNa/ArjnRgfO7y+H+D+Wkx\r\nVsUdLkSZGNaNnzdcEXB42D39vIW5H01ROUMuJMeaMZGLYfqz67wE52HXaZGY\r\nb2F3Ix0oawK9TGWVA5t54f+YwSJo807aHEjCwGdNqbnX+WHGHEe3GoUGoH51\r\nRdPAeB7NmYra/8ZQdHn2LMtnw4oPK7BOyWHyfuw3OfV0lHBKOzuUgZWD/BL+\r\nqWdaZQ01ObZluegvjEkyxcTivhYKESD3oa1ktwLR7G72R7YWfiuISMIyGGF1\r\n1qs8ZrXxUQ4+nK1G7O9LNGl9WsN3ojLl0PtkeDlk4QQGoktU0Uvdnu1Kf9Ud\r\nKl0uIbithJqZQTgJBCtglEZHfDa0UZ7+I6SijvdSbPvjjZ1gImj9nA97qRel\r\n41wlFihYfa6x6xueoM5otiSyASykkyPomGk=\r\n=Myrz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8"}, "funding": [{"url": "https://github.com/sponsors/sibir<PERSON>-s", "type": "github"}], "gitHead": "f8d37a35394d2b6ab85025b536f0a5dbe456da1d", "scripts": {"test": "standard && node test.js", "prepare": "husky install", "lint:fix": "standard --fix"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>-s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "18.12.1", "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.6.1", "husky": "^8.0.2", "standard": "^17.0.0", "clear-module": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_3.7.1_1672653733535_0.6271403421729898", "host": "s3://npm-registry-packages"}}, "3.8.0": {"name": "ci-info", "version": "3.8.0", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@3.8.0", "maintainers": [{"name": "watson", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>-s", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "81408265a5380c929f0bc665d62256628ce9ef91", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-3.8.0.tgz", "fileCount": 7, "integrity": "sha512-eXTggHWSooYhq49F2opQhuHWgzucfF2YgODK4e1566GQs5BIfP30B0oenwBJHfWxAs2fyPB1s7Mg949zLf61Yw==", "signatures": [{"sig": "MEQCIHdw2LjLJZkIQcJgnVa4sRb3oL7SxIBcqZyAYnpjPo8mAiAc+qR+Vr8ZV94akljrW2KkIKUsm8es8Tv4cHma/h2nzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25781, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj56IWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrlJg//TfNvvWHP4/LMytT/qISZs5YWxjqXMu+5NnxHelCx1b1pAygQ\r\nDvY9lCKljF7ZFKel6vnmeKj3HdcuZEQI1eyhpMgE8VpKOMq05/+lQI/rPdh3\r\nkZhfEnxiMe3Yk+I+d/44T7xFfS74pafV8h/s7HDKLymdwEYOQ7dpg83asBZ5\r\niUP4nhLXdSGw0XWXpkOOSa4tGKAgnGJ0/ntqlefI+Kn5x+K/P2044mKcnUrQ\r\nCOqhQw8TBCiA0ZzQRhwt79qTEdJZsSs6cThMsyTuR4XxIIz6RD4ayXvCEwFc\r\nzT8BZ6tpHu3AhE6k5naKh77itXk8enwRK7Rc6FbO02agqy5fGUF/Ev7yJKE/\r\ninkhjOKaaKfMROORi2yomYPG6LM61XBaXR+XrifRlGV7FjQeksZScDf1IMyD\r\nIeTHS3esUx1vzm6wVV+BWK9+O9mFev/j0Yl0dP6cx6bispb0dt3ooqVNslJZ\r\ncFa+snoFhF6P5DQbo6tQdG86Zz02blj0qvEkyLGOIFhv55X6/M7tLRUJgqpl\r\nY5jSCHb0R2VX9oBmnIjhdOq6/JgRwPeZZnFm57er4s+NAeQZz2kF8Z6i6qun\r\nLuaskfI/iZW0wvSxnmVPc7a3Tz6TYSHj5Dk+aoxEz6ZUxU3eEgkF9tGe9HcL\r\ntHROxUifFT6HO1vwrt47rvL8jGYZ2hDYG1w=\r\n=o4U2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8"}, "funding": [{"url": "https://github.com/sponsors/sibir<PERSON>-s", "type": "github"}], "gitHead": "20fae89d2bdeb0e5dd70e6a9e8d2647764e6ff04", "scripts": {"test": "standard && node test.js", "prepare": "husky install", "lint:fix": "standard --fix"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>-s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.6.1", "husky": "^8.0.2", "standard": "^17.0.0", "clear-module": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_3.8.0_1676124694616_0.5742124784583424", "host": "s3://npm-registry-packages"}}, "3.9.0": {"name": "ci-info", "version": "3.9.0", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@3.9.0", "maintainers": [{"name": "watson", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>-s", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "4279a62028a7b1f262f3473fc9605f5e218c59b4", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-3.9.0.tgz", "fileCount": 7, "integrity": "sha512-NIxF55hv4nSqQswkAeiOi1r83xy8JldOFDTWiug55KBu9Jnblncd2U6ViHmYgHf01TPZS77NJBhBMKdWj9HQMQ==", "signatures": [{"sig": "MEUCIDQgGdy50aZrJRYk6ArOyP05LINUmFjcmiumovhtfQVuAiEAuYacpHYT7/YcGLYwt0OBsm++Gsrr9KKLH5TmY54k/2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26061}, "main": "index.js", "engines": {"node": ">=8"}, "funding": [{"url": "https://github.com/sponsors/sibir<PERSON>-s", "type": "github"}], "gitHead": "54e74d014ebed90aa5684c9812d8e14f49c194b0", "scripts": {"test": "standard && node test.js", "prepare": "husky install", "lint:fix": "standard --fix"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>-s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.7.0", "husky": "^8.0.3", "standard": "^17.1.0", "clear-module": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_3.9.0_1696395370039_0.7281113696240451", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "ci-info", "version": "4.0.0", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@4.0.0", "maintainers": [{"name": "watson", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>-s", "email": "<EMAIL>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "65466f8b280fc019b9f50a5388115d17a63a44f2", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-4.0.0.tgz", "fileCount": 7, "integrity": "sha512-TdHqgGf9odd8SXNuxtUBVx8Nv+qZOejE6qyqiy5NtbYYQOeFa6zmHkxlPzmaLxWWHsU6nJmB7AETdVPi+2NBUg==", "signatures": [{"sig": "MEQCIGmLkMfS/gtJvc7F47E5htrWSqIH1aeEq8P2UnriVCD3AiBaBYRsHhK0dwYnfQCBvGU24xokX5+b2Fd6NfZpGCrPSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27308}, "main": "index.js", "engines": {"node": ">=8"}, "funding": [{"url": "https://github.com/sponsors/sibir<PERSON>-s", "type": "github"}], "gitHead": "3e1488e98680f1f776785fe8708a157b7f00e568", "scripts": {"test": "standard && node test.js", "prepare": "husky install", "lint:fix": "standard --fix"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>-s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "20.9.0", "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.7.0", "husky": "^8.0.3", "standard": "^17.1.0", "clear-module": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_4.0.0_1698561683087_0.8009734209136421", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "ci-info", "version": "4.1.0", "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": {"url": "https://twitter.com/wa7son", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ci-info@4.1.0", "maintainers": [{"name": "watson", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>-s", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/sibiraj-s", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/watson/ci-info", "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "dist": {"shasum": "92319d2fa29d2620180ea5afed31f589bc98cf83", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-4.1.0.tgz", "fileCount": 7, "integrity": "sha512-HutrvTNsF48wnxkzERIXOe5/mlcfFcbfCmwcg6CJnizbSue78AbDt+1cgl26zwn61WFxhcPykPfZrbqjGmBb4A==", "signatures": [{"sig": "MEUCIQDMuUgPfl/BKzAq3owHupUw/wS/5CeDh+s2jnLbVTb2RAIgWyYDuFnElMT5Ltmo0AEQ1YmgrADuvE12rm0uG6nIl0Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28509}, "main": "index.js", "engines": {"node": ">=8"}, "funding": [{"url": "https://github.com/sponsors/sibir<PERSON>-s", "type": "github"}], "gitHead": "dd5cd6f593307b7e8d3787366cbffcb04b7accf9", "scripts": {"test": "standard && node test.js", "prepare": "husky install || true", "lint:fix": "standard --fix"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>-s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/watson/ci-info.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Get details about the current Continuous Integration environment", "directories": {}, "_nodeVersion": "22.11.0", "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.9.0", "husky": "^9.1.6", "standard": "^17.1.2", "clear-module": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/ci-info_4.1.0_1731409912870_0.8262153348150001", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "ci-info", "version": "4.2.0", "description": "Get details about the current Continuous Integration environment", "main": "index.js", "typings": "index.d.ts", "type": "commonjs", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/wa7son"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/watson/ci-info.git"}, "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "homepage": "https://github.com/watson/ci-info", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/sibiraj-s"}], "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "keywords": ["ci", "continuous", "integration", "test", "detect"], "scripts": {"lint:fix": "standard --fix", "test": "standard && node test.js", "prepare": "husky install || true"}, "devDependencies": {"clear-module": "^4.1.2", "husky": "^9.1.7", "publint": "^0.3.8", "standard": "^17.1.2", "tape": "^5.9.0"}, "engines": {"node": ">=8"}, "_id": "ci-info@4.2.0", "gitHead": "ec8a1554cfd7989b7f6a7edc2846827f9fcf3bc3", "_nodeVersion": "22.14.0", "_npmVersion": "11.2.0", "dist": {"integrity": "sha512-cYY9mypksY8NRqgDB1XD1RiJL338v/551niynFTGkZOO2LHuB2OmOYxDIe/ttN9AHwrqdum1360G3ald0W9kCg==", "shasum": "cbd21386152ebfe1d56f280a3b5feccbd96764c7", "tarball": "https://registry.npmjs.org/ci-info/-/ci-info-4.2.0.tgz", "fileCount": 7, "unpackedSize": 28914, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCt4exekrZOjcNCKhihETXCX3GTGWgJpJm5R25N/A66UwIhAMc0hduvNtZusAQkjMU8TacuhFrchwh6P0PTaIOPr9uG"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>-s", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "watson", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>-s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/ci-info_4.2.0_1741513936969_0.6705580276305272"}, "_hasShrinkwrap": false}}, "time": {"created": "2016-10-20T16:01:20.750Z", "modified": "2025-03-09T09:52:17.320Z", "1.0.0": "2016-10-20T16:01:20.750Z", "1.0.1": "2017-09-04T15:30:55.785Z", "1.1.0": "2017-09-05T11:33:34.768Z", "1.1.1": "2017-09-05T11:53:11.571Z", "1.1.2": "2017-11-17T19:11:56.371Z", "1.1.3": "2018-03-12T19:51:17.183Z", "1.2.0": "2018-08-14T08:28:13.186Z", "1.3.0": "2018-08-14T21:20:19.004Z", "1.3.1": "2018-08-16T15:47:09.554Z", "1.4.0": "2018-08-20T09:57:24.747Z", "1.5.0": "2018-09-08T23:59:16.943Z", "1.5.1": "2018-09-09T10:36:26.599Z", "1.6.0": "2018-09-20T08:37:01.113Z", "2.0.0": "2018-12-05T21:18:28.926Z", "3.0.0": "2021-02-17T16:44:14.096Z", "3.1.0": "2021-02-18T03:05:05.526Z", "3.1.1": "2021-02-18T06:13:28.091Z", "3.2.0": "2021-05-26T14:38:47.637Z", "3.3.0": "2021-11-24T03:16:04.024Z", "3.3.1": "2022-05-11T17:54:20.971Z", "3.3.2": "2022-06-15T00:47:58.515Z", "3.4.0": "2022-09-09T17:35:16.780Z", "3.5.0": "2022-10-09T01:35:44.386Z", "3.6.0": "2022-11-13T11:06:13.441Z", "3.6.1": "2022-11-13T15:28:57.544Z", "3.6.2": "2022-11-22T15:20:31.064Z", "3.7.0": "2022-11-24T14:44:08.892Z", "3.7.1": "2023-01-02T10:02:13.741Z", "3.8.0": "2023-02-11T14:11:34.779Z", "3.9.0": "2023-10-04T04:56:10.209Z", "4.0.0": "2023-10-29T06:41:23.336Z", "4.1.0": "2024-11-12T11:11:53.051Z", "4.2.0": "2025-03-09T09:52:17.153Z"}, "bugs": {"url": "https://github.com/watson/ci-info/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/wa7son"}, "license": "MIT", "homepage": "https://github.com/watson/ci-info", "keywords": ["ci", "continuous", "integration", "test", "detect"], "repository": {"type": "git", "url": "git+https://github.com/watson/ci-info.git"}, "description": "Get details about the current Continuous Integration environment", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/sibiraj-s"}], "maintainers": [{"name": "watson", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>-s", "email": "<EMAIL>"}], "readme": "# ci-info\n\nGet details about the current Continuous Integration environment.\n\nPlease [open an\nissue](https://github.com/watson/ci-info/issues/new?template=ci-server-not-detected.md)\nif your CI server isn't properly detected :)\n\n[![npm](https://img.shields.io/npm/v/ci-info.svg)](https://www.npmjs.com/package/ci-info)\n[![Tests](https://github.com/watson/ci-info/workflows/Tests/badge.svg)](https://github.com/watson/ci-info/actions)\n[![js-standard-style](https://img.shields.io/badge/code%20style-standard-brightgreen.svg?style=flat)](https://github.com/feross/standard)\n\n## Installation\n\n```bash\nnpm install ci-info --save\n```\n\n## Usage\n\n```js\nvar ci = require('ci-info')\n\nif (ci.isCI) {\n  console.log('The name of the CI server is:', ci.name)\n} else {\n  console.log('This program is not running on a CI server')\n}\n```\n\n## Supported CI tools\n\nOfficially supported CI servers:\n\n| Name                                                                            | Constant                | isPR |\n| ------------------------------------------------------------------------------- | ----------------------- | ---- |\n| [Agola CI](https://agola.io/)                                                   | `ci.AGOLA`              | ✅   |\n| [Appcircle](https://appcircle.io/)                                              | `ci.APPCIRCLE`          | ✅   |\n| [AppVeyor](http://www.appveyor.com)                                             | `ci.APPVEYOR`           | ✅   |\n| [AWS CodeBuild](https://aws.amazon.com/codebuild/)                              | `ci.CODEBUILD`          | ✅   |\n| [Azure Pipelines](https://azure.microsoft.com/en-us/services/devops/pipelines/) | `ci.AZURE_PIPELINES`    | ✅   |\n| [Bamboo](https://www.atlassian.com/software/bamboo) by Atlassian                | `ci.BAMBOO`             | 🚫   |\n| [Bitbucket Pipelines](https://bitbucket.org/product/features/pipelines)         | `ci.BITBUCKET`          | ✅   |\n| [Bitrise](https://www.bitrise.io/)                                              | `ci.BITRISE`            | ✅   |\n| [Buddy](https://buddy.works/)                                                   | `ci.BUDDY`              | ✅   |\n| [Buildkite](https://buildkite.com)                                              | `ci.BUILDKITE`          | ✅   |\n| [CircleCI](http://circleci.com)                                                 | `ci.CIRCLE`             | ✅   |\n| [Cirrus CI](https://cirrus-ci.org)                                              | `ci.CIRRUS`             | ✅   |\n| [Cloudflare Pages](https://pages.cloudflare.com/)                               | `ci.CLOUDFLARE_PAGES`   | 🚫   |\n| [Codefresh](https://codefresh.io/)                                              | `ci.CODEFRESH`          | ✅   |\n| [Codeship](https://codeship.com)                                                | `ci.CODESHIP`           | 🚫   |\n| [Drone](https://drone.io)                                                       | `ci.DRONE`              | ✅   |\n| [dsari](https://github.com/rfinnie/dsari)                                       | `ci.DSARI`              | 🚫   |\n| [Earthly CI](https://earthly.dev/)                                              | `ci.EARTHLY`            | 🚫   |\n| [Expo Application Services](https://expo.dev/eas)                               | `ci.EAS`                | 🚫   |\n| [Gerrit CI](https://www.gerritcodereview.com)                                   | `ci.GERRIT`             | 🚫   |\n| [GitHub Actions](https://github.com/features/actions/)                          | `ci.GITHUB_ACTIONS`     | ✅   |\n| [GitLab CI](https://about.gitlab.com/gitlab-ci/)                                | `ci.GITLAB`             | ✅   |\n| [Gitea Actions](https://about.gitea.com/)                                       | `ci.GITEA_ACTIONS`      | 🚫  |\n| [GoCD](https://www.go.cd/)                                                      | `ci.GOCD`               | 🚫   |\n| [Google Cloud Build](https://cloud.google.com/build)                            | `ci.GOOGLE_CLOUD_BUILD` | 🚫   |\n| [Harness CI](https://www.harness.io/products/continuous-integration)            | `ci.HARNESS`            | 🚫   |\n| [Heroku](https://www.heroku.com)                                                | `ci.HEROKU`             | 🚫   |\n| [Hudson](http://hudson-ci.org)                                                  | `ci.HUDSON`             | 🚫   |\n| [Jenkins CI](https://jenkins-ci.org)                                            | `ci.JENKINS`            | ✅   |\n| [LayerCI](https://layerci.com/)                                                 | `ci.LAYERCI`            | ✅   |\n| [Magnum CI](https://magnum-ci.com)                                              | `ci.MAGNUM`             | 🚫   |\n| [Netlify CI](https://www.netlify.com/)                                          | `ci.NETLIFY`            | ✅   |\n| [Nevercode](http://nevercode.io/)                                               | `ci.NEVERCODE`          | ✅   |\n| [Prow](https://docs.prow.k8s.io/)                                               | `ci.PROW`               | 🚫   |\n| [ReleaseHub](https://releasehub.com/)                                           | `ci.RELEASEHUB`         | 🚫   |\n| [Render](https://render.com/)                                                   | `ci.RENDER`             | ✅   |\n| [Sail CI](https://sail.ci/)                                                     | `ci.SAIL`               | ✅   |\n| [Screwdriver](https://screwdriver.cd/)                                          | `ci.SCREWDRIVER`        | ✅   |\n| [Semaphore](https://semaphoreci.com)                                            | `ci.SEMAPHORE`          | ✅   |\n| [Sourcehut](https://sourcehut.org/)                                             | `ci.SOURCEHUT`          | 🚫   |\n| [Strider CD](https://strider-cd.github.io/)                                     | `ci.STRIDER`            | 🚫   |\n| [TaskCluster](http://docs.taskcluster.net)                                      | `ci.TASKCLUSTER`        | 🚫   |\n| [TeamCity](https://www.jetbrains.com/teamcity/) by JetBrains                    | `ci.TEAMCITY`           | 🚫   |\n| [Travis CI](http://travis-ci.org)                                               | `ci.TRAVIS`             | ✅   |\n| [Vela](https://go-vela.github.io/docs/)                                         | `ci.VELA`               | ✅   |\n| [Vercel](https://vercel.com/)                                                   | `ci.VERCEL`             | ✅   |\n| [Visual Studio App Center](https://appcenter.ms/)                               | `ci.APPCENTER`          | 🚫   |\n| [Woodpecker](https://woodpecker-ci.org/)                                        | `ci.WOODPECKER`         | ✅   |\n\n## API\n\n### `ci.name`\n\nReturns a string containing name of the CI server the code is running on.\nIf CI server is not detected, it returns `null`.\n\nDon't depend on the value of this string not to change for a specific\nvendor. If you find your self writing `ci.name === 'Travis CI'`, you\nmost likely want to use `ci.TRAVIS` instead.\n\n### `ci.isCI`\n\nReturns a boolean. Will be `true` if the code is running on a CI server,\notherwise `false`.\n\nSome CI servers not listed here might still trigger the `ci.isCI`\nboolean to be set to `true` if they use certain vendor neutral\nenvironment variables. In those cases `ci.name` will be `null` and no\nvendor specific boolean will be set to `true`.\n\n### `ci.isPR`\n\nReturns a boolean if PR detection is supported for the current CI server. Will\nbe `true` if a PR is being tested, otherwise `false`. If PR detection is\nnot supported for the current CI server, the value will be `null`.\n\n### `ci.<VENDOR-CONSTANT>`\n\nA vendor specific boolean constant is exposed for each support CI\nvendor. A constant will be `true` if the code is determined to run on\nthe given CI server, otherwise `false`.\n\nExamples of vendor constants are `ci.TRAVIS` or `ci.APPVEYOR`. For a\ncomplete list, see the support table above.\n\n## Ports\n\nci-info has been ported to the following languages\n\n| Language | Repository |\n|----------|------------|\n| Go       | https://github.com/hofstadter-io/cinful |\n| Rust     | https://github.com/sagiegurari/ci_info |\n| Kotlin   | https://github.com/cloudflightio/ci-info |\n\n## License\n\n[MIT](LICENSE)\n", "readmeFilename": "README.md", "users": {"hualei": true, "vdsabev": true, "flumpus-dev": true}}