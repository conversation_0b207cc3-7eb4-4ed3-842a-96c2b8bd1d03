const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, Embed<PERSON>uilder, PermissionFlagsBits } = require('discord.js');
const { getTeamManager } = require('../../database/teamManager');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('add-to-team')
        .setDescription('Add a member to a team (Admin only)')
        .addUserOption(option =>
            option.setName('member')
                .setDescription('The member to add to the team')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('team_name')
                .setDescription('The name of the team')
                .setRequired(true)
                .setAutocomplete(true))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    async autocomplete(interaction) {
        try {
            const focusedValue = interaction.options.getFocused();
            const teamManager = getTeamManager();
            const teams = teamManager.getAllTeams();
            
            const filtered = teams.filter(team => 
                team.name.toLowerCase().includes(focusedValue.toLowerCase())
            ).slice(0, 25); // Discord limits to 25 choices

            await interaction.respond(
                filtered.map(team => ({
                    name: `${team.name} (${team.total_points} points, Level ${team.level})`,
                    value: team.name
                }))
            );
        } catch (error) {
            console.error('Error in add-to-team autocomplete:', error);
            await interaction.respond([]);
        }
    },

    async execute(interaction) {
        try {
            // Check if user has administrator permissions
            if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ Access Denied')
                    .setDescription('You need Administrator permissions to use this command.')
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            const member = interaction.options.getUser('member');
            const teamName = interaction.options.getString('team_name');
            const teamManager = getTeamManager();

            // Check if the target is a bot
            if (member.bot) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ Invalid Target')
                    .setDescription('Cannot add bots to teams.')
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Add member to team
            const result = await teamManager.addMemberToTeam(member.id, teamName, interaction.guild);

            if (!result.success) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ Failed to Add Member')
                    .setDescription(result.message)
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Get updated member and team info
            const memberInfo = await teamManager.getMemberInfo(member.id);
            const teamInfo = await teamManager.getTeamInfo(teamName);

            // Success response
            const successEmbed = new EmbedBuilder()
                .setColor('#00FF00')
                .setTitle('✅ Member Added to Team')
                .setDescription(`${member} has been successfully added to team **"${teamName}"**!`)
                .addFields(
                    { name: '👤 Member', value: `${member.tag}`, inline: true },
                    { name: '🏆 Member Points', value: `${memberInfo.points}`, inline: true },
                    { name: '⭐ Member Level', value: `${memberInfo.level}`, inline: true },
                    { name: '🏅 Team Points', value: `${teamInfo.team.total_points}`, inline: true },
                    { name: '🌟 Team Level', value: `${teamInfo.team.level}`, inline: true },
                    { name: '👥 Team Members', value: `${teamInfo.team.memberCount}`, inline: true }
                )
                .setFooter({ text: `Added by ${interaction.user.tag}` })
                .setTimestamp();

            await interaction.reply({ embeds: [successEmbed] });

            // Log the action
            console.log(`✅ ${member.tag} (${member.id}) added to team "${teamName}" by ${interaction.user.tag} (${interaction.user.id})`);

        } catch (error) {
            console.error('Error in add-to-team command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Command Error')
                .setDescription('An error occurred while adding the member to the team. Please try again later.')
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
