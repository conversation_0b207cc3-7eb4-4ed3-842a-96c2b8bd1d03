const { EmbedBuilder, ChannelType, PermissionFlagsBits, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const { getTeamManager } = require('../../database/teamManager');

// Team request ticket configuration
const TEAM_REQUEST_CONFIG = {
    categoryName: 'Team Requests',
    staffRoleNames: ['Admin', 'Moderator', 'Staff'], // Add your staff role names here
    logChannelName: 'team-logs' // Optional: channel to log team actions
};

async function createTeamTicket(interaction, requestType) {
    try {
        const guild = interaction.guild;
        const user = interaction.user;
        
        // Find or create category
        let category = guild.channels.cache.find(c => 
            c.type === ChannelType.GuildCategory && 
            c.name === TEAM_REQUEST_CONFIG.categoryName
        );
        
        if (!category) {
            category = await guild.channels.create({
                name: TEAM_REQUEST_CONFIG.categoryName,
                type: ChannelType.GuildCategory,
                permissionOverwrites: [
                    {
                        id: guild.roles.everyone,
                        deny: [PermissionFlagsBits.ViewChannel]
                    }
                ]
            });
        }

        // Find staff roles
        const staffRoles = guild.roles.cache.filter(role => 
            TEAM_REQUEST_CONFIG.staffRoleNames.some(staffRoleName => 
                role.name.toLowerCase().includes(staffRoleName.toLowerCase())
            )
        );

        // Create permission overwrites
        const permissionOverwrites = [
            {
                id: guild.roles.everyone,
                deny: [PermissionFlagsBits.ViewChannel]
            },
            {
                id: user.id,
                allow: [
                    PermissionFlagsBits.ViewChannel,
                    PermissionFlagsBits.SendMessages,
                    PermissionFlagsBits.ReadMessageHistory
                ]
            }
        ];

        // Add staff roles to permissions
        staffRoles.forEach(role => {
            permissionOverwrites.push({
                id: role.id,
                allow: [
                    PermissionFlagsBits.ViewChannel,
                    PermissionFlagsBits.SendMessages,
                    PermissionFlagsBits.ReadMessageHistory,
                    PermissionFlagsBits.ManageMessages
                ]
            });
        });

        // Create ticket channel
        const ticketChannel = await guild.channels.create({
            name: `${requestType}-${user.username}`,
            type: ChannelType.GuildText,
            parent: category.id,
            permissionOverwrites: permissionOverwrites
        });

        // Get request type details
        const requestDetails = getRequestTypeDetails(requestType);
        
        // Get user's current team info
        const teamManager = getTeamManager();
        const memberInfo = await teamManager.getMemberInfo(user.id);

        // Create ticket embed
        const ticketEmbed = new EmbedBuilder()
            .setColor(requestDetails.color)
            .setTitle(`${requestDetails.emoji} ${requestDetails.title}`)
            .setDescription(`**User:** ${user}\n**Request Type:** ${requestDetails.title}\n\n${requestDetails.description}`)
            .addFields(
                { name: '👤 Current Status', value: memberInfo.team_id ? `In team: **${memberInfo.team_name}**` : 'Not in any team', inline: true },
                { name: '🏆 Points', value: `${memberInfo.points}`, inline: true },
                { name: '⭐ Level', value: `${memberInfo.level}`, inline: true }
            )
            .setFooter({ text: `Ticket created at` })
            .setTimestamp();

        // Create close button
        const closeButton = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`close_team_ticket_${ticketChannel.id}`)
                    .setLabel('🔒 Close Ticket')
                    .setStyle(ButtonStyle.Danger)
            );

        // Send initial message
        const initialMessage = await ticketChannel.send({
            content: `${user} ${staffRoles.size > 0 ? staffRoles.map(r => `<@&${r.id}>`).join(' ') : '@Staff'}`,
            embeds: [ticketEmbed],
            components: [closeButton]
        });

        // Pin the initial message
        await initialMessage.pin();

        // Send instructions for staff
        const instructionsEmbed = new EmbedBuilder()
            .setColor('#FFA500')
            .setTitle('📋 Staff Instructions')
            .setDescription('Use the following commands to handle this request:')
            .addFields(
                { name: '🆕 Create Team', value: '`/create-team team_name:<name>`', inline: false },
                { name: '📥 Add to Team', value: '`/add-to-team member:@user team_name:<name>`', inline: false },
                { name: '📤 Remove from Team', value: '`/remove-from-team member:@user`', inline: false },
                { name: '📊 Check Info', value: '`/team-info member:@user` or `/team-info team:<name>`', inline: false }
            )
            .setFooter({ text: 'Close this ticket when the request is handled' });

        await ticketChannel.send({ embeds: [instructionsEmbed] });

        // Log the ticket creation
        await logTeamAction(guild, 'Ticket Created', `${requestDetails.title} ticket created by ${user.tag}`, user, requestDetails.color);

        return ticketChannel;

    } catch (error) {
        console.error('Error creating team ticket:', error);
        throw error;
    }
}

function getRequestTypeDetails(requestType) {
    const details = {
        'join': {
            title: 'Join Team Request',
            emoji: '📥',
            color: '#0099FF',
            description: 'Please specify which team you would like to join and why you want to join them.'
        },
        'leave': {
            title: 'Leave Team Request',
            emoji: '📤',
            color: '#FFA500',
            description: 'Please confirm that you want to leave your current team and provide a reason if you wish.'
        },
        'create': {
            title: 'Create Team Request',
            emoji: '🆕',
            color: '#00FF00',
            description: 'Please provide the name for your new team and a brief description of your team\'s goals.'
        }
    };

    return details[requestType] || details['join'];
}

async function logTeamAction(guild, action, description, user, color = '#0099FF') {
    try {
        const logChannel = guild.channels.cache.find(c => c.name === TEAM_REQUEST_CONFIG.logChannelName);
        if (!logChannel) return;

        const logEmbed = new EmbedBuilder()
            .setColor(color)
            .setTitle(`🏅 Team System - ${action}`)
            .setDescription(description)
            .addFields(
                { name: '👤 User', value: `${user.tag} (${user.id})`, inline: true },
                { name: '📅 Time', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true }
            )
            .setThumbnail(user.displayAvatarURL({ dynamic: true }))
            .setTimestamp();

        await logChannel.send({ embeds: [logEmbed] });
    } catch (error) {
        console.error('Error logging team action:', error);
    }
}

async function closeTeamTicket(interaction) {
    try {
        const channel = interaction.channel;
        const user = interaction.user;

        // Check if user has permission to close tickets
        const hasPermission = interaction.member.permissions.has(PermissionFlagsBits.ManageMessages) ||
                             TEAM_REQUEST_CONFIG.staffRoleNames.some(roleName => 
                                 interaction.member.roles.cache.some(role => 
                                     role.name.toLowerCase().includes(roleName.toLowerCase())
                                 )
                             );

        if (!hasPermission) {
            await interaction.reply({
                content: '❌ You don\'t have permission to close this ticket.',
                ephemeral: true
            });
            return;
        }

        // Create closing embed
        const closingEmbed = new EmbedBuilder()
            .setColor('#FF0000')
            .setTitle('🔒 Ticket Closing')
            .setDescription(`This ticket is being closed by ${user}`)
            .setTimestamp();

        await interaction.reply({ embeds: [closingEmbed] });

        // Log the closure
        await logTeamAction(interaction.guild, 'Ticket Closed', `Team request ticket closed by ${user.tag}`, user, '#FF0000');

        // Delete the channel after a short delay
        setTimeout(async () => {
            try {
                await channel.delete();
            } catch (error) {
                console.error('Error deleting team ticket channel:', error);
            }
        }, 5000);

    } catch (error) {
        console.error('Error closing team ticket:', error);
        await interaction.reply({
            content: '❌ An error occurred while closing the ticket.',
            ephemeral: true
        });
    }
}

module.exports = {
    name: 'interactionCreate',
    async execute(interaction, client) {
        if (!interaction.isButton()) return;

        try {
            // Handle team request buttons
            if (interaction.customId.startsWith('team_request_')) {
                const requestType = interaction.customId.replace('team_request_', '');
                
                await interaction.deferReply({ ephemeral: true });
                
                const ticketChannel = await createTeamTicket(interaction, requestType);
                
                const successEmbed = new EmbedBuilder()
                    .setColor('#00FF00')
                    .setTitle('✅ Ticket Created')
                    .setDescription(`Your team request ticket has been created: ${ticketChannel}`)
                    .setFooter({ text: 'Staff will assist you shortly' })
                    .setTimestamp();

                await interaction.editReply({ embeds: [successEmbed] });
            }
            
            // Handle ticket close buttons
            else if (interaction.customId.startsWith('close_team_ticket_')) {
                await closeTeamTicket(interaction);
            }

        } catch (error) {
            console.error('Error in team request handler:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Error')
                .setDescription('An error occurred while processing your request. Please try again later.')
                .setTimestamp();

            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
