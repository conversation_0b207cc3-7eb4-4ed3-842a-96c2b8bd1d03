const { <PERSON><PERSON><PERSON><PERSON>mandB<PERSON>er, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const { getTeamManager } = require('../../database/teamManager');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('remove-from-team')
        .setDescription('Remove a member from their current team (Admin only)')
        .addUserOption(option =>
            option.setName('member')
                .setDescription('The member to remove from their team')
                .setRequired(true))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    async execute(interaction) {
        try {
            // Check if user has administrator permissions
            if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ Access Denied')
                    .setDescription('You need Administrator permissions to use this command.')
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            const member = interaction.options.getUser('member');
            const teamManager = getTeamManager();

            // Check if the target is a bot
            if (member.bot) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ Invalid Target')
                    .setDescription('Bots are not in teams.')
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Get member info before removal
            const memberInfo = await teamManager.getMemberInfo(member.id);
            
            if (!memberInfo.team_id) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ Member Not in Team')
                    .setDescription(`${member} is not currently in any team.`)
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            const teamName = memberInfo.team_name || 'Unknown';

            // Remove member from team
            const result = await teamManager.removeMemberFromTeam(member.id, interaction.guild);

            if (!result.success) {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#FF0000')
                    .setTitle('❌ Failed to Remove Member')
                    .setDescription(result.message)
                    .setTimestamp();

                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Success response
            const successEmbed = new EmbedBuilder()
                .setColor('#FFA500')
                .setTitle('✅ Member Removed from Team')
                .setDescription(`${member} has been successfully removed from team **"${teamName}"**!`)
                .addFields(
                    { name: '👤 Member', value: `${member.tag}`, inline: true },
                    { name: '🏆 Member Points', value: `${memberInfo.points}`, inline: true },
                    { name: '⭐ Member Level', value: `${memberInfo.level}`, inline: true },
                    { name: '📋 Previous Team', value: teamName, inline: true },
                    { name: '⚠️ Note', value: 'Member will no longer earn points until they join a new team', inline: false }
                )
                .setFooter({ text: `Removed by ${interaction.user.tag}` })
                .setTimestamp();

            await interaction.reply({ embeds: [successEmbed] });

            // Log the action
            console.log(`✅ ${member.tag} (${member.id}) removed from team "${teamName}" by ${interaction.user.tag} (${interaction.user.id})`);

        } catch (error) {
            console.error('Error in remove-from-team command:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Command Error')
                .setDescription('An error occurred while removing the member from the team. Please try again later.')
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
