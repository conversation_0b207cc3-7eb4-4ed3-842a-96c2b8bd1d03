const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

class TeamDatabase {
    constructor() {
        // Ensure database directory exists
        const dbDir = path.join(__dirname, '../data');
        if (!fs.existsSync(dbDir)) {
            fs.mkdirSync(dbDir, { recursive: true });
        }

        // Initialize database
        const dbPath = path.join(dbDir, 'teams.db');
        this.db = new Database(dbPath);
        
        // Enable WAL mode for better performance
        this.db.pragma('journal_mode = WAL');
        
        this.initializeTables();
        console.log('✅ Team database initialized successfully');
    }

    initializeTables() {
        // Create members table
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS members (
                id TEXT PRIMARY KEY,
                team_id TEXT,
                points INTEGER DEFAULT 0,
                level INTEGER DEFAULT 0,
                last_daily INTEGER DEFAULT 0,
                voice_time INTEGER DEFAULT 0,
                created_at INTEGER DEFAULT (strftime('%s', 'now')),
                updated_at INTEGER DEFAULT (strftime('%s', 'now')),
                FOREIGN KEY (team_id) REFERENCES teams (id) ON DELETE SET NULL
            )
        `);

        // Create teams table
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS teams (
                id TEXT PRIMARY KEY,
                name TEXT UNIQUE NOT NULL,
                created_at INTEGER DEFAULT (strftime('%s', 'now')),
                total_points INTEGER DEFAULT 0,
                level INTEGER DEFAULT 0
            )
        `);

        // Create indexes for better performance
        this.db.exec(`
            CREATE INDEX IF NOT EXISTS idx_members_team_id ON members(team_id);
            CREATE INDEX IF NOT EXISTS idx_members_points ON members(points DESC);
            CREATE INDEX IF NOT EXISTS idx_teams_total_points ON teams(total_points DESC);
        `);

        // Create triggers to automatically update team totals and levels
        this.db.exec(`
            CREATE TRIGGER IF NOT EXISTS update_team_totals_on_member_update
            AFTER UPDATE OF points ON members
            WHEN NEW.team_id IS NOT NULL
            BEGIN
                UPDATE teams
                SET total_points = (
                    SELECT COALESCE(SUM(points), 0)
                    FROM members
                    WHERE team_id = NEW.team_id
                ),
                level = (
                    SELECT COALESCE(SUM(points), 0) / 500
                    FROM members
                    WHERE team_id = NEW.team_id
                )
                WHERE id = NEW.team_id;
            END;
        `);

        this.db.exec(`
            CREATE TRIGGER IF NOT EXISTS update_team_totals_on_member_insert
            AFTER INSERT ON members
            WHEN NEW.team_id IS NOT NULL
            BEGIN
                UPDATE teams
                SET total_points = (
                    SELECT COALESCE(SUM(points), 0)
                    FROM members
                    WHERE team_id = NEW.team_id
                ),
                level = (
                    SELECT COALESCE(SUM(points), 0) / 500
                    FROM members
                    WHERE team_id = NEW.team_id
                )
                WHERE id = NEW.team_id;
            END;
        `);

        this.db.exec(`
            CREATE TRIGGER IF NOT EXISTS update_team_totals_on_member_team_change
            AFTER UPDATE OF team_id ON members
            BEGIN
                -- Update old team totals
                UPDATE teams
                SET total_points = (
                    SELECT COALESCE(SUM(points), 0)
                    FROM members
                    WHERE team_id = OLD.team_id
                ),
                level = (
                    SELECT COALESCE(SUM(points), 0) / 500
                    FROM members
                    WHERE team_id = OLD.team_id
                )
                WHERE id = OLD.team_id AND OLD.team_id IS NOT NULL;

                -- Update new team totals
                UPDATE teams
                SET total_points = (
                    SELECT COALESCE(SUM(points), 0)
                    FROM members
                    WHERE team_id = NEW.team_id
                ),
                level = (
                    SELECT COALESCE(SUM(points), 0) / 500
                    FROM members
                    WHERE team_id = NEW.team_id
                )
                WHERE id = NEW.team_id AND NEW.team_id IS NOT NULL;
            END;
        `);

        console.log('✅ Database tables and triggers created successfully');
    }

    // Member operations
    getMember(userId) {
        const stmt = this.db.prepare('SELECT * FROM members WHERE id = ?');
        return stmt.get(userId);
    }

    createMember(userId) {
        const stmt = this.db.prepare(`
            INSERT OR IGNORE INTO members (id, points, level, last_daily, voice_time) 
            VALUES (?, 0, 0, 0, 0)
        `);
        return stmt.run(userId);
    }

    updateMemberPoints(userId, points) {
        // Ensure member exists
        this.createMember(userId);
        
        const stmt = this.db.prepare(`
            UPDATE members 
            SET points = points + ?, 
                level = (points + ?) / 100,
                updated_at = strftime('%s', 'now')
            WHERE id = ?
        `);
        return stmt.run(points, points, userId);
    }

    setMemberTeam(userId, teamId) {
        // Ensure member exists
        this.createMember(userId);
        
        const stmt = this.db.prepare(`
            UPDATE members 
            SET team_id = ?, 
                updated_at = strftime('%s', 'now')
            WHERE id = ?
        `);
        return stmt.run(teamId, userId);
    }

    removeMemberFromTeam(userId) {
        const stmt = this.db.prepare(`
            UPDATE members 
            SET team_id = NULL, 
                updated_at = strftime('%s', 'now')
            WHERE id = ?
        `);
        return stmt.run(userId);
    }

    updateLastDaily(userId, timestamp) {
        // Ensure member exists
        this.createMember(userId);
        
        const stmt = this.db.prepare(`
            UPDATE members 
            SET last_daily = ?, 
                updated_at = strftime('%s', 'now')
            WHERE id = ?
        `);
        return stmt.run(timestamp, userId);
    }

    updateVoiceTime(userId, seconds) {
        // Ensure member exists
        this.createMember(userId);
        
        const stmt = this.db.prepare(`
            UPDATE members 
            SET voice_time = voice_time + ?, 
                updated_at = strftime('%s', 'now')
            WHERE id = ?
        `);
        return stmt.run(seconds, userId);
    }

    // Team operations
    createTeam(teamId, teamName) {
        const stmt = this.db.prepare(`
            INSERT INTO teams (id, name, total_points, level) 
            VALUES (?, ?, 0, 0)
        `);
        return stmt.run(teamId, teamName);
    }

    getTeam(teamId) {
        const stmt = this.db.prepare('SELECT * FROM teams WHERE id = ?');
        return stmt.get(teamId);
    }

    getTeamByName(teamName) {
        const stmt = this.db.prepare('SELECT * FROM teams WHERE name = ?');
        return stmt.get(teamName);
    }

    deleteTeam(teamId) {
        // First remove all members from the team
        const removeMembersStmt = this.db.prepare('UPDATE members SET team_id = NULL WHERE team_id = ?');
        removeMembersStmt.run(teamId);
        
        // Then delete the team
        const deleteTeamStmt = this.db.prepare('DELETE FROM teams WHERE id = ?');
        return deleteTeamStmt.run(teamId);
    }

    getTeamMembers(teamId) {
        const stmt = this.db.prepare(`
            SELECT * FROM members 
            WHERE team_id = ? 
            ORDER BY points DESC
        `);
        return stmt.all(teamId);
    }

    // Leaderboard operations
    getTopMembers(limit = 10) {
        const stmt = this.db.prepare(`
            SELECT m.*, t.name as team_name 
            FROM members m 
            LEFT JOIN teams t ON m.team_id = t.id 
            WHERE m.team_id IS NOT NULL
            ORDER BY m.points DESC 
            LIMIT ?
        `);
        return stmt.all(limit);
    }

    getTopTeams(limit = 10) {
        const stmt = this.db.prepare(`
            SELECT * FROM teams 
            ORDER BY total_points DESC 
            LIMIT ?
        `);
        return stmt.all(limit);
    }

    // Utility methods
    getAllTeams() {
        const stmt = this.db.prepare('SELECT * FROM teams ORDER BY name');
        return stmt.all();
    }

    getMemberCount(teamId) {
        const stmt = this.db.prepare('SELECT COUNT(*) as count FROM members WHERE team_id = ?');
        return stmt.get(teamId).count;
    }

    close() {
        this.db.close();
    }
}

module.exports = TeamDatabase;
